# Luna Discord Bot - Ollama to llama.cpp Migration Guide

## Overview

This document describes the migration from Ollama API calls to direct llama.cpp Python integration to eliminate API call latency.

## Changes Made

### 1. Dependencies
- **Removed**: `openai>=1.0.0` (used for Ollama API calls)
- **Added**: `llama-cpp-python>=0.2.87` (direct llama.cpp integration)

### 2. Configuration (llm_response/config.py)
- **Removed**: `OLLAMA_URL`, `OLLAMA_API_KEY`
- **Added**: llama.cpp configuration options:
  - `LLAMA_CPP_MODEL_PATH`: Path to GGUF model file
  - `LLAMA_CPP_N_CTX`: Context length (default: 4096)
  - `LLAMA_CPP_N_THREADS`: Number of threads (-1 for auto)
  - `LLAMA_CPP_N_GPU_LAYERS`: GPU layers (-1 for auto)
  - `LLAMA_CPP_USE_MMAP`: Enable memory mapping (default: true)
  - `LLAMA_CPP_USE_MLOCK`: Enable memory locking (default: true)

### 3. Model File Requirements
- **Required**: Your model must be in GGUF format
- **Location**: Default path is `./models/luna-model.gguf`
- **System Prompt**: Now integrated directly into llama.cpp calls (extracted from luna.modelfile)

### 4. Performance Improvements
- **Eliminated**: Network API call overhead
- **Direct**: In-memory model inference
- **Expected**: 5-10x faster Time-to-First-Token (TTFT)
- **Target**: <200ms TTFT (vs previous 985ms)

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Convert Model to GGUF Format
If your model isn't already in GGUF format:
```bash
# Download your current model from Ollama
ollama show luna-tune:latest --modelfile > current_model.modelfile

# Use llama.cpp conversion tools or download a compatible GGUF model
# Place it at: ./models/luna-model.gguf
```

### 3. Environment Configuration
Create or update your `.env` file:
```env
# llama.cpp Configuration
LLAMA_CPP_MODEL_PATH=./models/luna-model.gguf
LLAMA_CPP_N_CTX=4096
LLAMA_CPP_N_THREADS=-1
LLAMA_CPP_N_GPU_LAYERS=-1
LLAMA_CPP_USE_MMAP=true
LLAMA_CPP_USE_MLOCK=true
LLAMA_CPP_VERBOSE=false
```

### 4. Test the Migration
```bash
python -c "from llm_response.initialization import initialize_llama_cpp; print('Success!' if initialize_llama_cpp() else 'Failed!')"
```

## Compatibility Notes

### Legacy Function Names
The following functions are maintained for compatibility but now use llama.cpp:
- `initialize_ollama()` → calls `initialize_llama_cpp()`
- `get_ollama_client()` → returns llama.cpp client
- `preload_ollama_model()` → checks llama.cpp model status

### System Prompt Integration
- The system prompt from `luna.modelfile` is automatically integrated
- No manual system prompt configuration needed
- Maintains Luna's personality and behavior

### Message Formatting
- Messages are automatically formatted for llama.cpp
- Maintains conversation context and history
- Preserves all existing features (RAG, memory, etc.)

## Performance Optimization

### GPU Acceleration
```env
LLAMA_CPP_N_GPU_LAYERS=35  # Adjust based on your GPU
```

### CPU Optimization
```env
LLAMA_CPP_N_THREADS=8  # Set to your CPU core count
```

### Memory Management
```env
LLAMA_CPP_USE_MMAP=true   # Faster model loading
LLAMA_CPP_USE_MLOCK=true  # Prevent swapping
```

## Troubleshooting

### Model Not Loading
- Verify GGUF model path is correct
- Check model file permissions
- Ensure sufficient RAM/VRAM

### Slow Performance
- Increase `LLAMA_CPP_N_GPU_LAYERS`
- Adjust `LLAMA_CPP_N_THREADS`
- Reduce context length if needed

### Memory Issues
- Disable `LLAMA_CPP_USE_MLOCK` if running out of RAM
- Use a smaller quantized model (Q4_K_M, Q5_K_M)

## Benchmark Tool

Run the updated TTFT optimization tool:
```bash
python optimize_ttft.py
```

This will benchmark the new llama.cpp performance and provide optimization recommendations.

## Migration Benefits

1. **Latency Reduction**: 5-10x faster response times
2. **No Network Overhead**: Direct in-memory inference
3. **Better Resource Control**: Fine-tuned CPU/GPU usage
4. **Simplified Architecture**: Fewer moving parts
5. **Maintained Compatibility**: All existing features preserved 