#!/usr/bin/env python3
"""
Memory Injection Test with LLM Initialization
Tests the complete memory system with proper LLM initialization and detailed logging
"""
import asyncio
import time
import logging
import sys
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType
from llm_response.processing import enhanced_get_memory_context, process_conversation_memory
from llm_response.initialization import initialize_all_clients

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

async def test_memory_injection_complete():
    print("🚀 Complete Memory Injection Test with LLM Initialization")
    print("=" * 80)
    
    # Step 1: Initialize LLM models (required for fact extraction)
    print("\n🔧 Initializing LLM models...")
    try:
        await initialize_all_clients()
        print("✅ LLM models initialized successfully")
    except Exception as e:
        print(f"⚠️ LLM initialization warning: {e}")
        print("📝 Continuing test - memory retrieval should still work")
    
    # Step 2: Initialize memory system
    print("\n🧠 Initializing memory system...")
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Step 3: Add comprehensive facts about <PERSON>'s preferences
    print("\n📚 Storing Luna's preference facts...")
    luna_facts = [
        MemoryFact(
            content="Luna's favorite color is purple",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,  # Gavin's ID
            channel_id="general",
            confidence=0.95,
            timestamp=time.time(),
            entities=["Luna", "purple", "color"],
            relationships=[("Luna", "prefers", "purple")]
        ),
        MemoryFact(
            content="Luna loves potatoes and considers them the best food",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="general",
            confidence=0.95,
            timestamp=time.time(),
            entities=["Luna", "potatoes", "food"],
            relationships=[("Luna", "loves", "potatoes")]
        ),
        MemoryFact(
            content="Luna's favorite Minecraft block is cobblestone",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="general",
            confidence=0.95,
            timestamp=time.time(),
            entities=["Luna", "cobblestone", "Minecraft", "block"],
            relationships=[("Luna", "prefers", "cobblestone")]
        ),
        MemoryFact(
            content="Luna also likes obsidian blocks in Minecraft for building",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="general",
            confidence=0.9,
            timestamp=time.time(),
            entities=["Luna", "obsidian", "Minecraft", "building"],
            relationships=[("Luna", "likes", "obsidian")]
        ),
        MemoryFact(
            content="Gavin created Luna as a Discord chatbot",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="general",
            confidence=0.98,
            timestamp=time.time(),
            entities=["Gavin", "Luna", "Discord", "chatbot"],
            relationships=[("Gavin", "created", "Luna")]
        )
    ]
    
    for fact in luna_facts:
        fact_id = ms.warm_storage.store_fact(fact)
        print(f"  ✅ Stored: {fact.content}")
    
    # Rebuild FTS index
    import sqlite3
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
        print("🔧 Rebuilt FTS search index")
    finally:
        conn.close()
    
    # Step 4: Test the exact conversation queries from the user
    print("\n💬 Testing Memory Injection for Gavin's Queries:")
    print("-" * 60)
    
    test_queries = [
        "Hey Luna.",
        "Luna, what's your favorite color?",
        "Luna, what's your favorite food?", 
        "Luna, what's your favorite block in Minecraft?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: Query = '{query}'")
        print(f"{'─' * 50}")
        
        # Test memory retrieval with detailed logging
        memory_start = time.time()
        
        # Enable debug logging for this call
        logger = logging.getLogger('llm_response.processing')
        logger.setLevel(logging.DEBUG)
        
        memory_context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=12345,
            current_channel_id=12345,
            is_dm_channel=False
        )
        
        memory_time = (time.time() - memory_start) * 1000
        
        print(f"⏱️  Memory retrieval time: {memory_time:.2f}ms")
        
        if memory_context and memory_context.strip():
            print(f"✅ Memory Context Found ({len(memory_context)} chars):")
            print(f"📝 Context Preview:")
            for line_num, line in enumerate(memory_context.split('\n')[:5], 1):
                if line.strip():
                    print(f"   {line_num}. {line.strip()}")
            
            # Show what would be injected into the prompt
            print(f"\n🎯 PROMPT INJECTION:")
            print(f"   Query: '{query}'")
            print(f"   Memory Context Added: {len(memory_context)} characters")
            print(f"   This context would be added to Luna's system prompt")
            
        else:
            print(f"❌ No Memory Context Retrieved")
            
            # Debug why no context was found
            print(f"🔍 Debugging search failure:")
            
            # Test raw search terms
            import re
            words = re.findall(r'\b\w+\b', query.lower())
            stop_words = {'what', 'is', 'are', 'do', 'you', 'know', 'about', 'the', 'a', 'an', 'your', 'my', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
            key_terms = [word for word in words if word not in stop_words and len(word) > 2]
            search_query = ' '.join(key_terms[:3]) if key_terms else query
            
            print(f"   Parsed terms: {key_terms}")
            print(f"   Search query: '{search_query}'")
            
            # Test direct database search
            facts = ms.warm_storage.search_facts(query=search_query, user_id=12345, limit=5)
            print(f"   Direct DB search found: {len(facts)} facts")
            for fact in facts[:2]:
                print(f"     - {fact.content[:80]}...")
    
    # Step 5: Test conversation processing with fact extraction
    print(f"\n🔄 Testing Background Conversation Processing:")
    print("-" * 60)
    
    conversation_messages = [
        {"role": "user", "content": "Gavin: Hey Luna.", "user_id": 12345},
        {"role": "assistant", "content": "Ugh, fine. What is it?", "user_id": 67890},
        {"role": "user", "content": "Gavin: Luna, what's your favorite color?", "user_id": 12345},
        {"role": "assistant", "content": "Purple. Obviously. Don't @ me about it.", "user_id": 67890},
        {"role": "user", "content": "Gavin: Luna, what's your favorite food?", "user_id": 12345},
        {"role": "assistant", "content": "Potatoes. Duh. Seriously, you're testing me.", "user_id": 67890},
        {"role": "user", "content": "Gavin: Luna, what's your favorite block in Minecraft?", "user_id": 12345},
        {"role": "assistant", "content": "Obsidian. It's strong and looks cool.", "user_id": 67890}
    ]
    
    print("📝 Processing conversation for fact extraction...")
    await process_conversation_memory(
        messages=conversation_messages,
        user_id=12345,
        channel_id="general",
        channel_type="text"
    )
    
    # Give background processor time
    print("⏳ Waiting for background processing...")
    await asyncio.sleep(2.0)
    
    # Final summary
    print(f"\n📊 MEMORY SYSTEM STATUS:")
    print("=" * 80)
    retrieval_times = ms.metrics.get('retrieval_time', [])
    extraction_times = ms.metrics.get('extraction_time', [])
    
    if retrieval_times:
        avg_retrieval = sum(retrieval_times) / len(retrieval_times)
        print(f"⚡ Average Retrieval Time: {avg_retrieval:.2f}ms")
        print(f"🎯 Target: <5ms ({'✅ ACHIEVED' if avg_retrieval < 5 else '❌ MISSED'})")
    
    print(f"🔍 Total retrievals: {len(retrieval_times)}")
    print(f"🧠 Background extractions: {len(extraction_times)}")
    
    print(f"\n✨ Memory injection test complete!")
    print(f"🔗 Memory context should now be visible in Luna's responses")

if __name__ == "__main__":
    asyncio.run(test_memory_injection_complete())