#!/usr/bin/env python3
"""
Test Person Scenarios
=====================
Final test of the exact scenarios from user logs
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def test_person_scenarios():
    print("👥 Testing Real Person Query Scenarios")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    
    # User ID from logs
    user_id = 921637353364287489
    
    # Test the exact scenarios from the user's logs
    scenarios = [
        ("<PERSON> what do you know about <PERSON>?", "Should return no memory (Mari unknown)"),
        ("<PERSON>, what do you know about <PERSON>?", "Should return no memory (<PERSON> unknown)"),
        ("<PERSON>, what do you know about <PERSON>?", "Should return Gavin facts (creator info)"),
        ("<PERSON>, what's your favorite color?", "Should return <PERSON>'s color preference"),
    ]
    
    for i, (query, expected) in enumerate(scenarios, 1):
        print(f"\n{i}. 🔍 Query: '{query}'")
        print(f"   Expected: {expected}")
        
        try:
            # Test through the exact same function used in logs
            context = await enhanced_get_memory_context(
                query_text=query,
                current_user_id=user_id,
                current_channel_id=12345,
                is_dm_channel=False
            )
            
            if context:
                print(f"   Result: ✅ Memory found ({len(context)} chars)")
                
                # Show what was found
                lines = [line.strip() for line in context.split('\n') if line.strip()]
                first_fact = lines[0] if lines else "No facts"
                print(f"   First fact: {first_fact}")
                
                # Check if it's relevant
                query_lower = query.lower()
                context_lower = context.lower()
                
                is_correct = False
                if 'mari' in query_lower:
                    if 'mari' not in context_lower:
                        print(f"   ❌ Wrong: About Mari but no Mari facts found")
                    else:
                        print(f"   ✅ Correct: Found Mari facts")
                        is_correct = True
                elif 'ethan' in query_lower:
                    if 'ethan' not in context_lower:
                        print(f"   ❌ Wrong: About Ethan but no Ethan facts found")
                    else:
                        print(f"   ✅ Correct: Found Ethan facts")
                        is_correct = True
                elif 'gavin' in query_lower:
                    if 'gavin' in context_lower:
                        print(f"   ✅ Correct: Found Gavin facts")
                        is_correct = True
                    else:
                        print(f"   ❌ Wrong: About Gavin but no Gavin facts found")
                elif 'color' in query_lower:
                    if 'purple' in context_lower or 'color' in context_lower:
                        print(f"   ✅ Correct: Found color facts")
                        is_correct = True
                    else:
                        print(f"   ❌ Wrong: About color but no color facts found")
                
                if not is_correct and 'mari' not in query_lower and 'ethan' not in query_lower:
                    print(f"   ❓ Unclear relevance")
                    
            else:
                print(f"   Result: ❌ No memory found")
                if 'mari' in query.lower() or 'ethan' in query.lower():
                    print(f"   ✅ Correct: No facts about unknown people")
                else:
                    print(f"   ❌ Wrong: Should have found facts")
                    
        except Exception as e:
            print(f"   Error: {e}")
    
    print(f"\n🎯 Summary:")
    print(f"   The memory system should now correctly handle:")
    print(f"   ✅ Unknown people → No memory injection")
    print(f"   ✅ Known people → Relevant facts")
    print(f"   ✅ Luna queries → Luna's facts")
    print(f"   This prevents misleading responses!")

if __name__ == "__main__":
    asyncio.run(test_person_scenarios())