# VTube Studio Integration Setup Guide for <PERSON>

This guide will help you set up <PERSON>'s VTube Studio integration to give her a Live2D avatar with lip-sync and automatic expressions.

## Overview

Luna's VTube Studio integration provides:
- **Automatic lip-sync** using local TTS output to a Virtual Audio Cable
- **Intelligent emotion detection** that triggers VTube Studio expressions based on <PERSON>'s speech content
- **Real-time expression changes** as <PERSON> responds to conversations
- **Non-blocking operation** that doesn't interfere with Discord functionality

## Prerequisites

1. **VTube Studio** installed and running
2. **VB-CABLE Virtual Audio Cable** (or similar virtual audio solution)
3. **OBS Studio** (for streaming the avatar to Discord)
4. **A separate Discord account** for the avatar (optional but recommended)

## Step 1: Install Required Dependencies

The setup script should have already added these to `requirements.txt`, but verify they're installed:

```bash
pip install websockets>=11.0.0
pip install pyttsx3>=2.90
```

## Step 2: Install and Configure VB-CABLE

1. Download VB-CABLE from: https://vb-audio.com/Cable/
2. Install VB-CABLE (requires administrator privileges)
3. Restart your computer after installation
4. Verify "CABLE Input" appears in your audio devices

## Step 3: Configure VTube Studio

### Enable API Access
1. Open VTube Studio
2. Go to Settings → General
3. Enable "API" checkbox
4. Set API Port to `8001` (default)
5. Apply settings

### Create Expression Hotkeys
You need to create hotkeys for each emotion Luna can express. Go to Settings → Hotkeys and create the following:

**Important**: After creating each hotkey, you need to copy its exact Hotkey ID and update the `vtube_studio_emotions.py` file.

#### Required Hotkeys:
- **Happy Expression**: Create a hotkey that triggers a happy/smiling expression
- **Sad Expression**: Create a hotkey that triggers a sad/disappointed expression  
- **Thinking Expression**: Create a hotkey that triggers a thoughtful/curious expression
- **Surprised Expression**: Create a hotkey that triggers a surprised/shocked expression
- **Angry Expression**: Create a hotkey that triggers an angry/frustrated expression
- **Confused Expression**: Create a hotkey that triggers a confused/puzzled expression

### Configure Microphone Input
1. In VTube Studio, go to Settings → Microphone
2. Set the microphone input to "CABLE Output" (this receives audio from Luna's TTS)
3. Adjust sensitivity as needed for lip-sync

## Step 4: Update Hotkey IDs in Luna's Code

After creating the hotkeys in VTube Studio:

1. Open `vtube_studio_emotions.py`
2. Find the emotion patterns section (around line 30)
3. Replace the placeholder hotkey IDs with your actual VTube Studio hotkey IDs:

```python
# Example - replace these with your actual hotkey IDs:
"happy": {
    "hotkey_id": "YOUR_ACTUAL_HAPPY_HOTKEY_ID_HERE",  # Replace this
    # ... rest of config
},
"sad": {
    "hotkey_id": "YOUR_ACTUAL_SAD_HOTKEY_ID_HERE",    # Replace this
    # ... rest of config
},
# ... continue for all emotions
```

**To find your hotkey IDs:**
1. In VTube Studio, right-click on a hotkey
2. Select "Copy Hotkey ID"
3. Paste the ID into the appropriate place in the code

## Step 5: Configure Luna's Environment

Add these environment variables to your `local.env` file:

```env
# VTube Studio Integration
VTUBE_STUDIO_ENABLED=true
VTUBE_STUDIO_URL=ws://127.0.0.1:8001
VTUBE_STUDIO_AUDIO_DEVICE=CABLE Input
```

## Step 6: Set Up Audio Routing

### Option A: Set VB-CABLE as Default (Recommended)
1. Right-click the speaker icon in system tray
2. Select "Playback devices"
3. Set "CABLE Input" as the default playback device
4. This ensures Luna's local TTS goes to VTube Studio

### Option B: Manual Audio Routing
If you can't change the default device:
1. Use VB-CABLE's audio routing features
2. Route system audio or specific app audio to CABLE Input
3. Ensure Luna's TTS reaches VTube Studio's microphone input

## Step 7: Configure OBS for Discord Streaming

1. **Create OBS Scene**:
   - Add "Window Capture" source
   - Select VTube Studio window
   - Position and scale as desired

2. **Set Up Virtual Camera**:
   - In OBS, click "Start Virtual Camera"
   - Choose "OBS Virtual Camera" as output

3. **Configure Discord Account**:
   - Use a separate Discord account for Luna's avatar (recommended)
   - In Discord voice settings, set camera to "OBS Virtual Camera"
   - **Important**: Mute the microphone for this account to avoid audio loops

## Step 8: Test the Integration

1. **Start Luna**: Run your bot as normal
2. **Check Connection**: Look for VTube Studio connection messages in logs:
   ```
   ✅ Successfully connected to VTube Studio WebSocket
   ✅ VTube Studio authentication successful
   ```
3. **Test Expressions**: Have Luna respond with emotional content and watch for expressions
4. **Test Lip-Sync**: Luna's responses should trigger lip movement in VTube Studio

## Troubleshooting

### VTube Studio Connection Issues
- Ensure VTube Studio is running and API is enabled
- Check that port 8001 is not blocked by firewall
- Verify WebSocket URL in configuration

### Audio Issues
- Confirm VB-CABLE is properly installed
- Check that VTube Studio is receiving audio on "CABLE Output"
- Verify Luna's TTS is outputting to "CABLE Input"

### Authentication Issues
- On first connection, VTube Studio may ask to approve the plugin
- Click "Allow" when prompted
- Check logs for authentication error details

### Expression Issues
- Verify hotkey IDs are correctly copied from VTube Studio
- Check emotion detection logs to see if emotions are being detected
- Test hotkeys manually in VTube Studio first

## Configuration Options

### Disable VTube Studio Integration
Set in `local.env`:
```env
VTUBE_STUDIO_ENABLED=false
```

### Change Audio Device
If using a different virtual audio cable:
```env
VTUBE_STUDIO_AUDIO_DEVICE=Your_Audio_Device_Name
```

### Adjust Emotion Detection
Modify `vtube_studio_emotions.py` to:
- Add new keywords for emotions
- Adjust confidence thresholds
- Add new emotion types

## Advanced Configuration

### Adding New Emotions
1. Create new hotkey in VTube Studio
2. Add emotion pattern to `vtube_studio_emotions.py`
3. Include keywords and patterns for detection

### Customizing TTS Voice
Modify `vtube_studio_tts.py` to change:
- Speech rate
- Voice selection
- Volume levels

## Logs and Debugging

Luna will log VTube Studio operations with emojis for easy identification:
- 🎭 Emotion detection and expression triggers
- 🔊 Local TTS operations
- ✅ Successful operations
- ❌ Errors and failures

Check the logs if something isn't working as expected.

## Final Notes

- The VTube Studio integration runs concurrently and won't block Discord responses
- All operations are designed to fail gracefully without breaking Luna's core functionality
- The system automatically handles reconnections and authentication
- Expression detection uses intelligent context awareness to prevent rapid emotion switching

Enjoy Luna's new Live2D avatar! 🎭✨ 