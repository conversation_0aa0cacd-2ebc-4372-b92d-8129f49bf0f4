"""
Simple import test for Luna Memory System
"""

def test_imports():
    """Test that all memory system components can be imported"""
    print("Testing memory system imports...")
    
    try:
        # Test core memory system
        from llm_response.memory_system import MemoryType, MemoryFact, HotCache
        print("✅ Core memory system imports successful")
        
        # Test memory types
        factual = MemoryType.FACTUAL
        behavioral = MemoryType.BEHAVIORAL
        relationship = MemoryType.RELATIONSHIP
        print("✅ Memory types working")
        
        # Test hot cache
        cache = HotCache(max_size=10, ttl_seconds=60)
        cache.put("test", "value")
        result = cache.get("test")
        assert result == "value"
        print("✅ Hot cache working")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 Luna Memory System imports are working!")
        print("✅ The new memory system is ready for integration")
    else:
        print("\n⚠️ Import issues detected - please check syntax errors")