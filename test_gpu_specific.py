#!/usr/bin/env python3
"""
Specific test to force models to specific GPUs
"""

import os
import sys
import logging
from load_environment import load_env_vars

# Load environment variables first
load_env_vars()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_force_amd_gpu():
    """Test forcing a model to AMD GPU only"""
    logger.info("=" * 60)
    logger.info("TESTING FORCED AMD GPU ALLOCATION")
    logger.info("=" * 60)
    
    try:
        from llama_cpp import Llama
        
        # Force environment to only see AMD GPU
        os.environ["GGML_BACKEND"] = "vulkan"
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
        os.environ["GGML_CUDA"] = "0"
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "1"  # Only AMD GPU
        
        logger.info(f"Environment: GGML_BACKEND={os.environ.get('GGML_BACKEND')}")
        logger.info(f"Environment: GGML_VK_VISIBLE_DEVICES={os.environ.get('GGML_VK_VISIBLE_DEVICES')}")
        logger.info(f"Environment: CUDA_VISIBLE_DEVICES={os.environ.get('CUDA_VISIBLE_DEVICES')}")
        
        # Load model with only AMD GPU visible
        model = Llama(
            model_path="C:/Users/<USER>/.lmstudio/models/lmstudio-community/gemma-3-4B-it-qat-GGUF/gemma-3-4B-it-QAT-Q4_0.gguf",
            n_ctx=2048,
            n_gpu_layers=-1,
            main_gpu=0,  # Should be AMD GPU since it's the only one visible
            verbose=True
        )
        
        logger.info("✅ Model loaded successfully on AMD GPU only")
        
        # Test generation
        response = model("Hello", max_tokens=3, temperature=0.1)
        logger.info(f"Test response: {response['choices'][0]['text']}")
        
        # Clean up
        del model
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")

def test_force_nvidia_gpu():
    """Test forcing a model to NVIDIA GPU only"""
    logger.info("=" * 60)
    logger.info("TESTING FORCED NVIDIA GPU ALLOCATION")
    logger.info("=" * 60)
    
    try:
        from llama_cpp import Llama
        
        # Force environment to only see NVIDIA GPU
        os.environ["GGML_BACKEND"] = "vulkan"
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
        os.environ["GGML_CUDA"] = "0"
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0"  # Only NVIDIA GPU
        
        logger.info(f"Environment: GGML_BACKEND={os.environ.get('GGML_BACKEND')}")
        logger.info(f"Environment: GGML_VK_VISIBLE_DEVICES={os.environ.get('GGML_VK_VISIBLE_DEVICES')}")
        logger.info(f"Environment: CUDA_VISIBLE_DEVICES={os.environ.get('CUDA_VISIBLE_DEVICES')}")
        
        # Load model with only NVIDIA GPU visible
        model = Llama(
            model_path="./models/luna-model.gguf",
            n_ctx=2048,
            n_gpu_layers=35,
            main_gpu=0,  # Should be NVIDIA GPU since it's the only one visible
            verbose=True
        )
        
        logger.info("✅ Model loaded successfully on NVIDIA GPU only")
        
        # Test generation
        response = model("Hello", max_tokens=3, temperature=0.1)
        logger.info(f"Test response: {response['choices'][0]['text']}")
        
        # Clean up
        del model
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")

def main():
    """Main test function"""
    logger.info("Starting GPU-specific allocation test")
    
    # Test AMD GPU only
    test_force_amd_gpu()
    
    # Reset environment
    if "GGML_VK_VISIBLE_DEVICES" in os.environ:
        del os.environ["GGML_VK_VISIBLE_DEVICES"]
    
    # Test NVIDIA GPU only  
    test_force_nvidia_gpu()
    
    logger.info("=" * 60)
    logger.info("GPU-SPECIFIC TEST COMPLETE")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
