import os
import requests
import json
import discord # Added for Discord operations
import asyncio # Added for potential async operations within helper
import re # Added for parsing LLM decisions
from dotenv import load_dotenv
import threading
from llama_cpp import Llama

# Load environment variables
import time # Add time import for logging
import logging # Add logging import

# Import necessary components from llm_response package
from llm_response.commands import generate_call_message
from llm_response.config import (
    CALL_NOTIFICATION_CHANNEL_ID_STR,
    get_id_from_alias,
    get_main_name_by_id,
    GEMMA_CPP_MODEL_PATH,  # This is actually Qwen3
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
    LLAMA_CPP_USE_MMAP,
    LLAMA_CPP_USE_MLOCK
)

# Import latency tracking
try:
    from llm_response.latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

# Load environment variables
load_dotenv('local.env')

# Setup logger
logger = logging.getLogger(__name__)
# Basic logging configuration (can be refined in main app setup)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Configuration (Consider moving to a dedicated config file later)
LM_STUDIO_URL = os.getenv("LM_STUDIO_API_URL", "http://127.0.0.1:1234/v1") # Default URL if not set in .env
MODEL_NAME = "gemma-3-4b-it-qat" # As specified
TEMPERATURE = 0.4 # Adjust as needed

# Cache for the system prompt
_llm_brain_system_prompt = None

# Use shared model system for Qwen3 on AMD 6650XT
def get_qwen3_brain_client():
    """Get the Qwen3 client for brain operations on AMD 6650XT."""
    from llm_response.initialization import get_qwen3_client
    return get_qwen3_client()

# Legacy compatibility
_gemma_cpp_client = None
_gemma_cpp_lock = threading.Lock()
_gemma_inference_lock = asyncio.Lock()  # Async lock for inference

def get_gemma_cpp_client():
    """Legacy function - now returns Qwen3 client on AMD 6650XT."""
    return get_qwen3_brain_client()

def _load_brain_system_prompt():
    """Loads the system prompt from llm_brain_prompt.txt"""
    global _llm_brain_system_prompt
    if _llm_brain_system_prompt is None:
        try:
            with open('llm_brain_prompt.txt', 'r', encoding='utf-8') as f:
                _llm_brain_system_prompt = f.read()
            logger.info("LLM Brain system prompt loaded successfully.")
        except FileNotFoundError:
            logger.error("llm_brain_prompt.txt not found. Using default fallback.")
            _llm_brain_system_prompt = "You are Luna's decision-making core. Decide the action: Disconnect User, DM User, Call User, or No Action. "
        except Exception as e:
            logger.error(f"Error loading llm_brain_prompt.txt: {e}. Using default fallback.")
            _llm_brain_system_prompt = "You are Luna's decision-making core. Decide the action: Disconnect User, DM User, Call User, or No Action. "
    return _llm_brain_system_prompt

async def get_brain_decision(prompt: str) -> str: # Converted to async
    # Prepare the system and user prompt using Qwen3 chat format
    system_prompt_content = _load_brain_system_prompt()
    full_prompt = f"<|im_start|>system\n{system_prompt_content}<|im_end|>\n<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\nAction:"

    # Use thread-safe wrapper to prevent concurrent access issues
    mark_latency_timestamp("brain_api_start")
    
    try:
        # Use Qwen3-specific thread-safe model access on AMD 6650XT
        from shared_model import call_qwen3_safe
        
        model = get_qwen3_brain_client()
        
        loop = asyncio.get_event_loop()
        def run_inference():
            return call_qwen3_safe(
                model,
                full_prompt,
                max_tokens=50,  # Increased to handle longer DM messages and Call actions
                temperature=0.1,  # Lower temperature for more consistent output
                top_p=0.9,
                stop=["<|im_end|>", "\n\n\n", "USER:", "ASSISTANT:"],  # Removed aggressive stops that cut off responses
                stream=False
            )

        # Add timeout protection for AMD GPU conflicts
        try:
            result = await asyncio.wait_for(
                loop.run_in_executor(None, run_inference),
                timeout=15.0  # 15 second timeout for brain decisions
            )
        except asyncio.TimeoutError:
            logger.error("Brain decision timed out - AMD GPU may be busy with decision/memory systems")
            return "Action: None"
        
    except Exception as e:
        logger.error(f"Error in brain decision (AMD GPU conflict?): {e}")
        # Force cleanup on AMD GPU error
        try:
            import gc
            gc.collect()
        except:
            pass
        return "Action: None"
    
    mark_latency_timestamp("brain_api_end")

    # Extract decision text from thread-safe result
    if isinstance(result, dict):
        decision = result.get('choices', [{}])[0].get('text', '').strip()
    else:
        decision = str(result).strip()
    
    logger.info(f"LLM Brain Decision (Qwen3 on AMD 6650XT): {decision}")
    return decision


# --- Disconnect User Execution Logic ---
async def _execute_disconnect_user(*, bot, sink, target_name: str, effective_text_channel):
    """
    Finds a user by name and disconnects them from their voice channel.
    """
    logger.info(f"Brain executing disconnect for target: {target_name}")
    target_member = None
    guild = sink.voice_client.guild if sink and sink.voice_client else None

    if not guild:
        logger.error("Cannot execute disconnect: No guild context available.")
        if effective_text_channel:
            await effective_text_channel.send(f"Sorry, I can't disconnect {target_name} right now (missing server context).")
        return False # Indicate failure

    # Import the utility function
    from utils import find_user_from_identifier

    try:
        # Use the utility function to find the user
        target_user = await find_user_from_identifier(
            identifier=target_name,
            bot=bot,
            guild=guild
        )

        # If we found a user but it's not a Member, try to get the Member from the guild
        if target_user and not isinstance(target_user, discord.Member):
            try:
                target_member = await guild.fetch_member(target_user.id)
            except (discord.NotFound, discord.HTTPException) as e:
                logger.warning(f"Found user {target_user.name} ({target_user.id}) but couldn't fetch as member: {e}")
                target_member = None
        elif isinstance(target_user, discord.Member):
            target_member = target_user

    except Exception as e:
        logger.error(f"Error finding user '{target_name}' for disconnect: {e}")
        if effective_text_channel:
            await effective_text_channel.send(f"❌ Error finding user '{target_name}' to disconnect.")
        return False

    # Execute the disconnect
    if target_member:
        if target_member.voice and target_member.voice.channel:
            try:
                await target_member.move_to(None, reason="Action requested by LLM Brain")
                if effective_text_channel:
                    await effective_text_channel.send(f"✅ Disconnected {target_member.mention} as requested.")
                logger.info(f"Successfully disconnected {target_member.display_name} ({target_member.id})")
                return True # Indicate success
            except discord.Forbidden:
                logger.warning(f"Missing permissions to disconnect {target_member.display_name}")
                if effective_text_channel:
                    await effective_text_channel.send(f"⚠️ I don't have permission to disconnect {target_member.mention}.")
                return False
            except discord.HTTPException as e:
                logger.error(f"Failed to disconnect {target_member.display_name}: {e}")
                if effective_text_channel:
                    await effective_text_channel.send(f"❌ Failed to disconnect {target_member.mention}.")
                return False
        else:
            logger.info(f"User {target_member.display_name} ({target_member.id}) not in a voice channel to disconnect.")
            if effective_text_channel:
                await effective_text_channel.send(f"ℹ️ {target_member.mention} isn't in a voice channel.")
            return False # User not in voice channel
    else:
        logger.warning(f"Could not find user matching '{target_name}' to disconnect.")
        if effective_text_channel:
            await effective_text_channel.send(f"❓ Couldn't find user matching '{target_name}' to disconnect.")
        return False


# --- Call User Execution Logic (Moved from processing.py) ---
async def _execute_call_user(*, bot, sink, target_name: str, system_prompt: str, effective_text_channel):
    """
    Finds a user by name, generates a call message, and sends notifications.
    """
    logger.info(f"Brain executing call for target: {target_name}")
    target_member = None
    target_user_id = None # To store ID if found via alias
    search_method = "unknown"
    guild = sink.voice_client.guild if sink and sink.voice_client else None

    if not guild:
        logger.error("Cannot execute call: No guild context available.")
        if effective_text_channel: await effective_text_channel.send(f"Sorry, I can't call {target_name} right now (missing server context).")
        return False # Indicate failure

    target_name_lower = target_name.lower()

    # --- User Finding Logic (Prioritize Alias Lookup) ---

    # 1. Check Alias Dictionary First
    target_user_id = get_id_from_alias(target_name_lower)
    if target_user_id:
        logger.info(f"Found alias '{target_name_lower}' mapping to ID: {target_user_id}")
        # Try cache first
        target_member = guild.get_member(target_user_id)
        if target_member:
            search_method = "alias_cache"
            logger.info(f"Found member {target_member.display_name} ({target_user_id}) in cache via alias.")
        else:
            # If not in cache, try fetching by ID
            logger.info(f"Member {target_user_id} (from alias) not in cache, attempting fetch by ID...")
            try:
                target_member = await guild.fetch_member(target_user_id)
                search_method = "alias_fetch"
                logger.info(f"Successfully fetched member {target_member.display_name} ({target_user_id}) via alias ID.")
            except discord.NotFound:
                logger.warning(f"Member with ID {target_user_id} (from alias) not found in guild.")
            except discord.Forbidden:
                logger.warning(f"Bot lacks permission to fetch member by ID {target_user_id}.")
            except Exception as e:
                logger.error(f"Error fetching member by ID {target_user_id}: {e}")
                target_member = None # Ensure member is None on error

    # --- Fallback to Name Search if Alias Lookup Failed ---
    if not target_member:
        logger.info(f"No member found via alias '{target_name_lower}', falling back to name search...")

        # 2. Check current voice channel members (if applicable)
        if sink and sink.voice_client and sink.voice_client.channel:
            vc_members = sink.voice_client.channel.members
            target_member = discord.utils.find(lambda m: m.display_name.lower() == target_name_lower or m.name.lower() == target_name_lower, vc_members)
            if target_member: search_method = "vc_current"

        # 3. Check guild members cache (display name first, then username)
        if not target_member:
            target_member = discord.utils.find(lambda m: m.display_name.lower() == target_name_lower, guild.members)
            if target_member: search_method = "cache_display"
        if not target_member:
            target_member = discord.utils.find(lambda m: m.name.lower() == target_name_lower, guild.members)
            if target_member: search_method = "cache_username"

        # 4. Fetch all members if still not found (last resort for names)
        if not target_member:
            logger.info(f"Target '{target_name}' not found in cache, attempting fetch all...")
            try:
                # Consider limiting fetch if guild is huge, but flatten() implies fetching all anyway
                fetched_members = await guild.fetch_members(limit=None).flatten()
                # Search fetched list by display name
                target_member = discord.utils.find(lambda m: m.display_name.lower() == target_name_lower, fetched_members)
                if target_member: search_method = "fetch_display"
                # Search fetched list by username if still not found
                if not target_member:
                    target_member = discord.utils.find(lambda m: m.name.lower() == target_name_lower, fetched_members)
                    if target_member: search_method = "fetch_username"
                if target_member: logger.info(f"Found member '{target_name}' via fetch.")
            except discord.Forbidden: logger.warning("Failed to fetch members: Bot lacks necessary permissions (Server Members Intent likely missing).")
            except Exception as e: logger.error(f"Error fetching members: {e}")

    # Log final result of finding user
    if target_member: logger.info(f"Successfully identified target_member '{target_member.display_name}' (ID: {target_member.id}) using method: {search_method}")
    else:
        logger.warning(f"Failed to identify target_member for '{target_name}' after all search methods.")
        if effective_text_channel: await effective_text_channel.send(f"Sorry, I couldn't find anyone named '{target_name}' to call.")
        return False # Indicate failure

    # --- Notification Logic (Copied from processing.py) ---
    notification_channel = None
    if CALL_NOTIFICATION_CHANNEL_ID_STR:
        try:
            notification_channel_id = int(CALL_NOTIFICATION_CHANNEL_ID_STR)
            notification_channel = bot.get_channel(notification_channel_id)
            if not notification_channel: logger.warning(f"Notification channel ID {notification_channel_id} not found.")
        except ValueError: logger.error(f"Invalid CALL_NOTIFICATION_CHANNEL_ID: '{CALL_NOTIFICATION_CHANNEL_ID_STR}'. Must be an integer.")
        except Exception as e: logger.error(f"Error getting notification channel {CALL_NOTIFICATION_CHANNEL_ID_STR}: {e}")
    else: logger.warning("CALL_NOTIFICATION_CHANNEL_ID environment variable not set.")

    if not notification_channel:
         logger.warning("Cannot send call notification: Notification channel not found or configured.")
         # Optionally inform user if possible
         if effective_text_channel: await effective_text_channel.send(f"I found {target_name}, but I don't know where to send the call notification.")
         # Proceed to DM attempt? Or fail here? Let's try DM anyway.

    luna_voice_channel = sink.voice_client.channel if sink and sink.voice_client else None
    if not luna_voice_channel:
        logger.error("Cannot execute call: Luna is not in a voice channel.")
        if effective_text_channel: await effective_text_channel.send(f"Sorry, I can't call {target_name} because I'm not in a voice channel myself.")
        return False # Indicate failure

    # Generate message and send notifications
    try:
        call_body = await generate_call_message(target_name, system_prompt)
        message_to_send = f"{target_member.mention} {call_body} Join {luna_voice_channel.mention}"

        # Send to notification channel if found
        if notification_channel:
            try:
                await notification_channel.send(message_to_send)
                logger.info(f"Sent call notification for {target_name} to channel {notification_channel.name}")
            except (discord.Forbidden, discord.HTTPException) as send_error:
                 logger.error(f"Failed to send call notification to channel #{notification_channel.name}: {send_error}")
                 if effective_text_channel: await effective_text_channel.send(f"(Couldn't send notification to #{notification_channel.name}.)")

        # Attempt DM
        dm_success = False
        try:
            await target_member.send(message_to_send)
            logger.info(f"Sent call notification DM to {target_name}")
            dm_success = True
        except (discord.Forbidden, discord.HTTPException) as dm_error:
            logger.warning(f"Could not send DM to {target_name}: {dm_error}")
            if effective_text_channel: await effective_text_channel.send(f"(Couldn't send a DM to {target_name}, they might have them disabled.)")

        # Confirmation in original channel
        if effective_text_channel:
             if notification_channel or dm_success:
                  await effective_text_channel.send(f"Okay, I'm calling {target_name}.")
             else: # Neither notification channel nor DM worked
                  await effective_text_channel.send(f"I found {target_name}, but couldn't send the notification anywhere.")
                  return False # Indicate overall failure if no notification sent

        return True # Indicate success

    except Exception as e:
        logger.error(f"Unexpected error during call execution for {target_name}: {e}", exc_info=True)
        if effective_text_channel: await effective_text_channel.send(f"Sorry, something went wrong while trying to call {target_name}.")
        return False # Indicate failure


# --- Brain Request Processing & Execution ---
async def process_brain_request(*, prompt: str, bot, sink, system_prompt: str, effective_text_channel):
    """
    Gets a decision from the LLM brain, parses it, and executes the action if possible.
    Returns a dictionary describing the action taken or decided.
    """
    mark_latency_timestamp("brain_decision_start")
    
    # Now get_brain_decision is async, so we await it
    raw_decision = await get_brain_decision(prompt)
    logger.info(f"LLM Brain Raw Output: '{raw_decision}'") # Log the raw output immediately
    
    mark_latency_timestamp("brain_decision_end")

    # Basic parsing (can be made more robust)
    action_detail = {'action': 'error', 'raw_decision': raw_decision, 'details': 'Could not parse decision.'} # Default error

    # Simplified parsing approach - look for "Action: X" pattern first
    action_match = re.match(r"Action:\s*(\w+)", raw_decision, re.IGNORECASE)

    if action_match:
        action_type = action_match.group(1).strip().lower()

        # --- Call User Parsing & Execution ---
        if action_type == "call":
            # Extract target name - everything after "Action: Call" until end of line or punctuation
            call_match = re.match(r"Action:\s*Call\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
            if call_match:
                target_name = call_match.group(1).strip()
                logger.info(f"Brain decided to call user: {target_name}")
                # Execute the call asynchronously
                logger.info(f"Attempting to execute call for {target_name} via _execute_call_user...")
                success = False # Default to failure
                try:
                    success = await _execute_call_user(
                        bot=bot,
                        sink=sink,
                        target_name=target_name,
                        system_prompt=system_prompt,
                        effective_text_channel=effective_text_channel
                    )
                    logger.info(f"Call execution for {target_name} completed. Success: {success}")
                except Exception as exec_err:
                    logger.error(f"Exception during _execute_call_user for {target_name}: {exec_err}", exc_info=True)
                    success = False
                action_detail = {'action': 'call_user', 'target': target_name, 'success': success, 'raw_decision': raw_decision}

        # --- DM User Parsing ---
        elif action_type == "dm":
            # Try to match specific message format first: DM <user> message "<message>"
            dm_message_match = re.match(r"Action:\s*DM\s+(.*?)\s+message\s+\"([^\"]*)\"", raw_decision, re.IGNORECASE)
            if dm_message_match:
                target_identifier = dm_message_match.group(1).strip()
                exact_message = dm_message_match.group(2).strip()
                logger.info(f"Brain decided to DM user: {target_identifier} with exact message: \"{exact_message}\"")
                action_detail = {'action': 'dm_user', 'target': target_identifier, 'exact_message': exact_message, 'success': True, 'raw_decision': raw_decision}
            else:
                # Fallback to topic format: DM <user> about "<topic>"
                dm_topic_match = re.match(r"Action:\s*DM\s+(.*?)\s+about\s+\"([^\"]*)\"", raw_decision, re.IGNORECASE)
                if dm_topic_match:
                    target_identifier = dm_topic_match.group(1).strip()
                    topic = dm_topic_match.group(2).strip()
                    logger.info(f"Brain decided to DM user: {target_identifier} about \"{topic}\"")
                    action_detail = {'action': 'dm_user', 'target': target_identifier, 'topic': topic, 'success': True, 'raw_decision': raw_decision}

        # --- No Action Parsing ---
        elif action_type == "no":
            logger.info("Brain decided no action is needed.")
            action_detail = {'action': 'no_action', 'raw_decision': raw_decision}

        # --- Pokemon Info Parsing ---
        elif action_type == "pokemon":
            # Extract the last mentioned Pokémon name in the brain output (handles multiple lines)
            poke_matches = re.findall(r"Action:\s*Pokemon\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
            if poke_matches:
                poke_name = poke_matches[-1].strip()  # choose the last one
                logger.info(f"Brain decided Pokémon info for: {poke_name}")
                action_detail = {'action': 'pokemon_info', 'pokemon_name': poke_name.lower(), 'success': True, 'raw_decision': raw_decision}

        # --- Web Search Parsing ---
        elif action_type == "websearch":
            # Extract the search query from WebSearch action - be more generous with capture
            search_matches = re.findall(r"Action:\s*WebSearch\s+(.+?)(?:\n|$)", raw_decision, re.IGNORECASE | re.DOTALL)
            if search_matches:
                search_query = search_matches[-1].strip()  # choose the last one
                # Clean up any trailing punctuation or extra whitespace
                search_query = re.sub(r'[\n\r]+', ' ', search_query).strip()
                logger.info(f"Brain decided web search for: {search_query}")
                action_detail = {'action': 'web_search', 'query': search_query, 'success': True, 'raw_decision': raw_decision}

        # --- Type Chart Parsing ---
        elif action_type == "typechart":
            # Extract the type name from TypeChart action
            type_matches = re.findall(r"Action:\s*TypeChart\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
            if type_matches:
                type_name = type_matches[-1].strip()  # choose the last one
                logger.info(f"Brain decided type effectiveness for: {type_name}")
                action_detail = {'action': 'type_effectiveness', 'type_name': type_name.lower(), 'success': True, 'raw_decision': raw_decision}

        # --- Disconnect User Parsing & Execution ---
        elif action_type == "disconnect":
            disconnect_match = re.match(r"Action:\s*Disconnect\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
            if disconnect_match:
                target_name = disconnect_match.group(1).strip()
                logger.info(f"Brain decided to disconnect user: {target_name}")
                # Execute the disconnect asynchronously
                logger.info(f"Attempting to execute disconnect for {target_name} via _execute_disconnect_user...")
                success = False # Default to failure
                try:
                    success = await _execute_disconnect_user(
                        bot=bot,
                        sink=sink,
                        target_name=target_name,
                        effective_text_channel=effective_text_channel
                    )
                except Exception as e:
                    logger.error(f"Exception during disconnect execution for {target_name}: {e}", exc_info=True)
                    success = False

                action_detail = {'action': 'disconnect_user', 'target': target_name, 'success': success, 'raw_decision': raw_decision}

    # Fallback patterns for responses without "Action:" prefix
    
    # DM fallback - look for just "DM <user> message "<message>""
    elif re.match(r"DM\s+", raw_decision, re.IGNORECASE):
        # Try specific message format first
        dm_message_match = re.match(r"DM\s+(.*?)\s+message\s+\"([^\"]*)\"", raw_decision, re.IGNORECASE)
        if dm_message_match:
            target_identifier = dm_message_match.group(1).strip()
            exact_message = dm_message_match.group(2).strip()
            logger.info(f"Brain decided DM (fallback pattern) user: {target_identifier} with message: \"{exact_message}\"")
            action_detail = {'action': 'dm_user', 'target': target_identifier, 'exact_message': exact_message, 'success': True, 'raw_decision': raw_decision}
        else:
            # Try topic format
            dm_topic_match = re.match(r"DM\s+(.*?)\s+about\s+\"([^\"]*)\"", raw_decision, re.IGNORECASE)
            if dm_topic_match:
                target_identifier = dm_topic_match.group(1).strip()
                topic = dm_topic_match.group(2).strip()
                logger.info(f"Brain decided DM (fallback pattern) user: {target_identifier} about \"{topic}\"")
                action_detail = {'action': 'dm_user', 'target': target_identifier, 'topic': topic, 'success': True, 'raw_decision': raw_decision}
    
    # Call fallback - look for just "Call <user>"
    elif re.match(r"Call\s+", raw_decision, re.IGNORECASE):
        call_match = re.match(r"Call\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
        if call_match:
            target_name = call_match.group(1).strip()
            logger.info(f"Brain decided Call (fallback pattern) user: {target_name}")
            # Execute the call asynchronously
            success = False # Default to failure
            try:
                success = await _execute_call_user(
                    bot=bot,
                    sink=sink,
                    target_name=target_name,
                    system_prompt=system_prompt,
                    effective_text_channel=effective_text_channel
                )
                logger.info(f"Call execution for {target_name} completed. Success: {success}")
            except Exception as exec_err:
                logger.error(f"Exception during _execute_call_user for {target_name}: {exec_err}", exc_info=True)
                success = False
            action_detail = {'action': 'call_user', 'target': target_name, 'success': success, 'raw_decision': raw_decision}
    
    # Web search fallback - look for just "WebSearch <query>"
    elif re.match(r"WebSearch\s+(.+)", raw_decision, re.IGNORECASE):
        search_match = re.match(r"WebSearch\s+(.+?)(?:\n|$)", raw_decision, re.IGNORECASE | re.DOTALL)
        if search_match:
            search_query = search_match.group(1).strip()
            # Clean up any trailing punctuation or extra whitespace
            search_query = re.sub(r'[\n\r]+', ' ', search_query).strip()
            logger.info(f"Brain decided web search (fallback pattern) for: {search_query}")
            action_detail = {'action': 'web_search', 'query': search_query, 'success': True, 'raw_decision': raw_decision}
    
    # No Action fallback
    elif re.search(r"No Action|No further action", raw_decision, re.IGNORECASE):
        logger.info("Brain decided no action is needed (fallback pattern).")
        action_detail = {'action': 'no_action', 'raw_decision': raw_decision}

    # --- Error Handling ---
    elif raw_decision.startswith("Error:"):
        logger.error(f"Brain encountered an error during decision generation: {raw_decision}")
        action_detail = {'action': 'error', 'raw_decision': raw_decision, 'details': raw_decision}

    # --- Fallback/Unknown ---
    else:
        # Only log as warning if it wasn't handled by other specific actions
        if action_detail['action'] == 'error' and action_detail['details'] == 'Could not parse decision.':
             logger.warning(f"Brain returned unparseable decision: {raw_decision}")
        # Keep default error action_detail or the one set by specific parsing logic if it failed execution

    return action_detail


# Basic Test (Phase 1, Step 4) - Keep for basic API check, but doesn't test new logic
if __name__ == "__main__":
    import asyncio

    async def run_tests():
        print("Testing LLM Brain connection and prompt loading...")
        # Ensure LM Studio is running with the 'gemma-3-4b-it' model loaded.
        test_prompt = "User 'TestUser#1234' asked: 'Luna, can you disconnect Bob#5678 from the voice channel?' "
        decision = await get_brain_decision(test_prompt)
        print(f"Test Prompt: {test_prompt}")
        print(f"Received Decision: {decision}")

        print("\nTesting with a different prompt...")
        test_prompt_2 = "User 'Admin#0001' said: 'DM Alice#1111 and tell her the meeting is moved to 3 PM.' "
        decision_2 = await get_brain_decision(test_prompt_2)
        print(f"Test Prompt: {test_prompt_2}")
        print(f"Received Decision: {decision_2}")

    # Run the async test function
    asyncio.run(run_tests())