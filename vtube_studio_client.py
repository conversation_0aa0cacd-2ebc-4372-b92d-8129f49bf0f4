"""
VTube Studio API Client for Luna Discord Bot

This module handles all communication with the VTube Studio API including:
- WebSocket connection management
- Plugin authentication
- Hotkey triggering for expressions
- Robust error handling and logging
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Optional, Dict, Any, List

try:
    import websockets
    from websockets.exceptions import ConnectionClosed, WebSocketException
except ImportError:
    logging.error("websockets library not found. Please install it with: pip install websockets")
    raise

from llm_response.config import (
    VTUBE_STUDIO_ENABLED,
    VTUBE_STUDIO_URL,
    VTUBE_STUDIO_PLUGIN_NAME,
    VTUBE_STUDIO_PLUGIN_DEVELOPER
)

# Import latency tracking
try:
    from llm_response.latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

logger = logging.getLogger(__name__)

class VTubeStudioClient:
    """VTube Studio API client with connection management and authentication."""
    
    def __init__(self):
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.auth_token: Optional[str] = None
        self.is_authenticated: bool = False
        self.connection_lock = asyncio.Lock()
        self.last_connection_attempt = 0
        self.connection_retry_delay = 5.0  # seconds
        
    async def connect_vts(self) -> bool:
        """
        Establishes WebSocket connection to VTube Studio.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        if not VTUBE_STUDIO_ENABLED:
            logger.debug("VTube Studio integration is disabled")
            return False
            
        try:
            # Prevent rapid reconnection attempts
            current_time = time.time()
            if current_time - self.last_connection_attempt < self.connection_retry_delay:
                logger.debug(f"Skipping connection attempt, retry delay not met")
                return False
                
            self.last_connection_attempt = current_time
            
            logger.info(f"Attempting to connect to VTube Studio at {VTUBE_STUDIO_URL}")
            
            # Close existing connection if any
            if self.websocket:
                try:
                    await self.websocket.close()
                except Exception:
                    pass  # Ignore errors when closing
                
            # Establish new connection with timeout
            self.websocket = await asyncio.wait_for(
                websockets.connect(VTUBE_STUDIO_URL),
                timeout=10.0
            )
            
            logger.info("✅ Successfully connected to VTube Studio WebSocket")
            
            # Attempt authentication
            auth_success = await self._authenticate()
            if auth_success:
                logger.info("✅ VTube Studio authentication successful")
                return True
            else:
                logger.warning("❌ VTube Studio authentication failed")
                await self._disconnect()
                return False
                
        except asyncio.TimeoutError:
            logger.error("❌ VTube Studio connection timed out")
            return False
        except ConnectionRefusedError:
            logger.error("❌ VTube Studio connection refused - is VTube Studio running with API enabled?")
            return False
        except Exception as e:
            logger.error(f"❌ Error connecting to VTube Studio: {e}", exc_info=True)
            return False
    
    async def _disconnect(self):
        """Safely disconnect from VTube Studio."""
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                logger.debug(f"Error closing websocket: {e}")
        
        self.websocket = None
        self.is_authenticated = False
    
    async def ensure_vts_connection(self) -> bool:
        """
        Ensures VTube Studio connection is active and authenticated.
        Attempts to reconnect if needed.
        
        Returns:
            bool: True if connected and authenticated, False otherwise
        """
        if not VTUBE_STUDIO_ENABLED:
            return False
            
        async with self.connection_lock:
            # Check if we have a valid connection
            if (self.websocket and self.is_authenticated):
                return True
            
            # Attempt to connect
            return await self.connect_vts()
    
    async def _authenticate(self) -> bool:
        """
        Handles VTube Studio plugin authentication process.
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            # Step 1: Request authentication token
            if not self.auth_token:
                logger.info("Requesting authentication token from VTube Studio...")
                token_request = {
                    "apiName": "VTubeStudioPublicAPI",
                    "apiVersion": "1.0",
                    "requestID": str(uuid.uuid4()),
                    "messageType": "AuthenticationTokenRequest",
                    "data": {
                        "pluginName": VTUBE_STUDIO_PLUGIN_NAME,
                        "pluginDeveloper": VTUBE_STUDIO_PLUGIN_DEVELOPER,
                        "pluginIcon": ""  # Optional base64 icon
                    }
                }
                
                await self._send_request(token_request)
                response = await self._receive_response()
                
                if response and response.get("messageType") == "AuthenticationTokenResponse":
                    if response.get("data", {}).get("authenticationToken"):
                        self.auth_token = response["data"]["authenticationToken"]
                        logger.info("✅ Authentication token received")
                    else:
                        error_msg = response.get("data", {}).get("errorID", "Unknown error")
                        logger.error(f"❌ Token request failed: {error_msg}")
                        return False
                else:
                    logger.error("❌ Unexpected response to token request")
                    return False
            
            # Step 2: Authenticate with the token
            logger.info("Authenticating with VTube Studio...")
            auth_request = {
                "apiName": "VTubeStudioPublicAPI",
                "apiVersion": "1.0",
                "requestID": str(uuid.uuid4()),
                "messageType": "AuthenticationRequest",
                "data": {
                    "pluginName": VTUBE_STUDIO_PLUGIN_NAME,
                    "pluginDeveloper": VTUBE_STUDIO_PLUGIN_DEVELOPER,
                    "authenticationToken": self.auth_token
                }
            }
            
            await self._send_request(auth_request)
            response = await self._receive_response()
            
            if response and response.get("messageType") == "AuthenticationResponse":
                if response.get("data", {}).get("authenticated") is True:
                    self.is_authenticated = True
                    logger.info("✅ Successfully authenticated with VTube Studio")
                    return True
                else:
                    error_id = response.get("data", {}).get("errorID", "Unknown")
                    reason = response.get("data", {}).get("reason", "No reason provided")
                    logger.error(f"❌ Authentication failed - Error {error_id}: {reason}")
                    if error_id == 50:  # Token expired or invalid
                        self.auth_token = None  # Reset token to request new one
                    return False
            else:
                logger.error("❌ Unexpected response to authentication request")
                return False
                
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}", exc_info=True)
            return False
    
    async def _send_request(self, request: Dict[str, Any]):
        """Send a request to VTube Studio."""
        if not self.websocket:
            raise Exception("WebSocket not connected")
        
        request_json = json.dumps(request)
        logger.debug(f"📤 Sending VTS request: {request['messageType']}")
        logger.debug(f"📤 Request data: {request.get('data', {})}")
        await self.websocket.send(request_json)
    
    async def _receive_response(self, timeout: float = 10.0) -> Optional[Dict[str, Any]]:
        """Receive and parse response from VTube Studio."""
        if not self.websocket:
            return None
        
        try:
            response_json = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=timeout
            )
            response = json.loads(response_json)
            logger.debug(f"📥 Received VTS response: {response.get('messageType', 'Unknown')}")
            logger.debug(f"📥 Response data: {response.get('data', {})}")
            return response
        except asyncio.TimeoutError:
            logger.error("❌ Timeout waiting for VTube Studio response")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse VTS response JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error receiving VTS response: {e}")
            return None
    
    async def trigger_hotkey(self, hotkey_id: str) -> bool:
        """
        Triggers a hotkey in VTube Studio.
        
        Args:
            hotkey_id (str): The ID of the hotkey to trigger
            
        Returns:
            bool: True if successful, False otherwise
        """
        mark_latency_timestamp("vts_hotkey_start")
        
        if not await self.ensure_vts_connection():
            logger.warning(f"Cannot trigger hotkey '{hotkey_id}' - not connected to VTube Studio")
            mark_latency_timestamp("vts_hotkey_end")
            return False
        
        try:
            logger.info(f"🎭 Triggering VTube Studio hotkey: {hotkey_id}")
            
            hotkey_request = {
                "apiName": "VTubeStudioPublicAPI",
                "apiVersion": "1.0",
                "requestID": str(uuid.uuid4()),
                "messageType": "HotkeyTriggerRequest",
                "data": {
                    "hotkeyID": hotkey_id
                }
            }
            
            mark_latency_timestamp("vts_api_call_start")
            await self._send_request(hotkey_request)
            response = await self._receive_response()
            mark_latency_timestamp("vts_api_call_end")
            
            if response and response.get("messageType") == "HotkeyTriggerResponse":
                error_id = response.get("data", {}).get("errorID", -1)
                if error_id == 0:  # Success
                    logger.info(f"✅ Successfully triggered hotkey: {hotkey_id}")
                    mark_latency_timestamp("vts_hotkey_end")
                    return True
                else:
                    error_msg = response.get("data", {}).get("reason", f"Error ID: {error_id}")
                    
                    # Error ID -1 often means the hotkey worked but VTube Studio returned an unclear response
                    # Let's treat this as a warning rather than an error since the functionality seems to work
                    if error_id == -1:
                        logger.warning(f"⚠️ VTube Studio hotkey '{hotkey_id}' may have triggered (Error ID: -1 - unclear response)")
                        logger.debug(f"Full response: {response}")
                        # Return True since the hotkey likely worked despite the unclear response
                        mark_latency_timestamp("vts_hotkey_end")
                        return True
                    else:
                        logger.error(f"❌ Failed to trigger hotkey '{hotkey_id}': {error_msg}")
                        logger.debug(f"Full response: {response}")
                        mark_latency_timestamp("vts_hotkey_end")
                        return False
            elif response and response.get("messageType") == "APIError":
                # Handle API errors specifically
                error_id = response.get("data", {}).get("errorID", "Unknown")
                error_msg = response.get("data", {}).get("message", "Unknown error")
                
                if error_id == 204:
                    logger.error(f"❌ Hotkey '{hotkey_id}' has invalid data or missing files")
                    logger.error(f"💡 Check if the expression/animation files referenced by this hotkey exist")
                    logger.error(f"💡 Error: {error_msg}")
                elif error_id == 5:
                    logger.error(f"❌ Hotkey '{hotkey_id}' not found in current model")
                elif error_id == 6:
                    logger.error(f"❌ No model is currently loaded in VTube Studio")
                else:
                    logger.error(f"❌ VTube Studio API error {error_id}: {error_msg}")
                
                logger.debug(f"Full error response: {response}")
                mark_latency_timestamp("vts_hotkey_end")
                return False
            else:
                logger.error(f"❌ Unexpected response to hotkey trigger for '{hotkey_id}'")
                logger.debug(f"Full response: {response}")
                mark_latency_timestamp("vts_hotkey_end")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error triggering hotkey '{hotkey_id}': {e}", exc_info=True)
            mark_latency_timestamp("vts_hotkey_end")
            return False
    
    async def close(self):
        """Close the VTube Studio connection."""
        logger.info("Closing VTube Studio connection...")
        await self._disconnect()

    async def get_available_hotkeys(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get all available hotkeys for the current model.
        
        Returns:
            Optional[List[Dict[str, Any]]]: List of available hotkeys, or None if failed
        """
        mark_latency_timestamp("vts_hotkeys_start")
        
        if not await self.ensure_vts_connection():
            logger.warning("Cannot get hotkeys - not connected to VTube Studio")
            mark_latency_timestamp("vts_hotkeys_end")
            return None
        
        try:
            logger.info("🎭 Getting available hotkeys from VTube Studio")
            
            hotkeys_request = {
                "apiName": "VTubeStudioPublicAPI",
                "apiVersion": "1.0",
                "requestID": str(uuid.uuid4()),
                "messageType": "HotkeysInCurrentModelRequest"
            }
            
            mark_latency_timestamp("vts_api_call_start")
            await self._send_request(hotkeys_request)
            response = await self._receive_response()
            mark_latency_timestamp("vts_api_call_end")
            
            if response and response.get("messageType") == "HotkeysInCurrentModelResponse":
                data = response.get("data", {})
                hotkeys = data.get("availableHotkeys", [])
                logger.info(f"✅ Retrieved {len(hotkeys)} hotkeys from VTube Studio")
                mark_latency_timestamp("vts_hotkeys_end")
                return hotkeys
            else:
                logger.error(f"❌ Unexpected response to hotkeys request")
                logger.debug(f"Full response: {response}")
                mark_latency_timestamp("vts_hotkeys_end")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting hotkeys: {e}", exc_info=True)
            mark_latency_timestamp("vts_hotkeys_end")
            return None

# Global client instance
_vts_client: Optional[VTubeStudioClient] = None

async def get_vts_client() -> VTubeStudioClient:
    """Get or create the global VTube Studio client instance."""
    global _vts_client
    if _vts_client is None:
        _vts_client = VTubeStudioClient()
    return _vts_client

async def connect_vts() -> bool:
    """Initialize VTube Studio connection."""
    if not VTUBE_STUDIO_ENABLED:
        logger.info("VTube Studio integration is disabled")
        return False
    
    mark_latency_timestamp("vts_connect_start")
    client = await get_vts_client()
    success = await client.connect_vts()
    mark_latency_timestamp("vts_connect_end")
    return success

async def ensure_vts_connection() -> bool:
    """Ensure VTube Studio connection is active."""
    if not VTUBE_STUDIO_ENABLED:
        return False
    
    client = await get_vts_client()
    return await client.ensure_vts_connection()

async def trigger_hotkey(hotkey_id: str) -> bool:
    """Trigger a VTube Studio hotkey."""
    if not VTUBE_STUDIO_ENABLED:
        return False
    
    client = await get_vts_client()
    return await client.trigger_hotkey(hotkey_id)

async def return_to_idle() -> bool:
    """Return Luna to idle/neutral expression by removing all expressions."""
    if not VTUBE_STUDIO_ENABLED:
        return False
        
    mark_latency_timestamp("vts_idle_start")
    # Use the "Remove Expressions" hotkey we found earlier
    success = await trigger_hotkey("a1ca21c1c6c8463f8012d6115090f0c4")
    mark_latency_timestamp("vts_idle_end")
    return success

async def close_vts_client():
    """Close the VTube Studio client."""
    global _vts_client
    if _vts_client:
        await _vts_client.close()
        _vts_client = None 

async def get_available_hotkeys() -> Optional[List[Dict[str, Any]]]:
    """Get all available hotkeys for the current model."""
    if not VTUBE_STUDIO_ENABLED:
        return None
    
    client = await get_vts_client()
    return await client.get_available_hotkeys() 