#!/usr/bin/env python3
"""
Debug FTS Search Issues
======================
Investigates why FTS search isn't finding <PERSON>'s specific preference facts
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_fts_issues():
    print("🔍 Debugging FTS Search Issues")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Connect directly to database
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # First, let's see what's actually in the FTS table
        print("📋 FTS Table Contents:")
        cursor = conn.execute("SELECT rowid, content FROM memory_facts_fts LIMIT 10")
        for row in cursor.fetchall():
            print(f"  {row[0]}: {row[1][:80]}...")
        
        print(f"\n🧪 Testing FTS Queries:")
        
        # Test various FTS query patterns
        test_patterns = [
            'luna',
            'favorite', 
            'color',
            'purple',
            'potatoes',
            'minecraft',
            'cobblestone',
            '"luna"',
            '"favorite"',
            '"color"',
            '"luna" OR "favorite"',
            '"favorite" OR "color"',
            '"luna" OR "color"',
            'luna AND favorite',
            'luna AND color',
            'favorite AND color'
        ]
        
        for pattern in test_patterns:
            try:
                cursor = conn.execute("""
                    SELECT mf.rowid, mf.content, mf.confidence 
                    FROM memory_facts mf
                    WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                    LIMIT 3
                """, (pattern,))
                
                results = cursor.fetchall()
                print(f"\n  FTS '{pattern}' -> {len(results)} results:")
                for result in results:
                    print(f"    {result[0]}: {result[1][:60]}... (conf: {result[2]})")
                    
            except Exception as e:
                print(f"  FTS '{pattern}' -> ERROR: {e}")
        
        # Check if Luna's specific facts are indexed
        print(f"\n🔍 Checking specific Luna facts in FTS:")
        
        luna_facts = [
            "Luna's favorite color is purple",
            "Luna loves potatoes and considers them the best food",
            "Luna's favorite Minecraft block is cobblestone"
        ]
        
        for fact_content in luna_facts:
            # Find the fact in main table
            cursor = conn.execute("SELECT id FROM memory_facts WHERE content = ?", (fact_content,))
            fact_rows = cursor.fetchall()
            
            if fact_rows:
                fact_id = fact_rows[0][0]
                # Check if it exists in FTS
                cursor = conn.execute("SELECT content FROM memory_facts_fts WHERE rowid = ?", (fact_id,))
                fts_rows = cursor.fetchall()
                
                if fts_rows:
                    print(f"  ✅ '{fact_content[:40]}...' -> ID {fact_id} is in FTS")
                else:
                    print(f"  ❌ '{fact_content[:40]}...' -> ID {fact_id} NOT in FTS!")
            else:
                print(f"  ❌ '{fact_content[:40]}...' -> Not found in main table!")
        
        # Rebuild FTS index and test again
        print(f"\n🔧 Rebuilding FTS index...")
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
        print("✅ FTS index rebuilt")
        
        # Test again after rebuild
        print(f"\n🧪 Testing after FTS rebuild:")
        for pattern in ['"luna"', '"favorite"', '"color"', '"luna" OR "favorite"']:
            try:
                cursor = conn.execute("""
                    SELECT mf.rowid, mf.content, mf.confidence 
                    FROM memory_facts mf
                    WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                    LIMIT 3
                """, (pattern,))
                
                results = cursor.fetchall()
                print(f"  FTS '{pattern}' -> {len(results)} results:")
                for result in results:
                    print(f"    {result[0]}: {result[1][:60]}... (conf: {result[2]})")
                    
            except Exception as e:
                print(f"  FTS '{pattern}' -> ERROR: {e}")
    
    finally:
        conn.close()
    
    print(f"\n🎯 Summary:")
    print(f"  - If Luna facts are not in FTS, that's the problem")
    print(f"  - If FTS queries still fail after rebuild, there's a query formation issue")

if __name__ == "__main__":
    asyncio.run(debug_fts_issues())