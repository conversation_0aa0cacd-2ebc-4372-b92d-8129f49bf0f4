#!/usr/bin/env python3
"""
Test Memory Storage
===================
Tests if the memory system is properly storing new memories from conversations
"""
import asyncio
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import process_conversation_memory

async def test_memory_storage():
    print("💾 Testing Memory Storage System")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # User ID from logs
    user_id = 921637353364287489
    channel_id = "1378098310036066335"
    
    print(f"👤 User ID: {user_id}")
    print(f"📺 Channel ID: {channel_id}")
    
    # Test the exact conversation from the logs
    print(f"\n1️⃣ Testing memory storage with user preference:")
    conversation_messages = [
        {"role": "user", "content": "Luna my favorite color is blue", "user_id": user_id},
        {"role": "assistant", "content": "Blue? *Please*.", "user_id": 123456789}
    ]
    
    print(f"   Messages to process:")
    for i, msg in enumerate(conversation_messages, 1):
        print(f"     {i}. {msg['role']}: {msg['content']}")
    
    # Process the conversation
    try:
        print(f"\n   🧠 Processing conversation for memory extraction...")
        await process_conversation_memory(
            messages=conversation_messages,
            user_id=user_id,
            channel_id=channel_id,
            channel_type="text"
        )
        print(f"   ✅ Conversation queued for background processing")
        
        # Wait a bit for background processing
        print(f"   ⏳ Waiting 3 seconds for background processing...")
        await asyncio.sleep(3)
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Check if the memory was stored
    print(f"\n2️⃣ Checking if memory was stored:")
    
    # Search for facts about the user's color preference
    facts = ms.warm_storage.search_facts(
        query="blue color favorite",
        user_id=user_id,
        limit=5
    )
    
    if facts:
        print(f"   ✅ Found {len(facts)} facts about user's preferences:")
        for i, fact in enumerate(facts, 1):
            print(f"     {i}. {fact.content} (confidence: {fact.confidence})")
    else:
        print(f"   ❌ No facts found about user's color preference")
        
        # Check if ANY facts were stored for this user
        all_user_facts = ms.warm_storage.search_facts(
            query="",
            user_id=user_id,
            limit=10
        )
        
        if all_user_facts:
            print(f"   📊 But found {len(all_user_facts)} other facts for this user:")
            for i, fact in enumerate(all_user_facts, 1):
                print(f"     {i}. {fact.content}")
        else:
            print(f"   📊 No facts found for this user at all")
    
    # Test background processor status
    print(f"\n3️⃣ Checking background processor status:")
    try:
        stats = ms.get_performance_stats()
        print(f"   Background queue size: {stats['background_queue_size']}")
        print(f"   Cache hit rate: {stats['cache_hit_rate']}")
        print(f"   Avg retrieval time: {stats['avg_retrieval_time']}")
        print(f"   Avg extraction time: {stats['avg_extraction_time']}")
    except Exception as e:
        print(f"   ❌ Error getting stats: {e}")
    
    # Check if qwen3_client is available
    print(f"\n4️⃣ Checking fact extraction dependencies:")
    try:
        from llm_response.initialization import get_qwen3_client
        qwen3_client = get_qwen3_client()
        if qwen3_client:
            print(f"   ✅ Qwen3 client available for fact extraction")
        else:
            print(f"   ❌ Qwen3 client not available - this prevents fact extraction!")
            print(f"   💡 Memory extraction requires Qwen3 client to analyze conversations")
    except Exception as e:
        print(f"   ❌ Error checking Qwen3 client: {e}")
    
    # Manual fact storage test
    print(f"\n5️⃣ Testing manual fact storage:")
    try:
        from llm_response.memory_system import MemoryFact, MemoryType
        
        # Create a test fact manually
        test_fact = MemoryFact(
            content="User said their favorite color is blue",
            memory_type=MemoryType.FACTUAL,
            user_id=user_id,
            channel_id=channel_id,
            confidence=0.9,
            timestamp=time.time(),
            entities=["blue", "color", "favorite"],
            relationships=[("user", "prefers", "blue")]
        )
        
        # Store it
        fact_id = ms.warm_storage.store_fact(test_fact)
        print(f"   ✅ Manual fact stored with ID: {fact_id}")
        
        # Try to retrieve it
        manual_facts = ms.warm_storage.search_facts(
            query="blue color",
            user_id=user_id,
            limit=5
        )
        
        if manual_facts:
            print(f"   ✅ Manual fact retrieved successfully:")
            for fact in manual_facts:
                if fact.content == test_fact.content:
                    print(f"     ✅ {fact.content}")
                    break
        else:
            print(f"   ❌ Could not retrieve manually stored fact")
            
    except Exception as e:
        print(f"   ❌ Manual storage test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_memory_storage())