#!/usr/bin/env python3
"""
Debug FTS Search Issues
"""
import asyncio
import sqlite3
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType

async def debug_fts_search():
    print("🔍 Debugging FTS Search Issues")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Store a simple fact
    test_fact = MemoryFact(
        content="Luna's favorite color is purple",
        memory_type=MemoryType.FACTUAL,
        user_id=12345,
        channel_id="general",
        confidence=0.95,
        timestamp=time.time(),
        entities=["Luna", "purple", "color"],
        relationships=[("Luna", "prefers", "purple")]
    )
    
    fact_id = ms.warm_storage.store_fact(test_fact)
    print(f"✅ Stored fact: {test_fact.content}")
    
    # Rebuild FTS
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
        print("🔧 Rebuilt FTS index")
        
        # Check what's actually in the database
        print("\n📊 Database Contents:")
        cursor = conn.execute("SELECT id, content, entities FROM memory_facts WHERE id = ?", (fact_id,))
        row = cursor.fetchone()
        if row:
            print(f"  ID: {row[0]}")
            print(f"  Content: '{row[1]}'")
            print(f"  Entities: '{row[2]}'")
        
        # Test different search strategies
        print("\n🧪 Testing Search Strategies:")
        
        search_tests = [
            # Single terms
            ("luna", "Single term: luna"),
            ("color", "Single term: color"),
            ("purple", "Single term: purple"),
            ("favorite", "Single term: favorite"),
            
            # Combinations
            ("luna color", "Two terms: luna color"),
            ("favorite color", "Two terms: favorite color"),
            ("luna favorite", "Two terms: luna favorite"),
            ("luna favorite color", "Three terms: luna favorite color"),
            
            # Different operators
            ("luna AND color", "AND operator"),
            ("luna OR color", "OR operator"),
            ("\"luna color\"", "Exact phrase"),
            
            # Partial matches
            ("lun*", "Prefix match"),
            ("*olor", "Suffix match")
        ]
        
        for search_term, description in search_tests:
            try:
                # Test FTS search
                escaped_query = search_term.replace('"', '""')
                cursor = conn.execute("""
                    SELECT mf.id, mf.content FROM memory_facts mf
                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                """, (f'"{escaped_query}"',))
                fts_results = cursor.fetchall()
                
                print(f"  {description}: '{search_term}' -> {len(fts_results)} FTS results")
                
                # Also test warm storage search
                facts = ms.warm_storage.search_facts(query=search_term, user_id=12345, limit=5)
                print(f"    Warm storage: {len(facts)} results")
                
                if fts_results and len(fts_results) > 0:
                    for result in fts_results[:1]:
                        print(f"      -> {result[1]}")
                        
            except Exception as e:
                print(f"  {description}: ERROR - {e}")
        
        # Test entity search
        print(f"\n🏷️ Testing Entity-based Search:")
        cursor = conn.execute("""
            SELECT id, content, entities FROM memory_facts 
            WHERE entities LIKE '%Luna%' OR entities LIKE '%color%' OR entities LIKE '%purple%'
        """)
        entity_results = cursor.fetchall()
        print(f"  Entity search found: {len(entity_results)} results")
        for result in entity_results:
            print(f"    {result[0]}: {result[1]} | entities: {result[2]}")
    
    finally:
        conn.close()
    
    # Test the enhanced function with different approaches
    print(f"\n🚀 Testing Enhanced Function with Different Queries:")
    from llm_response.processing import enhanced_get_memory_context
    
    test_queries = [
        "luna",
        "color", 
        "purple",
        "favorite",
        "Luna color",
        "what color",
        "Luna's favorite color",
        "purple color"
    ]
    
    for query in test_queries:
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=12345
        )
        status = "✅ FOUND" if context and context.strip() else "❌ NOT FOUND"
        print(f"  '{query}' -> {status}")
        if context and context.strip():
            print(f"    Context: {context[:100]}...")

if __name__ == "__main__":
    asyncio.run(debug_fts_search())