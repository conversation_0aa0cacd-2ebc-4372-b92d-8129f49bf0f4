import os
import logging
import asyncio
import io
import re
import discord
import time
import torch
import threading
import traceback

# Import RealtimeTTS and KokoroEngine
import RealtimeTTS
from RealtimeTTS.engines import KokoroEngine

# Preprocess text to reduce pauses at punctuation
def preprocess_text_for_tts(text):
    """Intelligently process punctuation to reduce long pauses while maintaining natural speech"""
    import re

    # First, preserve the original text for logging
    original_text = text

    # Replace multiple periods/exclamation/question marks with a single one
    text = re.sub(r'\.{2,}', '.', text)
    text = re.sub(r'\!{2,}', '!', text)
    text = re.sub(r'\?{2,}', '?', text)

    # Add spaces after punctuation if missing to improve pronunciation
    text = re.sub(r'([.!?,;:])([^\s])', r'\1 \2', text)

    # Add slight pauses between words that might run together
    # For example: "pouch potato" -> "pouch, potato"
    text = re.sub(r'(\w+)\s+(potato|kangaroo|alright)', r'\1, \2', text)

    # For sentences in the middle of the text, replace periods with commas
    # But keep the final period to maintain natural speech cadence
    sentences = re.split(r'([.!?])', text)
    if len(sentences) > 2:  # If we have multiple sentences
        # Keep the last sentence intact
        last_sentence = sentences[-2] + sentences[-1] if len(sentences) % 2 == 0 else sentences[-1]
        # Process middle sentences
        middle_text = ''.join(sentences[:-2]) if len(sentences) % 2 == 0 else ''.join(sentences[:-1])
        middle_text = re.sub(r'\. ', ', ', middle_text)  # Replace period+space with comma+space
        middle_text = re.sub(r'! ', ', ', middle_text)   # Replace exclamation+space with comma+space
        middle_text = re.sub(r'\? ', ', ', middle_text)  # Replace question+space with comma+space
        text = middle_text + last_sentence

    # Replace multiple commas with a single comma
    text = re.sub(r',+', ',', text)

    # Replace comma+space+comma with just comma+space
    text = re.sub(r', ,', ', ', text)

    # Add emphasis to important words by adding a comma before them
    text = re.sub(r'\b(call|lazy|pouch|potato)\b', r', \1', text)

    # Log the transformation for debugging
    if original_text != text:
        logger.debug(f"Transformed text for TTS:\nOriginal: {original_text}\nModified: {text}")

    return text

logger = logging.getLogger(__name__)

# Configuration for Kokoro TTS
# Use the mixed voice format as in terminal_tts.py
INITIAL_VOICE = "af_nicole"  # Base voice for initialization
MIXED_VOICE_STRING = os.getenv("KOKORO_VOICE", "2*af_nicole + 5*zf_xiaoyi").strip('"\'')
KOKORO_FALLBACK_VOICE = INITIAL_VOICE  # Use the same base voice as fallback

# Global engine instance
engine = None
engine_lock = threading.Lock()

# Initialize the KokoroEngine in a separate thread to avoid blocking
def initialize_engine():
    global engine
    try:
        logger.info(f"Initializing KokoroEngine with initial voice: {INITIAL_VOICE}...")

        # --- FORCE GPU VISIBILITY FOR TTS -----------------------------------------
        # Some other modules intentionally blank out CUDA_VISIBLE_DEVICES to disable
        # CUDA (they rely on Vulkan/OpenCL instead).  Kokoro/RealtimeTTS, however,
        # requires CUDA for GPU acceleration.  If we detect that the variable has been
        # cleared, restore visibility to at least GPU 0 _before_ the engine is
        # instantiated.  This has to happen before ONNX Runtime initialises inside
        # KokoroEngine.

        if os.environ.get("CUDA_VISIBLE_DEVICES", "") == "":
            # Restore visibility to device-0 only (safest option that works on nearly
            # every single-GPU machine and on multi-GPU rigs where the primary card is
            # index 0).
            os.environ["CUDA_VISIBLE_DEVICES"] = "0"
            logging.getLogger(__name__).warning(
                "CUDA_VISIBLE_DEVICES was blank – restored to '0' so KokoroEngine can access the GPU.")

        # Initialize with initial voice first and slightly faster speed
        with engine_lock:
            # Initialize KokoroEngine with GPU acceleration
            logger.info("Creating KokoroEngine instance (will auto-select CUDA GPU)...")
            logger.info("🚀 KokoroEngine will use automatic GPU acceleration")
            
            engine = KokoroEngine()
            logger.info("✅ KokoroEngine initialized successfully and is ready.")

            # Set the desired mixed voice
            logger.info(f"Setting voice to custom mix: {MIXED_VOICE_STRING}")
            try:
                # Try to set the mixed voice
                engine.set_voice(MIXED_VOICE_STRING)
                logger.info(f"Engine voice set to: {MIXED_VOICE_STRING}")
            except Exception as voice_error:
                logger.warning(f"Error setting mixed voice: {voice_error}")
                logger.warning(f"Falling back to default voice: {INITIAL_VOICE}")

            # Pre-warm the engine
            logger.info("Pre-warming engine...")
            try:
                prewarm_stream = RealtimeTTS.TextToAudioStream(engine)
                prewarm_stream.feed("Engine warm-up.").play(muted=True)
                time.sleep(0.5)  # Allow time for pre-warming
                prewarm_stream.stop()
                logger.info("Engine pre-warmed successfully.")
            except Exception as e_warm:
                logger.warning(f"Pre-warming failed: {e_warm}")
                logger.warning("Continuing without pre-warming - first synthesis may be slower")

        logger.info(f"TTS Initialized using KokoroEngine (Voice: {MIXED_VOICE_STRING}).")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing KokoroEngine: {e}")
        logger.error(traceback.format_exc())
        return False

# NOTE: We no longer start KokoroEngine in a background thread at import time.
# Instead, the first call to synthesize_speech will initialise it synchronously
# (this avoids racing PyTorch CUDA init during very early startup).

async def synthesize_speech(text):
    """Synthesizes speech using RealtimeTTS with KokoroEngine.
    Returns audio bytes that can be played by discord.py.
    """
    global engine

    try:
        # Lazily initialise KokoroEngine on first use
        if engine is None:
            logger.info("KokoroEngine not yet initialised - initialising synchronously now...")
            if not initialize_engine():
                raise Exception("TTS engine failed to initialise")

        # Preprocess the text to reduce pauses at punctuation
        processed_text = preprocess_text_for_tts(text)
        logger.info(f"Original text: '{text[:30]}...'")
        logger.info(f"Processed text: '{processed_text[:30]}...'")

        logger.info(f"Synthesizing speech: '{processed_text[:30]}...'")
        t_start = time.monotonic()

        # Create a buffer to store the audio data
        audio_buffer = io.BytesIO()

        # Run the synthesis in a thread to avoid blocking the event loop
        # Pass the main event loop to the thread function
        def _synthesize_to_buffer(main_loop):
            try: # Main try block for the thread function
                thread_start_time = time.monotonic()
                logger.debug(f"⏱️ [TTS-TIMING] Starting synthesis thread")

                with engine_lock:  # Ensure thread safety when accessing the engine
                    engine_lock_acquired_time = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Engine lock acquisition took {engine_lock_acquired_time - thread_start_time:.4f}s")

                    # Create a stream with the engine
                    stream_creation_start = time.monotonic()
                    stream = RealtimeTTS.TextToAudioStream(engine)
                    stream_creation_end = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Stream creation took {stream_creation_end - stream_creation_start:.4f}s")

                    # Memory-based synthesis logic using on_audio_chunk callback
                    logger.info("Using memory-based synthesis approach.")
                    
                    chunk_count = 0
                    raw_audio_chunks = []
                    
                    def on_audio_chunk_callback(chunk):
                        """Callback to capture audio chunks directly to memory buffer"""
                        nonlocal chunk_count
                        chunk_count += 1
                        logger.debug(f"⏱️ [TTS-TIMING] Received audio chunk {chunk_count}: {len(chunk)} bytes")
                        raw_audio_chunks.append(chunk)

                    # Feed the processed text to the stream (needs a list)
                    feed_start_time = time.monotonic()
                    stream_obj = stream.feed([processed_text])
                    feed_end_time = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Stream feed took {feed_end_time - feed_start_time:.4f}s")

                    # Use memory-based synthesis with on_audio_chunk callback
                    play_start_time = time.monotonic()
                    logger.info(f"Synthesizing text to memory: '{processed_text[:30]}...'")
                    stream_obj.play(
                        on_audio_chunk=on_audio_chunk_callback,
                        muted=True,  # Don't play audio through speakers
                        tokenize_sentences=False,
                        buffer_threshold_seconds=0.5,
                        minimum_sentence_length=10000,
                        fast_sentence_fragment=False
                    )
                    play_end_time = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Memory-based synthesis took {play_end_time - play_start_time:.4f}s")

                    # Combine raw audio chunks and create WAV format
                    wav_creation_start = time.monotonic()
                    if raw_audio_chunks:
                        # Combine all raw audio data
                        raw_audio_data = b''.join(raw_audio_chunks)
                        
                        # Create WAV header for Discord compatibility
                        # RealtimeTTS typically outputs 16-bit, 24kHz PCM audio
                        sample_rate = 24000
                        bits_per_sample = 16
                        channels = 1
                        byte_rate = sample_rate * channels * bits_per_sample // 8
                        block_align = channels * bits_per_sample // 8
                        
                        # WAV file header
                        wav_header = b'RIFF'
                        wav_header += (36 + len(raw_audio_data)).to_bytes(4, 'little')  # File size - 8
                        wav_header += b'WAVE'
                        wav_header += b'fmt '
                        wav_header += (16).to_bytes(4, 'little')  # Subchunk1Size
                        wav_header += (1).to_bytes(2, 'little')   # AudioFormat (PCM)
                        wav_header += channels.to_bytes(2, 'little')  # NumChannels
                        wav_header += sample_rate.to_bytes(4, 'little')  # SampleRate
                        wav_header += byte_rate.to_bytes(4, 'little')  # ByteRate
                        wav_header += block_align.to_bytes(2, 'little')  # BlockAlign
                        wav_header += bits_per_sample.to_bytes(2, 'little')  # BitsPerSample
                        wav_header += b'data'
                        wav_header += len(raw_audio_data).to_bytes(4, 'little')  # Subchunk2Size
                        
                        # Write complete WAV file to buffer
                        audio_buffer.write(wav_header + raw_audio_data)
                        audio_buffer.seek(0)
                        
                        wav_creation_end = time.monotonic()
                        logger.debug(f"⏱️ [TTS-TIMING] WAV format creation took {wav_creation_end - wav_creation_start:.4f}s")
                        
                        total_audio_size = len(audio_buffer.getvalue())
                        logger.info(f"Memory synthesis complete - Total chunks: {chunk_count}, Raw audio: {len(raw_audio_data)} bytes, WAV format: {total_audio_size} bytes")
                    else:
                        logger.warning("No audio chunks received from synthesis!")

                    # Clean up stream object (still inside 'with engine_lock')
                    cleanup_start_time = time.monotonic()
                    stream.stop() # Ensure stream resources are released
                    cleanup_end_time = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Stream cleanup took {cleanup_end_time - cleanup_start_time:.4f}s")

                # Log total thread time (outside 'with engine_lock', but inside main try)
                thread_end_time = time.monotonic()
                logger.debug(f"⏱️ [TTS-TIMING] Total synthesis thread time: {thread_end_time - thread_start_time:.4f}s")

            except Exception as e: # Except block for the main thread function try
                logger.error(f"Error in _synthesize_to_buffer: {e}")
                logger.error(traceback.format_exc())
        # --- End of _synthesize_to_buffer definition ---

        # --- Code back in synthesize_speech ---
        # Get the main event loop
        main_loop = asyncio.get_running_loop()
        # Run the synthesis in a thread, passing the main loop
        await asyncio.to_thread(_synthesize_to_buffer, main_loop)

        # Get the audio data from the buffer
        audio_data = audio_buffer.getvalue()

        t_end = time.monotonic()
        logger.info(f"⏱️ [TTS-TOTAL] Complete in {t_end - t_start:.4f}s")

        return audio_data # Return from synthesize_speech
    except Exception as e:
        logger.error(f"Error in synthesize_speech: {e}")
        logger.error(traceback.format_exc())
        raise Exception(f"Speech synthesis failed: {e}")

class StreamingSpeechPlayer:
    def __init__(self, voice_client, after_play_callback=None, enable_vts_sync=False):
        self.voice_client = voice_client
        self.after_play_callback = after_play_callback
        self.queue = asyncio.Queue()
        self.current_audio = None
        self.running = False
        self.task = None
        self.buffer = ""
        # Modified sentence endings pattern to be more selective
        # Only split on periods followed by spaces and capital letters (likely new sentences)
        # or on question/exclamation marks followed by spaces
        self.sentence_endings = re.compile(r'(?<=[.])\s+(?=[A-Z])|(?<=[!?])\s+')
        self.min_chunk_len = 120  # Increased from 30 to 120 for larger chunks
        self.first_chunk_sent = False  # Flag for immediate first chunk
        self.synthesis_task = None  # Task for prefetching next synthesis
        self.realtime_stream = None  # RealtimeTTS stream for direct playback
        # Removed self.playback_finished event, will use Future per-playback
        
        # VTube Studio synchronization
        self.enable_vts_sync = enable_vts_sync
        self.vts_full_text = ""  # Accumulate all text for VTS TTS
        self.vts_started = False  # Flag to ensure VTS TTS starts only once
        self.vts_emotion_triggered = False  # Flag to ensure emotion is triggered only once

    async def start(self):
        """Start the streaming player task"""
        if self.running:
            return
        self.running = True
        self.first_chunk_sent = False  # Reset flag when starting
        self.task = asyncio.create_task(self._player_loop())

    async def stop(self):
        """Stop the streaming player task"""
        self.running = False

        # Cancel the prefetch task if it exists
        if self.synthesis_task and not self.synthesis_task.done():
            self.synthesis_task.cancel()
            try:
                await self.synthesis_task
            except asyncio.CancelledError:
                pass
            self.synthesis_task = None

        if self.task:
            await self.task
            self.task = None

        # Clear any remaining items in the queue
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
                self.queue.task_done()
            except asyncio.QueueEmpty:
                break

        # Stop any currently playing audio
        if self.voice_client and self.voice_client.is_playing():
            self.voice_client.stop()

    async def add_text(self, text):
        """Add text to the streaming buffer with a larger first chunk for smoother transitions"""
        if not text:
            return

        self.buffer += text
        
        # Accumulate text for VTube Studio TTS
        if self.enable_vts_sync:
            self.vts_full_text += text

        # Immediately send first chunk if we haven't sent anything yet,
        # but make it much larger to reduce gaps between first and subsequent chunks
        if not self.first_chunk_sent and self.buffer:
            # For the first chunk, we want enough text for a substantial utterance
            words = self.buffer.split()

            # Require at least 20 words or 100+ characters for a substantial first chunk
            if len(words) >= 20 or (len(self.buffer) >= 100 and ' ' in self.buffer):
                # Try to find a natural break point - preferably at a sentence end
                # or after several words
                sentence_end = re.search(r'(?<=[.])\s+(?=[A-Z])|(?<=[!?])\s+', self.buffer)
                if sentence_end and sentence_end.end() >= 100:
                    # If there's a sentence end after at least 100 chars, break there
                    chunk_to_send = self.buffer[:sentence_end.end()]
                    self.buffer = self.buffer[sentence_end.end():]
                elif len(words) >= 20:
                    # If we have at least 20 words, send a larger chunk
                    words_to_include = min(30, len(words))  # Include up to 30 words
                    space_after_words = self.buffer.find(' ', len(' '.join(words[:words_to_include])))
                    if space_after_words > 0:
                        chunk_to_send = self.buffer[:space_after_words]
                        self.buffer = self.buffer[space_after_words:]
                    else:
                        chunk_to_send = self.buffer
                        self.buffer = ""
                else:
                    chunk_to_send = self.buffer
                    self.buffer = ""

                self.first_chunk_sent = True
                logger.info(f"Sending larger initial chunk: '{chunk_to_send[:50]}...' ({len(chunk_to_send)} chars, ~{len(chunk_to_send.split())} words)")
                
                # Start VTube Studio emotion at the same time as Discord TTS
                if self.enable_vts_sync and not self.vts_emotion_triggered:
                    self._start_vts_emotion()
                
                await self._synthesize_and_queue(chunk_to_send)
            else:
                # Not enough text for a substantial first chunk yet, continue accumulating
                pass
        else:
            # Process buffer using normal logic for subsequent text
            await self._process_buffer()

    async def finalize(self):
        """Process any remaining text in buffer"""
        if self.buffer:
            await self._synthesize_and_queue(self.buffer)
            self.buffer = ""

        # Add a special sentinel to signal we're done
        await self.queue.put(None)
        
        # Start VTube Studio emotion if we have accumulated text but never started it
        # (This can happen if finalize is called before the first chunk threshold is met)
        if self.enable_vts_sync and not self.vts_emotion_triggered and self.vts_full_text.strip():
            self._start_vts_emotion()

    def _start_vts_emotion(self):
        """Trigger VTube Studio emotion expression (one time only)"""
        if self.vts_emotion_triggered or not self.vts_full_text.strip():
            return
            
        self.vts_emotion_triggered = True
        
        try:
            # Import VTube Studio modules
            import vtube_studio_emotions
            import vtube_studio_client
            from llm_response.config import VTUBE_STUDIO_ENABLED
            
            if VTUBE_STUDIO_ENABLED:
                logger.info(f"🎭 Triggering VTube Studio emotion for: '{self.vts_full_text[:50]}...'")
                
                # Start emotion detection as a background task
                async def vts_emotion_task():
                    try:
                        # Detect emotion and trigger expression
                        hotkey_id = vtube_studio_emotions.detect_emotion_for_vts(self.vts_full_text)
                        if hotkey_id:
                            await vtube_studio_client.trigger_hotkey(hotkey_id)
                    except Exception as e:
                        logger.error(f"❌ Error in VTube Studio emotion trigger: {e}", exc_info=True)
                
                # Create the task without awaiting it (run in background)
                import asyncio
                asyncio.create_task(vts_emotion_task())
                logger.debug("🎭 VTube Studio emotion trigger started")
                
        except Exception as e:
            logger.error(f"❌ Error triggering VTube Studio emotion: {e}", exc_info=True)
    
    async def _play_audio_locally(self, audio_data):
        """Play the same audio data locally for VTube Studio lip-sync"""
        try:
            import pygame
            import io
            from llm_response.config import VTUBE_STUDIO_AUDIO_DEVICE
            
            logger.info(f"🎭 Playing audio locally for VTube Studio synchronization ({len(audio_data)} bytes)")
            
            # Initialize pygame mixer if not already done
            if not pygame.mixer.get_init():
                # Use CD quality for better compatibility: 44100 Hz, 16-bit, stereo
                pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=1024)
                logger.info("🎵 Pygame mixer initialized for local audio playback")
            
            # Create a BytesIO buffer from the audio data
            audio_buffer = io.BytesIO(audio_data)
            audio_buffer.seek(0)
            
            # Load and play the audio
            pygame.mixer.music.load(audio_buffer)
            pygame.mixer.music.play()
            
            # Wait for the audio to finish playing
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.05)  # Small sleep to prevent blocking
            
            logger.info("🎭 Local audio playback completed successfully")
            
        except ImportError:
            logger.warning("🎭 pygame not available for local audio playback")
        except Exception as e:
            logger.error(f"❌ Error playing audio locally: {e}", exc_info=True)

    async def _process_buffer(self):
        """Process buffered text for subsequent chunks, respecting word boundaries"""
        # Skip if this is the first chunk (handled in add_text)
        if not self.first_chunk_sent:
            return

        processed_chunk = False  # Flag to track if we processed a chunk

        # If buffer has reached minimum length, try to find sentence breaks
        if len(self.buffer) >= self.min_chunk_len:
            splits = list(self.sentence_endings.finditer(self.buffer))
            if splits:
                # Get the position of the last sentence ending
                last_split = splits[-1].end()
                chunk = self.buffer[:last_split]
                self.buffer = self.buffer[last_split:]
                # Synthesize this complete sentence
                await self._synthesize_and_queue(chunk)
                processed_chunk = True
            elif len(self.buffer) > 100:  # Hard limit to avoid very long waits
                # No sentence break but buffer is getting too long
                # Find the last word boundary to avoid cutting words in the middle
                last_space = self.buffer.rfind(' ')
                if last_space > self.min_chunk_len:  # Only use word boundary if it gives a reasonable chunk
                    chunk = self.buffer[:last_space+1]  # Include the space
                    self.buffer = self.buffer[last_space+1:]
                else:
                    # If no good word boundary, use the whole buffer
                    chunk = self.buffer
                    self.buffer = ""
                await self._synthesize_and_queue(chunk)
                processed_chunk = True

        # Return whether a chunk was processed and queued
        return processed_chunk

    async def _synthesize_and_queue(self, text):
        """Synthesize a chunk of text and add to queue"""
        try:
            # Log synthesis request with timing
            logger.info(f"Synthesizing text chunk: '{text[:30]}...' ({len(text)} chars)")
            t_start_tts_synth = time.monotonic()

            # Get audio data from the synthesize_speech function
            audio_data = await synthesize_speech(text)

            t_end_tts_synth = time.monotonic()
            tts_synth_duration = t_end_tts_synth - t_start_tts_synth
            logger.info(f"⏱️ [TTS-SYNTH] Synthesis completed in {tts_synth_duration:.4f}s for {len(text)} chars")

            if audio_data:  # Only queue if synthesis was successful
                await self.queue.put(audio_data)

                # Start prefetching the next chunk if there's more in the buffer
                # and we don't already have a synthesis task running
                if self.buffer and self.first_chunk_sent and (not self.synthesis_task or self.synthesis_task.done()):
                    # Check if we can extract the next chunk
                    next_chunk = None
                    splits = list(self.sentence_endings.finditer(self.buffer))
                    if splits:
                        last_split = splits[-1].end()
                        next_chunk = self.buffer[:last_split]
                    elif len(self.buffer) > self.min_chunk_len:
                        next_chunk = self.buffer

                    if next_chunk:
                        # Prefetch the next chunk, but don't modify the buffer yet
                        self.synthesis_task = asyncio.create_task(self._prefetch_next_chunk(next_chunk))
            else:
                logger.warning("TTS synthesis returned no data, skipping queue.")
        except Exception as e:
            logger.error(f"Error during speech synthesis: {e}")
            logger.error(traceback.format_exc())

    async def _monitor_playback(self, segment_index):
        """Monitor audio playback without blocking the main processing loop"""
        try:
            # Log the start of monitoring
            logger.debug(f"Started monitoring playback for segment {segment_index}")

            # Wait for the audio to finish playing or for the player to stop
            while self.voice_client.is_playing() and self.running:
                await asyncio.sleep(0.02)  # Short sleep to avoid CPU hogging

            # Log completion of playback
            if self.running:
                logger.debug(f"Playback completed for segment {segment_index}")
            else:
                logger.debug(f"Monitoring stopped for segment {segment_index} (player not running)")

        except Exception as e:
            logger.error(f"Error monitoring playback for segment {segment_index}: {e}")

    async def _prefetch_next_chunk(self, text):
        """Prefetch synthesis for the next chunk while current audio is playing"""
        try:
            logger.info(f"Prefetching next chunk: '{text[:30]}...' ({len(text)} chars)")

            # Use the async version directly
            prefetched_audio = await synthesize_speech(text)

            # Store the prefetched result in a special queue entry
            if prefetched_audio:
                await self.queue.put(("prefetched", text, prefetched_audio))

        except Exception as e:
            logger.error(f"Error prefetching synthesis: {e}")
            # We don't propagate the error since this is just an optimization

    async def _player_loop(self):
        """Main player loop that processes the audio queue"""
        segment_index = 0

        while self.running:
            try:
                # Wait for the next audio chunk
                queue_item = await self.queue.get()

                # Handle prefetched data
                if isinstance(queue_item, tuple) and queue_item[0] == "prefetched":
                    _, prefetched_text, _ = queue_item  # We don't need the audio data anymore
                    logger.info(f"Received prefetched audio for '{prefetched_text[:30]}...'")

                    # Remove the corresponding text from buffer if it's still there
                    if self.buffer.startswith(prefetched_text):
                        self.buffer = self.buffer[len(prefetched_text):]

                    # Use the prefetched audio directly
                    self.queue.task_done()
                    continue

                # None is our sentinel value to indicate we're done
                if queue_item is None:
                    logger.info(f"Player loop finished")
                    self.queue.task_done()
                    
                    # Return Luna to idle expression after speaking
                    if self.enable_vts_sync:
                        async def return_to_idle_task():
                            try:
                                # Small delay to ensure the last expression was displayed
                                await asyncio.sleep(0.5)
                                import vtube_studio_client
                                await vtube_studio_client.return_to_idle()
                                logger.info("🎭 Luna returned to idle expression")
                            except Exception as e:
                                logger.error(f"❌ Error returning Luna to idle: {e}")
                        
                        asyncio.create_task(return_to_idle_task())
                    
                    if self.after_play_callback:
                        # Use call_soon_threadsafe if callback interacts with discord.py
                        asyncio.get_event_loop().call_soon_threadsafe(self.after_play_callback, None)
                    break

                # Use regular audio data
                audio_data = queue_item

                segment_index += 1
                logger.info(f"Starting playback for segment {segment_index}")

                # Create a buffer and play the audio
                buffer_start_time = time.monotonic()
                buffer = io.BytesIO(audio_data)
                buffer.seek(0)
                buffer_end_time = time.monotonic()
                logger.debug(f"⏱️ [DISCORD-TIMING] Buffer creation took {buffer_end_time - buffer_start_time:.4f}s")

                # --- Start: Use asyncio.Event for reliable sequencing ---
                if not self.running:
                    logger.info("Player loop: Not running, breaking before playback.")
                    self.queue.task_done() # Mark task done before breaking
                    break

                # Create audio source using FFmpegOpusAudio
                ffmpeg_start_time = time.monotonic()
                source = discord.FFmpegOpusAudio(
                    buffer,
                    pipe=True,
                    bitrate=96,
                    options='-loglevel warning -af aresample=async=1000'
                )
                ffmpeg_end_time = time.monotonic()
                logger.debug(f"⏱️ [DISCORD-TIMING] FFmpegOpusAudio creation took {ffmpeg_end_time - ffmpeg_start_time:.4f}s")

                # Start local audio playback for VTube Studio synchronization (if enabled)
                local_audio_task = None
                if self.enable_vts_sync:
                    # Start local audio playback as a concurrent task
                    local_audio_task = asyncio.create_task(self._play_audio_locally(audio_data))
                    logger.info("🎭 Started local audio playback task for VTube Studio synchronization")

                # Clear the event before starting playback for this segment
                # Create a future in the correct loop to signal playback completion
                loop = self.voice_client.loop
                playback_done_future = loop.create_future()

                # Define the after callback function to set the future's result
                def after_callback(error):
                    # This runs in discord.py's thread
                    if error:
                        logger.error(f"Playback error on segment {segment_index}: {error}")
                        # Set future result safely from the other thread
                        loop.call_soon_threadsafe(playback_done_future.set_result, False) # Indicate error
                    else:
                        logger.debug(f"Playback finished callback received for segment {segment_index}")
                        # Set future result safely from the other thread
                        loop.call_soon_threadsafe(playback_done_future.set_result, True) # Indicate success

                    # Call the external callback if provided (using threadsafe call)
                    # Pass the error argument to the external callback
                    if self.after_play_callback:
                        # Ensure this callback is also scheduled safely if it interacts with asyncio/discord.py
                        loop.call_soon_threadsafe(self.after_play_callback, error) # Pass error directly

                # Play the audio on Discord
                play_start_time = time.monotonic()
                self.voice_client.play(source, after=after_callback)
                play_end_time = time.monotonic()
                logger.debug(f"⏱️ [DISCORD-TIMING] Voice client play() call took {play_end_time - play_start_time:.4f}s")

                # Start prefetching next chunk while this one plays (if applicable)
                if self.buffer and (not self.synthesis_task or self.synthesis_task.done()):
                    next_chunk = None
                    splits = list(self.sentence_endings.finditer(self.buffer))
                    if splits:
                        last_split = splits[-1].end()
                        next_chunk = self.buffer[:last_split]
                    elif len(self.buffer) > self.min_chunk_len:
                        next_chunk = self.buffer
                    if next_chunk:
                         logger.debug(f"Starting prefetch task for next chunk while segment {segment_index} plays.")
                         self.synthesis_task = asyncio.create_task(self._prefetch_next_chunk(next_chunk))

                # Wait for the future to be set by the after_callback
                logger.debug(f"Waiting for playback completion future for segment {segment_index}...")
                try:
                    success = await playback_done_future
                    if not success:
                         logger.warning(f"Playback for segment {segment_index} reported an error.")
                    
                    # Also wait for local audio task to complete if it exists
                    if local_audio_task:
                        try:
                            await local_audio_task
                            logger.info("🎭 Local audio playback task completed successfully")
                        except Exception as e:
                            logger.warning(f"🎭 Local audio playback task failed: {e}")
                    
                    # Future completed, proceed to next segment
                except asyncio.CancelledError:
                    logger.warning(f"Playback future for segment {segment_index} cancelled.")
                    # Cancel local audio task if running
                    if local_audio_task and not local_audio_task.done():
                        local_audio_task.cancel()
                    break # Exit loop if cancelled
                logger.info(f"Playback completed for segment {segment_index}. Proceeding to next item.")
                # --- End: Use asyncio.Event for reliable sequencing ---

                self.queue.task_done()

            except Exception as e:
                logger.error(f"Error in player loop: {e}", exc_info=True)
                # Ensure task_done is called even on error to prevent deadlocks
                try:
                    self.queue.task_done()
                except ValueError:  # If task_done() already called or queue empty
                    pass

# Make sure to add this to main.py to clean up the engine on shutdown
async def close_tts_engine():
    """Shut down the TTS engine when the bot is shutting down"""
    global engine
    try:
        with engine_lock:
            if engine:
                logger.info("Shutting down KokoroEngine...")
                engine.shutdown()
                engine = None
                logger.info("KokoroEngine shut down successfully")
    except Exception as e:
        logger.error(f"Error shutting down KokoroEngine: {e}")
        logger.error(traceback.format_exc())

# ---------------------------------------------------------------------------
# PUBLIC HELPER FOR STARTUP WARMUP
# ---------------------------------------------------------------------------

def prewarm_tts_engine():
    """Synchronously initialise (and internally pre-warm) the KokoroEngine.

    Call this at application startup so the first user utterance is not slowed
    down by engine loading.  Safe to call multiple times – subsequent calls
    return immediately.
    Returns
    -------
    bool
        True if the engine is ready, False otherwise.
    """
    if engine is not None:
        logger.info("KokoroEngine already initialised – prewarm skipped.")
        return True

    logger.info("Pre-warming KokoroEngine at startup…")
    return initialize_engine()