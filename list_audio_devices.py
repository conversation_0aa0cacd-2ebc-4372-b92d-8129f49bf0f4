#!/usr/bin/env python3
"""
List all available audio devices to help configure VTube Studio TTS routing
"""

import pyttsx3

def list_audio_devices():
    """List all available audio devices and TTS voices"""
    print("🎧 Audio Device and Voice Detection")
    print("=" * 50)
    
    try:
        # Initialize TTS engine
        engine = pyttsx3.init()
        
        # List available voices
        print("🎤 Available TTS Voices:")
        print("-" * 30)
        voices = engine.getProperty('voices')
        if voices:
            for i, voice in enumerate(voices):
                name = voice.name if hasattr(voice, 'name') else 'Unknown'
                id_str = voice.id if hasattr(voice, 'id') else 'Unknown'
                print(f"{i+1}. {name}")
                print(f"   ID: {id_str}")
                print()
        else:
            print("No voices found")
        
        # Current voice
        current_voice = engine.getProperty('voice')
        print(f"🔊 Current Voice ID: {current_voice}")
        
        # Try to get audio device info (this is limited with pyttsx3)
        print("\n🔧 TTS Engine Info:")
        print("-" * 20)
        print(f"Engine: {type(engine).__name__}")
        
        # Check system default audio device using Windows API
        try:
            import win32api
            import win32con
            print("\n🖥️ Windows Audio Devices:")
            print("-" * 25)
            # This is a simplified approach - full enumeration requires more complex code
            print("Note: pyttsx3 uses the system default audio device")
            print("To route to VTube Studio, configure this in:")
            print("1. Windows Sound Settings (set default device)")
            print("2. Beacn Mix Create routing")
            print("3. VTube Studio microphone input")
        except ImportError:
            print("Windows audio enumeration not available")
        
        engine.stop()
        
    except Exception as e:
        print(f"Error: {e}")

def test_beacn_routing():
    """Provide guidance for Beacn Mix Create routing"""
    print("\n🎛️ Beacn Mix Create Configuration Guide")
    print("=" * 45)
    print()
    print("Since you're using Beacn Mix Create, here's how to route Luna's TTS:")
    print()
    print("1. 📥 In Beacn Mix Create:")
    print("   - Find the route where Luna's TTS is currently playing")
    print("   - Create a new output route or use an existing one")
    print("   - Route Luna's audio to a virtual input that VTube Studio can use")
    print()
    print("2. 🎤 In VTube Studio:")
    print("   - Settings → Microphone")
    print("   - Set input to the Beacn route/device you configured")
    print("   - Adjust sensitivity for lip-sync")
    print()
    print("3. 🔧 Alternative - Change System Default:")
    print("   - Set your system default audio device to one that routes to VTube Studio")
    print("   - This will make Luna's TTS automatically go to the right place")
    print()
    print("💡 Common Beacn device names to try:")
    print("   - 'Beacn Mix'")
    print("   - 'Beacn Virtual Audio'") 
    print("   - 'Beacn Output 1' (or 2, 3, etc.)")
    print("   - Check your Beacn settings for the exact names")

if __name__ == "__main__":
    list_audio_devices()
    test_beacn_routing() 