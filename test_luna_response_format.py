#!/usr/bin/env python3
"""
Test script to identify <PERSON>'s response formatting issues.
This script will help us understand:
1. Why <PERSON> is adding "<PERSON>:" prefixes to her responses
2. Why responses are being duplicated
3. How the training data format differs from Discord implementation
"""

import asyncio
import logging
import sys
import os
import time
from typing import Dict, List, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_training_data_format():
    """Test how training data was formatted vs current Discord implementation"""
    print("\n=== TRAINING DATA FORMAT ANALYSIS ===")
    
    # Example from your training data
    training_example = {
        "messages": [
            {"role": "user", "content": "EZ MONEY shards are MAX PRICE rn glad we got that low IQ cheeseball to stop min listing caught red handed XD"},
            {"role": "assistant", "content": "We'll see if the market stays this way I'm pretty sure it's cause vell was today cause how tf does the market go from 975k to 1.1m in one day"}
        ]
    }
    
    print("Training data format:")
    for msg in training_example["messages"]:
        print(f"  {msg['role']}: {msg['content']}")
    
    print("\nKey observations:")
    print("1. Training data has NO name prefixes like 'Gavin:' or 'Luna:'")
    print("2. User messages are just the raw content")
    print("3. Assistant messages are just Luna's response without 'Luna:' prefix")
    print("4. The model was trained to respond directly without adding speaker names")

def test_current_discord_format():
    """Test how Discord messages are currently being formatted"""
    print("\n=== CURRENT DISCORD FORMAT ANALYSIS ===")
    
    # Simulate current Discord message processing
    user_message = "Hi, Luna."
    user_display_name = "Gavin"
    
    # Current format (from processing.py line 1202)
    current_format = f"{user_display_name}: {user_message}"
    gemma3_format = f"<start_of_turn>user\n{current_format}<end_of_turn>"
    
    print("Current Discord format:")
    print(f"  Raw user input: '{user_message}'")
    print(f"  Formatted for LLM: '{current_format}'")
    print(f"  Gemma3 format: '{gemma3_format}'")
    
    print("\nProblem identified:")
    print("1. Discord adds 'Gavin:' prefix to user messages")
    print("2. But training data had NO such prefixes")
    print("3. This mismatch causes Luna to mirror the format and respond with 'Luna:'")

def test_gemma3_format_issues():
    """Test Gemma3 format handling"""
    print("\n=== GEMMA3 FORMAT ISSUES ===")
    
    # From web search: Gemma3 doesn't properly handle system prompts
    print("Gemma3 system prompt issues:")
    print("1. Gemma3 'fakes' system prompts by prepending them to user messages")
    print("2. This can cause format confusion and repetitive responses")
    print("3. The model may echo the system prompt or format instructions")
    
    # Current system prompt (luna_prompt.txt)
    system_prompt = "You are Luna, created by Gavin. A discord chatbot."
    
    print(f"\nCurrent system prompt: '{system_prompt}'")
    print("Issues:")
    print("1. Very basic system prompt doesn't match training style")
    print("2. Doesn't establish the casual, lowercase personality from training")
    print("3. Missing context about Discord conversation format")

def test_response_duplication():
    """Test why responses are being duplicated"""
    print("\n=== RESPONSE DUPLICATION ANALYSIS ===")
    
    # Simulate the current response flow
    print("Current response flow:")
    print("1. LLM generates response")
    print("2. Response goes through remove_emojis() cleaning")
    print("3. Response sent to Discord channel")
    print("4. Response logged to transcript with 'Luna:' prefix")
    
    # The duplication issue from your example:
    example_response = "Hi Gavin! How are you today?"
    
    print(f"\nExample response: '{example_response}'")
    print("Duplication sources:")
    print("1. LLM might be generating 'Luna: Hi Gavin! How are you today?'")
    print("2. Then Discord logs it as 'Luna: Luna: Hi Gavin! How are you today?'")
    print("3. Or the response is being sent twice through different code paths")

def test_solution_approach():
    """Test the solution approach"""
    print("\n=== SOLUTION APPROACH ===")
    
    print("Required fixes:")
    print("1. REMOVE name prefixes from user messages sent to LLM")
    print("2. Update system prompt to match training personality")
    print("3. Add response filtering to remove any 'Luna:' prefixes")
    print("4. Ensure single response path to prevent duplication")
    
    print("\nTraining-compatible format:")
    user_input = "Hi, Luna."
    clean_format = user_input  # No "Gavin:" prefix
    gemma3_clean = f"<start_of_turn>user\n{clean_format}<end_of_turn>"
    
    print(f"  User input: '{user_input}'")
    print(f"  Clean format: '{clean_format}'")
    print(f"  Gemma3 format: '{gemma3_clean}'")
    
    print("\nExpected Luna response (training style):")
    print("  'hey! doing pretty good, how about you? :)'")
    print("  (lowercase, casual, with emoticons - matching training data)")

def test_system_prompt_fix():
    """Test improved system prompt"""
    print("\n=== SYSTEM PROMPT FIX ===")
    
    current_prompt = "You are Luna, created by Gavin. A discord chatbot."
    
    improved_prompt = """You are Luna, created by Gavin. You're a casual, sassy Discord chatbot with personality.

Key traits:
- Respond in lowercase with casual language and slang
- Use emoticons like :) :P XD when appropriate  
- Be witty and playful, not formal
- Keep responses conversational and natural
- Don't add speaker names or prefixes to your responses
- Respond directly as if you're speaking, not narrating

Example style: "hey! doing pretty good, how about you? :)" """
    
    print("Current system prompt:")
    print(f"  '{current_prompt}'")
    print(f"  Length: {len(current_prompt)} chars")
    
    print("\nImproved system prompt:")
    print(f"  '{improved_prompt}'")
    print(f"  Length: {len(improved_prompt)} chars")
    
    print("\nImprovements:")
    print("1. Matches training data personality (casual, lowercase)")
    print("2. Explicitly tells Luna not to add speaker prefixes")
    print("3. Provides style examples")
    print("4. Emphasizes natural conversation flow")

if __name__ == "__main__":
    print("Luna Response Format Analysis")
    print("=" * 50)
    
    test_training_data_format()
    test_current_discord_format()
    test_gemma3_format_issues()
    test_response_duplication()
    test_solution_approach()
    test_system_prompt_fix()
    
    print("\n" + "=" * 50)
    print("SUMMARY OF ISSUES FOUND:")
    print("1. Training data has no name prefixes, but Discord adds them")
    print("2. System prompt doesn't match training personality")
    print("3. Gemma3 format issues with system prompt handling")
    print("4. Response duplication in logging/sending")
    print("5. Missing response filtering for unwanted prefixes")
    
    print("\nNEXT STEPS:")
    print("1. Update system prompt to match training style")
    print("2. Remove name prefixes from messages sent to LLM")
    print("3. Add response filtering to remove 'Luna:' prefixes")
    print("4. Test with actual Discord integration")
