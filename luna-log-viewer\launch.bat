@echo off
setlocal ENABLEDELAYEDEXPANSION
cd /d "%~dp0"

rem Default Electron Builder install paths for NSIS one-click installers
set "APPNAME=Luna Log Viewer.exe"
set "APPDIR1=%LOCALAPPDATA%\Programs\Luna Log Viewer"
set "APPDIR2=%LOCALAPPDATA%\Programs\luna-log-viewer"
set "APPPATH1=%APPDIR1%\%APPNAME%"
set "APPPATH2=%APPDIR2%\%APPNAME%"

if exist "%APPPATH1%" (
  start "" "%APPPATH1%"
  exit /b 0
) else if exist "%APPPATH2%" (
  start "" "%APPPATH2%"
  exit /b 0
) else (
  echo The packaged app is not installed yet.
  echo Expected at either:
  echo   %APPPATH1%
  echo   %APPPATH2%
  echo.
  echo Run build_installer.bat in this folder, then re-run this launcher.
  pause
  exit /b 1
)
