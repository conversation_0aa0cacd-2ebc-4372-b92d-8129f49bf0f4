#!/usr/bin/env python3
"""
Comprehensive integration test for Pokemon type effectiveness system
"""

import re
from rag_type_chart import get_type_effectiveness_context, needs_type_effectiveness_context

def test_type_effectiveness_parsing():
    """Test brain parsing logic for TypeChart actions"""
    print("Testing brain parsing logic...")
    
    # Test cases for brain output parsing
    test_cases = [
        "Action: TypeChart Fire",
        "Action: TypeChart Water",
        "Action: TypeChart Electric",
        "Some other text\nAction: TypeChart Dragon\nMore text",
    ]
    
    for raw_decision in test_cases:
        # Simulate the brain parsing logic
        action_match = re.match(r"Action:\s*(\w+)", raw_decision, re.IGNORECASE)
        if action_match:
            action_type = action_match.group(1).strip().lower()
            if action_type == "typechart":
                type_matches = re.findall(r"Action:\s*TypeChart\s+([^\n\.,;]+)", raw_decision, re.IGNORECASE)
                if type_matches:
                    type_name = type_matches[-1].strip()
                    print(f"✅ Raw: '{raw_decision}' → Parsed type: '{type_name}'")
                else:
                    print(f"❌ Failed to parse type from: '{raw_decision}'")
            else:
                print(f"⚠️  Not a TypeChart action: '{action_type}'")
        else:
            print(f"❌ No action found in: '{raw_decision}'")

def test_type_context_retrieval():
    """Test type effectiveness context retrieval"""
    print("\nTesting type effectiveness context retrieval...")
    
    test_cases = [
        ("fire", "What beats fire types?"),
        ("water", "What is water strong against?"),
        ("electric", "What types are electric weak to?"),
        ("dragon", "What beats dragon?"),
        ("invalid", "What beats invalid types?"),
    ]
    
    for type_name, question in test_cases:
        try:
            context = get_type_effectiveness_context(type_name, question)
            if context:
                print(f"✅ {type_name.capitalize()}: {context}")
            else:
                print(f"⚠️  No context for {type_name}")
        except Exception as e:
            print(f"❌ Error with {type_name}: {e}")

def test_question_detection():
    """Test question detection for type effectiveness"""
    print("\nTesting question detection...")
    
    test_questions = [
        "What beats fire types?",
        "What is water weak to?",
        "What types are good against electric?",
        "Tell me about Pikachu",  # Should not trigger
        "What's the weather like?",  # Should not trigger
    ]
    
    for question in test_questions:
        needs_context, detected_type = needs_type_effectiveness_context(question)
        if needs_context:
            print(f"✅ '{question}' → Type: {detected_type}")
        else:
            print(f"⚠️  '{question}' → No type effectiveness needed")

def test_end_to_end_integration():
    """Test the complete integration flow"""
    print("\nTesting end-to-end integration...")
    
    # Simulate a complete flow
    user_question = "What beats fire types?"
    
    # Step 1: Brain would detect this as TypeChart Fire
    raw_brain_decision = "Action: TypeChart Fire"
    
    # Step 2: Parse the brain decision
    type_matches = re.findall(r"Action:\s*TypeChart\s+([^\n\.,;]+)", raw_brain_decision, re.IGNORECASE)
    if type_matches:
        type_name = type_matches[-1].strip().lower()
        print(f"Step 1 - Brain decision parsed: {type_name}")
        
        # Step 3: Get type effectiveness context
        context = get_type_effectiveness_context(type_name, user_question)
        print(f"Step 2 - Context retrieved: {context}")
        
        # Step 4: Simulate context injection into system prompt
        system_prompt = "You are Luna, a helpful Discord bot."
        if context:
            final_system_prompt = system_prompt + f"\n\n[Type Effectiveness: {context}]"
            print(f"Step 3 - Final prompt: {final_system_prompt}")
            
        print("✅ End-to-end integration successful!")
    else:
        print("❌ Failed to parse brain decision")

if __name__ == "__main__":
    print("🔍 Pokemon Type Effectiveness Integration Test")
    print("=" * 50)
    
    test_type_effectiveness_parsing()
    test_type_context_retrieval()
    test_question_detection()
    test_end_to_end_integration()
    
    print("\n🎉 All tests completed!")
