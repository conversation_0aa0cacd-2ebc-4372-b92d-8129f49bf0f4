#!/usr/bin/env python3
"""
Debug Stored Facts
==================
Examines what facts are actually stored in the memory database
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_stored_facts():
    print("🔍 Debugging Stored Facts in Memory Database")
    print("=" * 60)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Connect to database and examine facts
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # Get all facts for user 921637353364287489 (from the logs)
        cursor = conn.execute("""
            SELECT id, content, memory_type, user_id, channel_id, confidence, entities, relationships
            FROM memory_facts 
            WHERE user_id = ?
            ORDER BY confidence DESC, timestamp DESC
        """, (921637353364287489,))
        
        facts = cursor.fetchall()
        print(f"📊 Found {len(facts)} facts for user 921637353364287489:")
        
        for i, fact in enumerate(facts, 1):
            print(f"\n  {i}. ID: {fact[0]}")
            print(f"     Content: '{fact[1]}'")
            print(f"     Type: {fact[2]}")
            print(f"     User ID: {fact[3]}")
            print(f"     Channel: {fact[4]}")
            print(f"     Confidence: {fact[5]}")
            print(f"     Entities: {fact[6]}")
            print(f"     Relationships: {fact[7]}")
        
        # Also check if there are any facts about Luna specifically
        print(f"\n🔍 Searching for facts containing 'Luna':")
        cursor = conn.execute("""
            SELECT id, content, memory_type, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%Luna%'
            ORDER BY confidence DESC
        """)
        
        luna_facts = cursor.fetchall()
        for i, fact in enumerate(luna_facts, 1):
            print(f"  {i}. '{fact[1]}' (User: {fact[3]}, Confidence: {fact[4]})")
        
        # Check facts containing 'purple'
        print(f"\n🟣 Searching for facts containing 'purple':")
        cursor = conn.execute("""
            SELECT id, content, memory_type, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%purple%'
            ORDER BY confidence DESC
        """)
        
        purple_facts = cursor.fetchall()
        for i, fact in enumerate(purple_facts, 1):
            print(f"  {i}. '{fact[1]}' (User: {fact[3]}, Confidence: {fact[4]})")
        
        # Check facts containing 'food' or 'potatoes'
        print(f"\n🥔 Searching for facts containing 'food' or 'potatoes':")
        cursor = conn.execute("""
            SELECT id, content, memory_type, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%food%' OR content LIKE '%potatoes%'
            ORDER BY confidence DESC
        """)
        
        food_facts = cursor.fetchall()
        for i, fact in enumerate(food_facts, 1):
            print(f"  {i}. '{fact[1]}' (User: {fact[3]}, Confidence: {fact[4]})")
        
        # Check facts containing 'block' or 'minecraft'
        print(f"\n🧱 Searching for facts containing 'block' or 'minecraft':")
        cursor = conn.execute("""
            SELECT id, content, memory_type, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%block%' OR content LIKE '%minecraft%' OR content LIKE '%cobblestone%' OR content LIKE '%obsidian%'
            ORDER BY confidence DESC
        """)
        
        block_facts = cursor.fetchall()
        for i, fact in enumerate(block_facts, 1):
            print(f"  {i}. '{fact[1]}' (User: {fact[3]}, Confidence: {fact[4]})")
        
        # Test FTS search directly
        print(f"\n🔍 Testing FTS Search:")
        test_queries = [
            'luna favorite color',
            'luna favorite food', 
            'luna favorite block',
            'purple',
            'color',
            'food',
            'potatoes',
            'minecraft',
            'block'
        ]
        
        for query in test_queries:
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 3
            """, (f'"{query}"',))
            
            results = cursor.fetchall()
            print(f"\n  FTS Query '{query}' -> {len(results)} results:")
            for result in results:
                print(f"    {result[0]}: {result[1]} (conf: {result[2]})")
    
    finally:
        conn.close()

if __name__ == "__main__":
    asyncio.run(debug_stored_facts())