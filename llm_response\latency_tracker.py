"""
Comprehensive Latency Tracking System for Luna Voice-to-Voice Bot

This system tracks every stage of Luna's response pipeline from audio detection
to final speech output, providing detailed performance analysis and bottleneck identification.
"""

import time
import logging
from dataclasses import dataclass, field
from typing import Dict, Optional, List
import uuid
from threading import Lock

logger = logging.getLogger(__name__)

@dataclass
class LatencyMetrics:
    """Complete latency metrics for a single voice interaction turn."""
    
    # Turn identification
    turn_id: str = field(default_factory=lambda: str(uuid.uuid4())[:8])
    user_id: Optional[str] = None
    user_name: Optional[str] = None
    
    # Core pipeline timestamps (monotonic time)
    audio_detected: Optional[float] = None
    transcription_end: Optional[float] = None
    
    # Processing pipeline timestamps
    process_start: Optional[float] = None
    brain_start: Optional[float] = None
    brain_api_start: Optional[float] = None
    brain_api_end: Optional[float] = None
    brain_decision_start: Optional[float] = None
    brain_decision_end: Optional[float] = None
    brain_end: Optional[float] = None
    
    # Decision-making timestamps
    decision_start: Optional[float] = None
    decision_api_start: Optional[float] = None
    decision_api_end: Optional[float] = None
    decision_end: Optional[float] = None
    
    # Web search timestamps
    web_search_start: Optional[float] = None
    web_search_end: Optional[float] = None
    
    # LLM generation timestamps
    llm_api_start: Optional[float] = None
    first_token: Optional[float] = None
    llm_api_end: Optional[float] = None
    
    # TTS pipeline timestamps
    tts_first_chunk: Optional[float] = None
    tts_player_start: Optional[float] = None
    audio_playback_start: Optional[float] = None
    
    # VTube Studio integration timestamps
    emotion_detection_start: Optional[float] = None
    emotion_detection_end: Optional[float] = None
    emotion_context_start: Optional[float] = None
    emotion_context_end: Optional[float] = None
    vts_emotion_start: Optional[float] = None
    vts_emotion_end: Optional[float] = None
    vts_hotkey_start: Optional[float] = None
    vts_api_call_start: Optional[float] = None
    vts_api_call_end: Optional[float] = None
    vts_hotkey_end: Optional[float] = None
    vts_connect_start: Optional[float] = None
    vts_connect_end: Optional[float] = None
    vts_idle_start: Optional[float] = None
    vts_idle_end: Optional[float] = None
    
    # Response metadata
    response_length: Optional[int] = None
    created_at: float = field(default_factory=time.time)

class LatencyTracker:
    """Thread-safe latency tracking system for voice interactions."""
    
    def __init__(self):
        self._current_turn: Optional[LatencyMetrics] = None
        self._lock = Lock()
        self._completed_turns: List[LatencyMetrics] = []
        self._max_history = 50  # Keep last 50 turns for analysis
    
    def start_turn(self, user_id: str = None, user_name: str = None) -> str:
        """Start tracking a new voice interaction turn."""
        with self._lock:
            self._current_turn = LatencyMetrics(
                user_id=user_id,
                user_name=user_name
            )
            logger.info(f"📊 Started latency tracking for turn {self._current_turn.turn_id}")
            return self._current_turn.turn_id
    
    def mark_timestamp(self, event_name: str, timestamp: float = None) -> None:
        """Mark a timestamp for the current turn."""
        if not self._current_turn:
            logger.debug(f"📊 No active turn to mark timestamp: {event_name}")
            return
        
        if timestamp is None:
            timestamp = time.monotonic()
        
        with self._lock:
            if hasattr(self._current_turn, event_name):
                setattr(self._current_turn, event_name, timestamp)
                logger.debug(f"📊 Marked {event_name} at {timestamp:.4f}")
            else:
                logger.warning(f"📊 Unknown event name: {event_name}")
    
    def set_response_length(self, length: int) -> None:
        """Set the response length for analysis."""
        if self._current_turn:
            with self._lock:
                self._current_turn.response_length = length
    
    def complete_turn(self) -> Optional[LatencyMetrics]:
        """Complete the current turn and return the metrics."""
        if not self._current_turn:
            logger.warning("📊 No active turn to complete")
            return None
        
        with self._lock:
            completed_turn = self._current_turn
            self._completed_turns.append(completed_turn)
            
            # Maintain history limit
            if len(self._completed_turns) > self._max_history:
                self._completed_turns.pop(0)
            
            logger.info(f"📊 Completed latency tracking for turn {completed_turn.turn_id}")
            self._current_turn = None
            return completed_turn
    
    def get_current_turn(self) -> Optional[LatencyMetrics]:
        """Get the current turn metrics."""
        return self._current_turn
    
    def get_recent_turns(self, count: int = 10) -> List[LatencyMetrics]:
        """Get the most recent completed turns."""
        with self._lock:
            return self._completed_turns[-count:] if self._completed_turns else []

# Global tracker instance
_tracker: Optional[LatencyTracker] = None
_tracker_lock = Lock()

def get_tracker() -> LatencyTracker:
    """Get or create the global latency tracker."""
    global _tracker
    if _tracker is None:
        with _tracker_lock:
            if _tracker is None:  # Double-check pattern
                _tracker = LatencyTracker()
    return _tracker

def start_latency_tracking(user_id: str = None, user_name: str = None) -> str:
    """Start tracking latency for a new voice interaction."""
    return get_tracker().start_turn(user_id, user_name)

def mark_latency_timestamp(event_name: str, timestamp: float = None) -> None:
    """Mark a timestamp in the current latency tracking turn."""
    get_tracker().mark_timestamp(event_name, timestamp)

def set_response_length(length: int) -> None:
    """Set the response length for the current turn."""
    get_tracker().set_response_length(length)

def complete_latency_tracking() -> Optional[LatencyMetrics]:
    """Complete the current latency tracking turn."""
    return get_tracker().complete_turn()

def get_current_metrics() -> Optional[LatencyMetrics]:
    """Get the current turn metrics."""
    return get_tracker().get_current_turn()

def calculate_duration(start: Optional[float], end: Optional[float]) -> Optional[float]:
    """Calculate duration between two timestamps."""
    if start is not None and end is not None:
        return end - start
    return None

def generate_latency_report(metrics: LatencyMetrics) -> Dict[str, any]:
    """Generate a comprehensive latency analysis report."""
    
    # Core pipeline metrics
    total_response_time = calculate_duration(metrics.audio_detected, metrics.audio_playback_start)
    transcription_to_process = calculate_duration(metrics.transcription_end, metrics.process_start)
    process_to_brain = calculate_duration(metrics.process_start, metrics.brain_start)
    
    # Brain processing metrics
    brain_total = calculate_duration(metrics.brain_start, metrics.brain_end)
    brain_api_duration = calculate_duration(metrics.brain_api_start, metrics.brain_api_end)
    brain_decision_duration = calculate_duration(metrics.brain_decision_start, metrics.brain_decision_end)
    
    # Decision-making metrics
    decision_total = calculate_duration(metrics.decision_start, metrics.decision_end)
    decision_api_duration = calculate_duration(metrics.decision_api_start, metrics.decision_api_end)
    
    # Web search metrics
    web_search_duration = calculate_duration(metrics.web_search_start, metrics.web_search_end)
    
    # LLM generation metrics
    llm_time_to_first_token = calculate_duration(metrics.llm_api_start, metrics.first_token)
    first_token_to_audio = calculate_duration(metrics.first_token, metrics.tts_first_chunk)
    llm_total_generation = calculate_duration(metrics.llm_api_start, metrics.llm_api_end)
    
    # TTS pipeline metrics
    tts_to_playback = calculate_duration(metrics.tts_first_chunk, metrics.audio_playback_start)
    
    # VTube Studio metrics
    emotion_detection_time = calculate_duration(metrics.emotion_detection_start, metrics.emotion_detection_end)
    emotion_context_time = calculate_duration(metrics.emotion_context_start, metrics.emotion_context_end)
    vts_emotion_total = calculate_duration(metrics.vts_emotion_start, metrics.vts_emotion_end)
    vts_hotkey_total = calculate_duration(metrics.vts_hotkey_start, metrics.vts_hotkey_end)
    vts_api_call_time = calculate_duration(metrics.vts_api_call_start, metrics.vts_api_call_end)
    
    return {
        'turn_id': metrics.turn_id,
        'user_id': metrics.user_id,
        'user_name': metrics.user_name,
        'response_length': metrics.response_length,
        'created_at': metrics.created_at,
        
        # Primary metrics (most important for optimization)
        'primary': {
            'total_response_time': total_response_time,
            'brain_processing': brain_total,
            'decision_making': decision_total,
            'web_search_duration': web_search_duration,
            'llm_time_to_first_token': llm_time_to_first_token,
            'first_token_to_audio': first_token_to_audio,
        },
        
        # Detailed breakdown
        'detailed': {
            'transcription_to_process': transcription_to_process,
            'process_to_brain': process_to_brain,
            'brain_api_call': brain_api_duration,
            'brain_decision_processing': brain_decision_duration,
            'decision_api_call': decision_api_duration,
            'llm_total_generation': llm_total_generation,
            'tts_to_playback': tts_to_playback,
        },
        
        # VTube Studio metrics
        'vts': {
            'emotion_detection': emotion_detection_time,
            'emotion_context': emotion_context_time,
            'vts_emotion_total': vts_emotion_total,
            'vts_hotkey_total': vts_hotkey_total,
            'vts_api_call': vts_api_call_time,
        }
    }

def format_latency_report_for_discord(report: Dict[str, any]) -> str:
    """Format latency report for Discord with grouped metrics and optimization guidance."""
    
    def format_time(seconds: Optional[float]) -> str:
        if seconds is None:
            return "N/A"
        if seconds < 0.001:
            return f"{seconds*1000:.1f}μs"
        elif seconds < 1.0:
            return f"{seconds*1000:.0f}ms"
        else:
            return f"{seconds:.2f}s"
    
    def get_performance_emoji(seconds: Optional[float], good_threshold: float, ok_threshold: float) -> str:
        if seconds is None:
            return "❓"
        elif seconds <= good_threshold:
            return "🟢"
        elif seconds <= ok_threshold:
            return "🟡"
        else:
            return "🔴"
    
    primary = report['primary']
    detailed = report['detailed']
    vts = report['vts']
    
    # Build the report
    lines = [
        f"**🎤 Luna Latency Report - Turn {report['turn_id']}**",
        f"User: {report['user_name'] or 'Unknown'} ({report['user_id'] or 'N/A'})",
        f"Response: {report['response_length'] or 'N/A'} chars",
        "",
        "**📊 KEY PERFORMANCE METRICS**"
    ]
    
    # Primary metrics with performance indicators
    total_emoji = get_performance_emoji(primary['total_response_time'], 2.0, 4.0)
    brain_emoji = get_performance_emoji(primary['brain_processing'], 0.5, 1.0)
    decision_emoji = get_performance_emoji(primary['decision_making'], 1.0, 2.0)
    search_emoji = get_performance_emoji(primary['web_search_duration'], 1.0, 3.0)
    ttft_emoji = get_performance_emoji(primary['llm_time_to_first_token'], 3.0, 6.0)
    audio_emoji = get_performance_emoji(primary['first_token_to_audio'], 0.5, 1.0)
    
    lines.extend([
        f"{total_emoji} **Total Response**: {format_time(primary['total_response_time'])}",
        f"{brain_emoji} **Brain Processing**: {format_time(primary['brain_processing'])}",
        f"{decision_emoji} **Decision Making**: {format_time(primary['decision_making'])}",
        f"{search_emoji} **Web Search**: {format_time(primary['web_search_duration'])}",
        f"{ttft_emoji} **LLM Time-to-First-Token**: {format_time(primary['llm_time_to_first_token'])}",
        f"{audio_emoji} **First Token → Audio**: {format_time(primary['first_token_to_audio'])}",

        "",
        "**🔧 DETAILED BREAKDOWN**"
    ])
    
    # Detailed metrics
    lines.extend([
        f"• Transcription → Process: {format_time(detailed['transcription_to_process'])}",
        f"• Process → Brain: {format_time(detailed['process_to_brain'])}",
        f"• Brain API Call: {format_time(detailed['brain_api_call'])}",
        f"• Brain Decision: {format_time(detailed['brain_decision_processing'])}",
        f"• Decision API Call: {format_time(detailed['decision_api_call'])}",
        f"• LLM Total Generation: {format_time(detailed['llm_total_generation'])}",
        f"• TTS → Playback: {format_time(detailed['tts_to_playback'])}",
    ])
    
    # VTube Studio metrics (if any)
    if any(v is not None for v in vts.values()):
        lines.extend([
            "",
            "**🎭 VTUBE STUDIO METRICS**",
            f"• Emotion Detection: {format_time(vts['emotion_detection'])}",
            f"• Emotion Context: {format_time(vts['emotion_context'])}",
            f"• VTS Emotion Total: {format_time(vts['vts_emotion_total'])}",
            f"• VTS Hotkey Total: {format_time(vts['vts_hotkey_total'])}",
            f"• VTS API Call: {format_time(vts['vts_api_call'])}"
        ])
    
    # Optimization guidance
    lines.extend([
        "",
        "**💡 OPTIMIZATION GUIDANCE**"
    ])
    
    bottlenecks = []
    if primary['total_response_time'] and primary['total_response_time'] > 4.0:
        bottlenecks.append("🔴 **Total response time is slow** - Check individual components")
    
    if primary['llm_time_to_first_token'] and primary['llm_time_to_first_token'] > 6.0:
        bottlenecks.append("🔴 **LLM TTFT is the primary bottleneck** - Consider model optimization")
    elif primary['llm_time_to_first_token'] and primary['llm_time_to_first_token'] > 3.0:
        bottlenecks.append("🟡 **LLM TTFT is slow** - Monitor for improvements")
    
    if primary['brain_processing'] and primary['brain_processing'] > 1.0:
        bottlenecks.append("🟡 **Brain processing is slow** - Check LLM Brain API")
    
    if primary['decision_making'] and primary['decision_making'] > 2.0:
        bottlenecks.append("🟡 **Decision making is slow** - Check decision API")
    
    if primary['web_search_duration'] and primary['web_search_duration'] > 3.0:
        bottlenecks.append("🔴 **Web search is very slow** - Check Google API or network")
    elif primary['web_search_duration'] and primary['web_search_duration'] > 1.0:
        bottlenecks.append("🟡 **Web search is slow** - Consider caching improvements")
    
    if not bottlenecks:
        lines.append("🟢 **Performance looks good!** All metrics within acceptable ranges.")
    else:
        lines.extend(bottlenecks)
    
    return "\n".join(lines)

# Test function for validation
async def test_latency_tracking():
    """Test the latency tracking system with sample data."""
    import asyncio
    
    logger.info("Testing latency tracking system...")
    
    # Start a test turn
    turn_id = start_latency_tracking("test_user", "TestUser")
    
    # Simulate various timestamps
    mark_latency_timestamp("audio_detected")
    await asyncio.sleep(0.01)
    mark_latency_timestamp("transcription_end")
    await asyncio.sleep(0.01)
    mark_latency_timestamp("process_start")
    await asyncio.sleep(0.005)
    mark_latency_timestamp("brain_start")
    mark_latency_timestamp("brain_api_start")
    await asyncio.sleep(0.1)  # Simulate brain API call
    mark_latency_timestamp("brain_api_end")
    mark_latency_timestamp("brain_end")
    await asyncio.sleep(0.005)
    mark_latency_timestamp("decision_start")
    mark_latency_timestamp("decision_api_start")
    await asyncio.sleep(0.05)  # Simulate decision API call
    mark_latency_timestamp("decision_api_end")
    mark_latency_timestamp("decision_end")
    await asyncio.sleep(0.01)

    mark_latency_timestamp("llm_api_start")
    await asyncio.sleep(0.5)  # Simulate LLM processing
    mark_latency_timestamp("first_token")
    await asyncio.sleep(0.3)  # Simulate more LLM generation
    mark_latency_timestamp("llm_api_end")
    await asyncio.sleep(0.01)
    mark_latency_timestamp("tts_first_chunk")
    await asyncio.sleep(0.05)
    mark_latency_timestamp("audio_playback_start")
    
    # Set response metadata
    set_response_length(150)
    
    # Complete the turn
    metrics = complete_latency_tracking()
    
    if metrics:
        report = generate_latency_report(metrics)
        formatted_report = format_latency_report_for_discord(report)
        logger.info(f"Test completed successfully!\n{formatted_report}")
        return True
    else:
        logger.error("Test failed - no metrics returned")
        return False 