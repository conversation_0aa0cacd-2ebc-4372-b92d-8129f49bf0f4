#!/usr/bin/env python3
"""
Test Fact Extraction
====================
Tests the fact extraction process using Qwen3
"""
import asyncio
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.initialization import get_qwen3_client, initialize_qwen3_client

async def test_fact_extraction():
    print("🧠 Testing Fact Extraction Process")
    print("=" * 45)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Test Qwen3 client directly
    print("1️⃣ Testing Qwen3 client initialization:")
    try:
        # Try to initialize Qwen3 client first
        qwen3_client = initialize_qwen3_client()
        if qwen3_client:
            print(f"   ✅ Qwen3 client initialized successfully")
        else:
            print(f"   ❌ Qwen3 client initialization failed")
            return
    except Exception as e:
        print(f"   ❌ Qwen3 initialization error: {e}")
        return
    
    # Test fact extraction manually
    print(f"\n2️⃣ Testing manual fact extraction:")
    
    conversation_text = """user: Luna my favorite color is blue
assistant: Blue? *Please*."""
    
    print(f"   Conversation to analyze:")
    print(f"   {conversation_text}")
    
    extraction_prompt = f"""Analyze this Discord conversation and extract key facts that should be remembered about the user or conversation context.

Conversation:
{conversation_text}

Extract facts in this format:
- [FACTUAL] User prefers X
- [BEHAVIORAL] User typically does Y
- [RELATIONSHIP] User A is friends with User B
- [GAMING] User plays Minecraft and likes building

Only extract clear, important facts. Be concise."""

    print(f"\n   Extraction prompt prepared")
    
    # Test Qwen3 extraction
    try:
        from shared_model import call_qwen3_safe
        
        print(f"   🧠 Calling Qwen3 for fact extraction...")
        
        facts_text = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: call_qwen3_safe(
                qwen3_client,
                extraction_prompt,
                max_tokens=200,
                temperature=0.3
            )
        )
        
        print(f"   ✅ Qwen3 call completed")
        
        if isinstance(facts_text, dict):
            facts_text = facts_text.get('choices', [{}])[0].get('text', '')
        
        print(f"   📝 Extracted facts:")
        print(f"   Raw response: '{facts_text}'")
        
        if facts_text and len(facts_text.strip()) > 10:
            print(f"   ✅ Facts extracted successfully")
            
            # Test fact parsing
            print(f"\n3️⃣ Testing fact parsing:")
            try:
                parsed_facts = ms._parse_extracted_facts(
                    facts_text, 
                    921637353364287489, 
                    "1378098310036066335"
                )
                
                print(f"   ✅ Parsed {len(parsed_facts)} facts:")
                for i, fact in enumerate(parsed_facts, 1):
                    print(f"     {i}. [{fact.memory_type.value}] {fact.content}")
                    print(f"        Confidence: {fact.confidence}")
                    print(f"        Entities: {fact.entities}")
                
                if parsed_facts:
                    # Test storing the facts
                    print(f"\n4️⃣ Testing fact storage:")
                    for i, fact in enumerate(parsed_facts, 1):
                        fact_id = ms.warm_storage.store_fact(fact)
                        print(f"     {i}. Stored fact {fact_id}: {fact.content}")
                    
                    print(f"   ✅ All facts stored successfully")
                    
                    # Test retrieval
                    print(f"\n5️⃣ Testing fact retrieval:")
                    context = await ms.retrieve_relevant_context(
                        query="user favorite color",
                        user_id=921637353364287489,
                        channel_id=None,
                        max_facts=5
                    )
                    
                    if context:
                        print(f"   ✅ Retrieved context: {len(context)} chars")
                        print(f"   Context: {context}")
                    else:
                        print(f"   ❌ No context retrieved")
                else:
                    print(f"   ⚠️ No facts were parsed from the extraction")
                    
            except Exception as e:
                print(f"   ❌ Fact parsing error: {e}")
                
        else:
            print(f"   ❌ No meaningful facts extracted")
            
    except Exception as e:
        print(f"   ❌ Qwen3 extraction error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test the full background processing pipeline
    print(f"\n6️⃣ Testing full background processing:")
    try:
        messages = [
            {"role": "user", "content": "Luna my favorite color is blue", "user_id": 921637353364287489},
            {"role": "assistant", "content": "Blue? *Please*.", "user_id": 123456789}
        ]
        
        print(f"   🔄 Calling _extract_facts_from_conversation directly...")
        await ms._extract_facts_from_conversation(
            messages,
            921637353364287489,
            "1378098310036066335"
        )
        print(f"   ✅ Background extraction completed")
        
    except Exception as e:
        print(f"   ❌ Background processing error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_fact_extraction())