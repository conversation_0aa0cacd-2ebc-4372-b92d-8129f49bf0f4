# Vulkan Dual GPU Configuration for Luna Discord Bot

This document explains the Vulkan GPU configuration for the Luna Discord Bot, which utilizes both NVIDIA and AMD GPUs for optimal performance.

## GPU Assignment

The system is configured to use two GPUs through the Vulkan backend:

### Primary GPU (Device 0): NVIDIA RTX 4070
- **Purpose**: Main response model processing
- **Model**: Primary Luna conversation model
- **Usage**: Heavy computational workloads for chat responses
- **Backend**: Vulkan

### Secondary GPU (Device 1): AMD RX 6650XT  
- **Purpose**: Decision making and utility models
- **Models**: 
  - Gemma 3-4B decision model
  - Brain/reasoning model
- **Usage**: Quick decision processing, intent detection
- **Backend**: Vulkan

## Configuration Files

### Environment Variables (`local.env`)
```bash
# Vulkan GPU Configuration
GGML_BACKEND=vulkan
GGML_VK_VISIBLE_DEVICES=0,1
GGML_VK_FORCE_MAX_ALLOCATION_SIZE=2147483648
GGML_VK_SUBALLOCATION_BLOCK_SIZE=1073741824
```

### Python Configuration
The GPU assignment is configured in the following files:

1. **`llm_response/initialization.py`**: Main response model (RTX 4070)
   ```python
   main_gpu=0  # RTX 4070 for primary model
   ```

2. **`llm_response/decision.py`**: Decision model (RX 6650XT)
   ```python
   main_gpu=1  # RX 6650XT for decision model
   ```

3. **`llm_brain.py`**: Brain model (RX 6650XT)
   ```python
   main_gpu=1  # RX 6650XT for brain model
   ```

## Benefits of Vulkan Dual GPU Setup

### Performance Advantages
- **Parallel Processing**: Decision making runs on RX 6650XT while main responses use RTX 4070
- **Reduced Latency**: Dedicated GPU for quick decision processing
- **Better Resource Utilization**: Both GPUs are fully utilized

### Memory Management
- **Isolated Memory**: Each model uses dedicated GPU memory
- **No Memory Conflicts**: Models don't compete for the same VRAM
- **Optimal Allocation**: Each GPU optimized for its specific workload

## Troubleshooting

### Device Detection Issues
If GPUs are not detected in the correct order:

1. Check device visibility:
   ```bash
   # List available Vulkan devices
   vulkaninfo --summary
   ```

2. Verify environment variables are loaded:
   ```python
   import os
   print(os.environ.get('GGML_VK_VISIBLE_DEVICES'))
   ```

3. Force device order if needed:
   ```bash
   export GGML_VK_VISIBLE_DEVICES="0,1"
   ```

### Performance Optimization
- **RTX 4070**: Optimized for large model inference with tensor cores
- **RX 6650XT**: Optimized for parallel decision processing with compute units
- **Memory Allocation**: Pre-allocated to avoid runtime allocation overhead

### Fallback Configuration
If Vulkan setup fails, the system will fall back to:
1. Single GPU Vulkan (primary device)
2. CUDA backend (if available)
3. CPU processing (emergency fallback)

## Migration Notes

### From CUDA to Vulkan
- All CUDA-specific environment variables removed
- GPU device assignment maintained but using Vulkan device IDs
- Performance characteristics may differ slightly
- Memory usage patterns optimized for Vulkan

### Compatibility
- **NVIDIA RTX 4070**: Full Vulkan 1.3+ support
- **AMD RX 6650XT**: Full Vulkan 1.3+ support with RDNA2 optimizations
- **Cross-vendor**: Mixed GPU vendors supported through Vulkan

## Performance Monitoring

Monitor GPU utilization:
```bash
# NVIDIA GPU monitoring
nvidia-smi

# AMD GPU monitoring  
radeontop

# General Vulkan monitoring
vulkaninfo --summary
```

This configuration provides optimal performance for Luna's conversational AI workloads while maintaining efficient resource utilization across both GPUs. 