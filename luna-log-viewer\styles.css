/* Ensure the app uses viewport height and page itself doesn't scroll */
html, body {
  height: 100%;
  overflow: hidden; /* prevent whole-page scroll */
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* contain scroll inside content area */
}
/* Enhanced Dark Theme Variables */
:root {
  /* Core color palette - refined dark theme */
  --bg-primary: #0a0a0b;
  --bg-secondary: #111113;
  --bg-tertiary: #1a1a1c;
  --bg-card: #1c1c1e;
  --bg-hover: #242427;
  --bg-active: #2c2c30;
  
  /* Border system */
  --border-subtle: #2a2a2d;
  --border-primary: #36363a;
  --border-hover: #4a4a50;
  --border-focus: #6366f1;
  
  /* Text hierarchy */
  --text-primary: #fafafa;
  --text-secondary: #a1a1aa;
  --text-tertiary: #71717a;
  --text-muted: #52525b;
  
  /* Accent colors */
  --accent-primary: #8b5cf6;
  --accent-hover: #7c3aed;
  --accent-secondary: #06b6d4;
  --accent-tertiary: #10b981;
  
  /* Status colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* Shadow system */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  font-family: var(--font-sans);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  overflow: hidden; /* prevent whole-page scroll; scrolling is inside .log-container */
}

/* App container */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
/* Header - redesigned */
.header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-subtle);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 100;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  gap: var(--spacing-lg);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.luna-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
}

.luna-icon {
  font-size: 2rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.3));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.025em;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.status-dot.connected {
  background: var(--success);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  animation: pulse-success 2s ease-in-out infinite;
}

.status-dot.disconnected {
  background: var(--warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: pulse-warning 2s ease-in-out infinite;
}

@keyframes pulse-success {
  0%, 100% { box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2); }
  50% { box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.4); }
}

@keyframes pulse-warning {
  0%, 100% { box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2); }
  50% { box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.4); }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.luna-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.luna-icon {
    font-size: 1.75rem;
    filter: drop-shadow(0 0 12px rgba(99, 102, 241, 0.6));
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, #8b5cf6, #6366f1);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.status-dot.connected {
    background: var(--success);
    box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
}

.status-dot.disconnected {
    background: var(--warning);
    box-shadow: 0 0 12px rgba(245, 158, 11, 0.6);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.luna-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-right: 1rem;
    padding-right: 1rem;
    border-right: 1px solid var(--border);
}

.log-stats {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

/* Buttons */
.btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    background: transparent;
    color: inherit;
}

.btn-primary {
    background: var(--accent);
    color: white;
    border: 1px solid var(--accent);
}

.btn-primary:hover {
    background: var(--accent-hover);
    border-color: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--border-hover);
    border-color: var(--border-hover);
}

.btn-ghost {
    color: var(--text-muted);
    border: 1px solid transparent;
}

.btn-ghost:hover {
    background: rgba(55, 65, 81, 0.5);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success);
    color: white;
    border: 1px solid var(--success);
}

.btn-success:hover:not(:disabled) {
    background: #059669;
    border-color: #059669;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-danger {
    background: var(--error);
    color: white;
    border: 1px solid var(--error);
}

.btn-danger:hover:not(:disabled) {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Tab navigation */
/* Navigation tabs - redesigned */
.tab-nav {
  display: flex;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-subtle);
  padding: 0 var(--spacing-xl);
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
}

.tab-nav::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  white-space: nowrap;
}

.tab-btn::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--accent-primary);
  transition: var(--transition-normal);
  transform: translateX(-50%);
    gap: 0.5rem;
    padding: 1rem 1.25rem;
    background: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 600;
}

.tab-btn:hover {
    color: var(--text-primary);
    background: rgba(55, 65, 81, 0.3);
}

.tab-btn.active {
    color: var(--accent);
    border-bottom-color: var(--accent);
    background: transparent;
}

.tab-icon {
    font-size: 1.1rem;
}

.tab-count {
    background: rgba(99, 102, 241, 0.15);
    color: var(--accent);
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    min-width: 20px;
    text-align: center;
}

.tab-btn.active .tab-count {
    background: rgba(99, 102, 241, 0.3);
    color: white;
}

/* Content area (override earlier base) */
.content {
    flex: 1;
    overflow: hidden; /* contain scroll inside tab panes */
    position: relative;
    display: flex;
    flex-direction: column;
}

.tab-content {
    display: none;
    flex-direction: column;
    height: 100%;
    flex: 1;
    min-height: 0; /* allow child to scroll */
}

.tab-content.active {
    display: flex;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    background: rgba(30, 41, 59, 0.6);
    border-bottom: 1px solid var(--border);
}

.tab-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.filters {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filters select,
.filters input {
    padding: 0.625rem 1rem;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid var(--border);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 160px;
    transition: var(--transition);
}

.filters input {
    min-width: 240px;
}

.filters select:focus,
.filters input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15);
}

/* Log container */
/* Content area - redesigned */
.content {
  flex: 1;
  overflow: hidden; /* contain scroll inside tab panes */
  position: relative;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  min-height: 0; /* required for inner flex children to scroll */
}

.tab-content {
  display: none;
  flex-direction: column;
  height: 100%;
  flex: 1; /* take available space under header/nav */
  min-height: 0; /* allow child to scroll */
  animation: fadeIn 0.2s ease-out;
}

.tab-content.active {
  display: flex;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-subtle);
  background: var(--bg-secondary);
  gap: var(--spacing-md);
  position: sticky; /* keep filters visible while scrolling */
  top: 0;
  z-index: 10;
}

.tab-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filters select,
.filters input {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition-fast);
}

.filters select:focus,
.filters input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.log-container {
  overflow: auto; /* scroll inside the tab */
  padding: var(--spacing-xl);
  scroll-behavior: smooth;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  background: var(--bg-primary);
  flex: 1;
  min-height: 0; /* enable scrolling when inside flex */
}

.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

.log-container::-webkit-scrollbar-thumb {
  background: var(--border-subtle);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

.log-container::-webkit-scrollbar {
    width: 10px;
}

.log-container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
    border: 2px solid var(--bg-secondary);
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: var(--accent);
}

/* Empty state */
/* Empty state - redesigned */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);
  text-align: center;
  padding: var(--spacing-xl);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
  filter: grayscale(1);
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.empty-state p {
  font-size: 0.875rem;
  max-width: 400px;
  line-height: 1.6;
  color: var(--text-tertiary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.3;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
}

.empty-state p {
    font-size: 1rem;
    max-width: 500px;
    line-height: 1.6;
}

/* Log entries */
/* Log entries - redesigned */
.log-entry {
  background: var(--bg-card);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
  animation: slideInUp 0.3s ease-out;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.log-entry::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--border-subtle);
  transition: var(--transition-normal);
}

.log-entry:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.log-entry:hover::before {
  background: var(--accent-primary);
}

.log-entry.error::before {
  background: var(--error);
}

.log-entry.warn::before {
  background: var(--warning);
}

.log-entry.info::before {
  background: var(--info);
}

.log-entry.debug::before {
  background: var(--accent-secondary);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.log-level {
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.75rem;
  border-radius: 2rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid;
}

.log-level.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border-color: rgba(59, 130, 246, 0.3);
}

.log-level.warn {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
  border-color: rgba(245, 158, 11, 0.3);
}

.log-level.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
  border-color: rgba(239, 68, 68, 0.3);
}

.log-level.debug {
  background: rgba(6, 182, 212, 0.1);
  color: var(--accent-secondary);
  border-color: rgba(6, 182, 212, 0.3);
}

.log-timestamp {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
  font-family: var(--font-mono);
}

.log-source {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-xs);
}

.log-message {
  font-size: 0.875rem;
  color: var(--text-primary);
  line-height: 1.6;
  margin-bottom: var(--spacing-sm);
  word-break: break-word;
}

.log-details {
  background: var(--bg-hover);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-sm);
  font-family: var(--font-mono);
  font-size: 0.8125rem;
  color: var(--text-secondary);
  overflow-x: auto;
  line-height: 1.5;
  position: relative;
}

.log-details::before {
  content: 'DETAILS';
  position: absolute;
  top: -0.5rem;
  left: var(--spacing-md);
  background: var(--bg-card);
  color: var(--text-tertiary);
  font-size: 0.625rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  padding: 0 0.5rem;
}

.log-entry:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.log-level {
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-transform: uppercase;
}

.log-level.info {
    background: rgba(14, 165, 233, 0.15);
    color: #0ea5e9;
}

.log-level.warn {
    background: rgba(245, 158, 11, 0.15);
    color: #f59e0b;
}

.log-level.error {
    background: rgba(239, 68, 68, 0.15);
    color: #ef4444;
}

.log-level.debug {
    background: rgba(139, 92, 246, 0.15);
    color: #8b5cf6;
}

.log-timestamp {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 500;
}

.log-source {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--accent);
    margin-bottom: 0.5rem;
}

.log-message {
    font-size: 0.95rem;
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: 0.75rem;
    white-space: pre-wrap;
    word-break: break-word;
}

.log-details {
    background: rgba(30, 41, 59, 0.7);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 0.75rem;
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--text-secondary);
    overflow-x: auto;
    white-space: pre;
}

/* Terminal output styles */
.terminal-entry {
  font-family: var(--font-mono);
  font-size: 0.875rem; /* slightly smaller */
  background: var(--bg-card);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-sm);
  padding: 0.375rem 0.625rem; /* compact */
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  display: grid; /* align wrapped lines */
  grid-template-columns: max-content 1fr;
  column-gap: 0.5rem;
  align-items: start;
}

.terminal-entry + .terminal-entry {
  margin-top: var(--spacing-xs);
}

.terminal-entry.info {
  border-left: 3px solid var(--info);
}

.terminal-entry.warning,
.terminal-entry.warn {
  border-left: 3px solid var(--warning);
}

.terminal-entry.error {
  border-left: 3px solid var(--error);
}

.terminal-entry.debug {
  border-left: 3px solid var(--accent-secondary);
}

.terminal-timestamp {
  color: var(--text-tertiary);
  grid-column: 1;
}

.terminal-content {
  color: var(--text-primary);
  grid-column: 2;
}

/* Highlight style for search matches in terminal */
.terminal-entry mark.hl {
  background: rgba(250, 204, 21, 0.25); /* amber */
  color: var(--text-primary);
  padding: 0 2px;
  border-radius: 3px;
  outline: 1px solid rgba(234, 179, 8, 0.35);
}

.terminal-entry mark.hl:focus {
  outline: 2px solid var(--border-focus);
}

/* Animations */
/* Enhanced animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Scroll control - redesigned */
.scroll-control {
  position: fixed;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 1000;
}

.btn-floating {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--bg-card);
  border: 1px solid var(--border-subtle);
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-floating:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-floating.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

/* Responsive adjustments */
/* Responsive design - enhanced */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .luna-controls {
    border-right: none;
    margin-right: 0;
    padding-right: 0;
    order: -1;
    width: 100%;
    justify-content: center;
  }

  .tab-nav {
    padding: 0 var(--spacing-md);
    overflow-x: auto;
  }

  .tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .filters {
    width: 100%;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .filters input,
  .filters select {
    min-width: 100%;
  }

  .log-container {
    padding: var(--spacing-md);
  }

  .scroll-control {
    bottom: var(--spacing-md);
    right: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.25rem;
  }

  .luna-icon {
    font-size: 1.5rem;
  }

  .tab-btn {
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
  }

  .tab-icon {
    font-size: 1rem;
  }

  .tab-label {
    font-size: 0.75rem;
  }
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Prompt log content improvements: wrap + scroll for long prompts */
.prompt-log .log-content {
  background: var(--bg-hover);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  max-height: 40vh;
  overflow: auto;
}

.prompt-log .log-content pre {
  white-space: pre-wrap; /* wrap long lines */
  word-break: break-word;
  margin: 0;
}

.prompt-log .log-content code {
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

/* Collapsible prompt controls */
.prompt-log .log-header {
  gap: var(--spacing-md);
}

.prompt-log .collapse-toggle {
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-subtle);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  margin-right: var(--spacing-sm);
}

.prompt-log .collapse-toggle:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.prompt-log .collapse-toggle::before {
  content: '▸';
  font-size: 0.9rem;
  line-height: 1;
}

.prompt-log .collapse-toggle[aria-expanded="true"]::before {
  content: '▾';
}

.prompt-log.collapsed .log-content {
  display: none;
}

.prompt-log .log-title {
  cursor: pointer;
}

/* Transcript layout improvements */
.transcript-entry {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
  background: var(--bg-card);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.transcript-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-hover);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  text-transform: uppercase;
  flex-shrink: 0;
  overflow: hidden;
}

.transcript-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}

.transcript-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.transcript-header {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-sm);
}

.transcript-user {
  font-weight: 600;
  color: var(--text-primary);
}

.transcript-header .transcript-timestamp {
  margin-left: auto;
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.transcript-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  color: var(--text-primary);
}

/* Print styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .header,
  .tab-nav,
  .scroll-control {
    display: none;
  }
  
  .log-entry {
    break-inside: avoid;
    border: 1px solid #ccc;
    margin-bottom: 1rem;
  }
}