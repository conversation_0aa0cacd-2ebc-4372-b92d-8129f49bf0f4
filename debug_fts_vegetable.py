#!/usr/bin/env python3
"""
Debug FTS Vegetable Search
==========================
Tests the exact FTS search that's failing for the vegetable query
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_fts_vegetable_search():
    print("🔍 Debugging FTS Search for: 'luna your favorite vegetable'")
    print("=" * 60)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # The exact query that's failing
    query = "luna your favorite vegetable"
    query_terms = query.split()
    
    print(f"🔍 Query: '{query}'")
    print(f"📝 Query terms: {query_terms}")
    
    # Test the exact logic from memory_system.py
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # First, convert "your" to "luna" like the code does
        query_terms = ['luna' if term == 'your' else term for term in query_terms]
        print(f"📝 After 'your' -> 'luna' conversion: {query_terms}")
        
        # Add semantic expansions
        semantic_expansions = {
            'vegetable': ['food', 'potatoes', 'loves'],
            'vegetables': ['food', 'potatoes', 'loves'],
            'eat': ['food', 'loves', 'potatoes'],
            'enjoy': ['loves', 'likes', 'food'],
            'preference': ['favorite', 'likes', 'loves'],
            'preferences': ['favorite', 'likes', 'loves'],
            'prefer': ['favorite', 'likes'],
            'like': ['loves', 'likes', 'favorite']
        }
        
        # Expand query terms with semantic alternatives
        expanded_terms = []
        for term in query_terms:
            expanded_terms.append(term)
            if term in semantic_expansions:
                expanded_terms.extend(semantic_expansions[term])
                print(f"   Expanded '{term}' → {semantic_expansions[term]}")
        
        # Remove duplicates while preserving order
        seen = set()
        final_terms = []
        for term in expanded_terms:
            if term not in seen:
                final_terms.append(term)
                seen.add(term)
        
        print(f"📝 Final expanded terms: {final_terms}")
        
        # Test Strategy 1: AND all terms (what's currently being tried first)
        valid_terms = [term for term in final_terms if len(term) > 2]
        print(f"📝 Valid terms (len > 2): {valid_terms}")
        
        if valid_terms:
            escaped_terms = [f'"{term.replace('"', '""')}"' for term in valid_terms]
            and_query = ' AND '.join(escaped_terms)
            print(f"\n1️⃣ Testing AND query: {and_query}")
            
            try:
                cursor = conn.execute("""
                    SELECT mf.id, mf.content, mf.confidence 
                    FROM memory_facts mf
                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                    LIMIT 5
                """, (and_query,))
                
                and_results = cursor.fetchall()
                print(f"   AND results: {len(and_results)}")
                for result in and_results[:3]:
                    print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
                    
                if len(and_results) == 0:
                    print(f"   ❌ No results - this is why it falls back to priority terms")
                    
            except Exception as e:
                print(f"   ❌ AND query failed: {e}")
        
        # Test Strategy 2: Priority terms (what happens when AND fails)
        print(f"\n2️⃣ Testing priority term fallback:")
        term_priority = {
            'color': 10, 'purple': 10,
            'food': 10, 'potatoes': 10, 'vegetable': 10, 'vegetables': 10, 'loves': 9, 'likes': 9,
            'block': 10, 'minecraft': 10, 'cobblestone': 10, 'obsidian': 10,
            'favorite': 7,
            'luna': 3  # General term, lower priority
        }
        
        # Sort terms by priority (higher priority first)
        prioritized_terms = []
        for term in valid_terms:
            if term in term_priority:
                priority = term_priority[term]
                prioritized_terms.append((priority, term))
                print(f"   '{term}' → priority {priority}")
        
        prioritized_terms.sort(reverse=True)  # Highest priority first
        
        if prioritized_terms:
            best_term = prioritized_terms[0][1]
            print(f"   🏆 Best term selected: '{best_term}' (priority: {prioritized_terms[0][0]})")
            
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 5
            """, (f'"{best_term}"',))
            
            best_results = cursor.fetchall()
            print(f"   Results for '{best_term}': {len(best_results)}")
            for result in best_results[:3]:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
        
        # Test Strategy 3: Individual key terms
        print(f"\n3️⃣ Testing individual key terms:")
        key_terms = ['vegetable', 'food', 'potatoes', 'favorite']
        for term in key_terms:
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 3
            """, (f'"{term}"',))
            
            results = cursor.fetchall()
            print(f"   '{term}' → {len(results)} results:")
            for result in results:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
    
    finally:
        conn.close()

if __name__ == "__main__":
    asyncio.run(debug_fts_vegetable_search())