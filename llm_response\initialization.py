import os

# Set Vulkan device visibility but don't disable CUDA yet
# Device 0: RTX 4070 (NVIDIA) - Main response model  
# Device 1: RX 6650XT (AMD) - Decision/utility models
os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"

def configure_llm_backends():
    """Configure GPU backends for LLM systems only, called during LLM initialization."""
    logger.info("🔧 Configuring LLM GPU backends...")
    
    # Force Vulkan backend for LLM systems while preserving CUDA for speech_to_text
    os.environ["GGML_BACKEND"] = "vulkan"
    # Note: NOT disabling CUDA_VISIBLE_DEVICES to preserve speech_to_text functionality
    
    logger.info("✅ LLM backends configured: Vulkan enabled for LLM systems")

import logging
from openai import OpenAI
import httpx
import backoff
import asyncio
from llama_cpp import Llama
import threading
from typing import Optional

# Import constants from the config module
from .config import (
    LM_STUDIO_URL,
    LM_STUDIO_API_KEY,
    LM_STUDIO_MODEL_NAME,
    LLAMA_CPP_MODEL_PATH,    # Gemma3 model path
    GEMMA_CPP_MODEL_PATH,    # Qwen3 model path (confusingly named)
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
    LLAMA_CPP_N_GPU_LAYERS,
    LLAMA_CPP_USE_MMAP,
    LLAMA_CPP_USE_MLOCK,
    LLAMA_CPP_VERBOSE,
    OLLAMA_MODEL_NAME,
    KOKORO_BASE_URL
)

logger = logging.getLogger(__name__)

# Global variables for clients
_lm_studio_client = None
_gemma3_client = None  # Gemma3 on RTX 4070
_qwen3_client = None   # Qwen3 on AMD 6650XT
_gemma3_lock = threading.Lock()
_qwen3_lock = threading.Lock()

# Legacy compatibility
_llama_cpp_client = None
_llama_cpp_lock = threading.Lock()

def initialize_lm_studio():
    """Stubbed: LM Studio disabled; skip initialization."""
    logger.info("LM Studio initialization skipped (disabled).")
    return None

def get_lm_studio_client():
    """Returns the initialized LM Studio client instance."""
    if _lm_studio_client is None:
        logger.warning("LM Studio client accessed before initialization!")
        return initialize_lm_studio() # Attempt initialization if not done
    return _lm_studio_client

def initialize_gemma3_client():
    """Initializes the Gemma3 client on RTX 4070 for processing tasks."""
    global _gemma3_client
    
    if _gemma3_client is not None:
        logger.info("Gemma3 client already initialized.")
        return _gemma3_client
    
    # Configure LLM backends now that speech_to_text has loaded
    configure_llm_backends()
        
    from shared_model import get_or_create_model, GPU_CONFIGS
    
    try:
        logger.info("Initializing Gemma3 on RTX 4070 for processing tasks...")
        _gemma3_client = get_or_create_model(
            key="gemma3_4070",
            model_path=LLAMA_CPP_MODEL_PATH,
            gpu_target="nvidia_4070",
            n_ctx=LLAMA_CPP_N_CTX,
            n_threads=LLAMA_CPP_N_THREADS
        )
        logger.info("✅ Gemma3 client initialized on RTX 4070")
        return _gemma3_client
    except Exception as e:
        logger.error(f"Failed to initialize Gemma3 client: {e}", exc_info=True)
        return None

def initialize_qwen3_client():
    """Initializes the Qwen3 client on AMD 6650XT for brain/decision tasks."""
    global _qwen3_client
    
    if _qwen3_client is not None:
        logger.info("Qwen3 client already initialized.")
        return _qwen3_client
        
    from shared_model import get_or_create_model
    from .config import GEMMA_CPP_MODEL_PATH
    
    try:
        logger.info("Initializing Qwen3 on AMD 6650XT for brain/decision tasks...")
        _qwen3_client = get_or_create_model(
            key="qwen3_amd",
            model_path=GEMMA_CPP_MODEL_PATH,  # This is actually Qwen3 despite the name
            gpu_target="amd_6650xt",
            n_ctx=LLAMA_CPP_N_CTX,
            n_threads=LLAMA_CPP_N_THREADS
        )
        logger.info("✅ Qwen3 client initialized on AMD 6650XT")
        return _qwen3_client
    except Exception as e:
        logger.error(f"Failed to initialize Qwen3 client: {e}", exc_info=True)
        return None

def initialize_llama_cpp():
    """Legacy function - now initializes Gemma3 client for compatibility."""
    global _llama_cpp_client
    
    if _llama_cpp_client is not None:
        logger.info("llama.cpp client already initialized.")
        return _llama_cpp_client

    # For compatibility, initialize Gemma3 client
    _llama_cpp_client = initialize_gemma3_client()
    return _llama_cpp_client

def get_gemma3_client():
    """Returns the Gemma3 client for processing tasks."""
    if _gemma3_client is None:
        logger.warning("Gemma3 client accessed before initialization!")
        return initialize_gemma3_client()
    return _gemma3_client

def get_qwen3_client():
    """Returns the Qwen3 client for brain/decision tasks."""
    if _qwen3_client is None:
        logger.warning("Qwen3 client accessed before initialization!")
        return initialize_qwen3_client()
    return _qwen3_client

def get_llama_cpp_client():
    """Legacy function - returns Gemma3 client for compatibility."""
    return get_gemma3_client()

# Legacy function names for compatibility
def initialize_ollama():
    """Legacy function - now initializes llama.cpp instead of Ollama."""
    logger.info("initialize_ollama() called - redirecting to llama.cpp initialization")
    return initialize_llama_cpp()

@backoff.on_exception(
    backoff.expo,
    (httpx.TimeoutException, httpx.ConnectError, httpx.ReadTimeout),
    max_tries=3,
    jitter=backoff.full_jitter
)
def create_lm_studio_client():
    """Create and return an OpenAI-compatible client for LM Studio"""
    logger.debug(f"Creating LM Studio client for URL: {LM_STUDIO_URL}")
    return OpenAI(
        api_key=LM_STUDIO_API_KEY,
        base_url=LM_STUDIO_URL
    )

def create_kokoro_client():
    """Create and return an OpenAI-compatible client for Kokoro."""
    logger.debug(f"Creating Kokoro client for URL: {KOKORO_BASE_URL}")
    return OpenAI(
        base_url=KOKORO_BASE_URL,
        api_key="not-needed" # API key is not needed for Kokoro
    )

async def preload_ollama_model(model_name: str):
    """Legacy function - llama.cpp models are preloaded on initialization."""
    logger.info(f"preload_ollama_model() called for {model_name} - llama.cpp models are preloaded on init")
    
    try:
        # Ensure llama.cpp client is initialized
        client = get_llama_cpp_client()
        if client is not None:
            logger.info(f"✅ llama.cpp model is already loaded and ready")
            return True
        else:
            logger.warning(f"⚠️ llama.cpp client not initialized")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking llama.cpp model: {e}")
        return False

# Legacy function for compatibility  
preload_llama_cpp_model = preload_ollama_model

# --- Initialize clients upon module load (optional, or can be called explicitly) ---
# It might be better to initialize these lazily or explicitly in main.py after env vars are loaded.
# For now, we provide the creation functions.

def initialize_all_clients():
    """Initialize both Gemma3 and Qwen3 clients for optimal performance."""
    logger.info("Initializing all GPU clients for optimal dual-GPU performance...")
    
    # Initialize Gemma3 on RTX 4070 for processing
    gemma3 = initialize_gemma3_client()
    
    # Initialize Qwen3 on AMD 6650XT for brain/decision
    qwen3 = initialize_qwen3_client()
    
    success = gemma3 is not None and qwen3 is not None
    if success:
        logger.info("🚀 All GPU clients initialized successfully for maximum performance!")
    else:
        logger.warning("⚠️ Some GPU clients failed to initialize")
    
    return success

# Example of explicit initialization call (usually done in main.py)
if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG)
    print("Initializing dual-GPU setup...")
    try:
        success = initialize_all_clients()
        print(f"Dual-GPU setup successful: {success}")
    except Exception as e:
        print(f"Dual-GPU setup failed: {e}")

    print("\nAttempting to create Kokoro client...")
    try:
        k_client = create_kokoro_client()
        print(f"Kokoro Client Created: {k_client is not None}")
    except Exception as e:
        print(f"Kokoro Client Creation failed: {e}")