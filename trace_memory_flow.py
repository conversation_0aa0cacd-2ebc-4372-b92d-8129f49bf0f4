#!/usr/bin/env python3
"""
Trace Memory Flow
================
Traces the exact flow of enhanced_get_memory_context to find where user filtering fails
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def trace_memory_flow():
    print("🔍 Tracing Memory Flow for User Query")
    print("=" * 50)
    
    await initialize_memory_system()
    user_id = 921637353364287489
    
    # Test the problematic query
    query = "Luna, what's my favorite color?"
    print(f"🧪 Tracing query: '{query}'")
    print(f"👤 User ID: {user_id}")
    
    # Step 1: Check what enhanced_get_memory_context does
    print(f"\n📋 Step 1: enhanced_get_memory_context processing")
    
    # Replicate the logic from enhanced_get_memory_context
    import re
    key_terms = []
    words = re.findall(r'\b\w+\b', query.lower())
    stop_words = {'what', 'is', 'are', 'do', 'you', 'know', 'about', 'the', 'a', 'an', 'your', 'my', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
    
    for word in words:
        if word not in stop_words and len(word) > 2:
            key_terms.append(word)
    
    search_query = ' '.join(key_terms[:3]) if key_terms else query
    print(f"   Original query: '{query}'")
    print(f"   Key terms: {key_terms}")
    print(f"   Search query: '{search_query}'")
    
    # Step 2: Check what retrieve_relevant_context does
    print(f"\n📋 Step 2: retrieve_relevant_context processing")
    ms = get_memory_system()
    
    # Manually replicate the user detection logic
    query_lower = search_query.lower()  # This is the key!
    
    user_indicators = ['my ', 'mine', 'i like', 'i love', 'i prefer', 'i said', 'i told', 'what did i']
    is_user_query = any(indicator in query_lower for indicator in user_indicators)
    
    luna_indicators = ['luna', 'your ', 'you like', 'you love', 'you prefer', 'favorite', 'you enjoy', 'do you like', 'what do you']
    is_luna_query = any(indicator in query_lower for indicator in luna_indicators)
    
    print(f"   Query used for detection: '{search_query}'")
    print(f"   is_user_query: {is_user_query}")
    print(f"   is_luna_query: {is_luna_query}")
    
    if is_user_query:
        search_user_id = user_id
        classification = "USER query (filter by user)"
    elif is_luna_query and not is_user_query:
        search_user_id = None
        classification = "LUNA query (no user filter)"
    else:
        search_user_id = user_id
        classification = "DEFAULT (filter by user)"
    
    print(f"   Classification: {classification}")
    print(f"   search_user_id: {search_user_id}")
    
    # Step 3: Test the actual calls
    print(f"\n📋 Step 3: Actual function calls")
    
    # Test retrieve_relevant_context directly
    context1 = await ms.retrieve_relevant_context(
        query=search_query,
        user_id=user_id,
        channel_id=None,
        max_facts=5
    )
    print(f"   Direct retrieve_relevant_context: {len(context1) if context1 else 0} chars")
    if context1:
        first_line = context1.split('\n')[0] if context1 else ''
        print(f"     First fact: {first_line}")
    
    # Test enhanced_get_memory_context
    context2 = await enhanced_get_memory_context(
        query_text=query,
        current_user_id=user_id,
        current_channel_id=None,
        is_dm_channel=False
    )
    print(f"   enhanced_get_memory_context: {len(context2) if context2 else 0} chars")
    if context2:
        first_line = context2.split('\n')[0] if context2 else ''
        print(f"     First fact: {first_line}")
    
    print(f"\n💡 Analysis:")
    if context1 != context2:
        print(f"   ❌ MISMATCH: The two functions return different results!")
        print(f"   🔍 This indicates a bug in the flow")
    else:
        print(f"   ✅ CONSISTENT: Both functions return the same result")
        if search_user_id == user_id and 'luna' in (context1 or '').lower():
            print(f"   ❌ BUT WRONG: User query returned Luna facts")
        elif search_user_id is None and 'gavin' in (context1 or '').lower():
            print(f"   ❌ BUT WRONG: Luna query returned user facts")
        else:
            print(f"   ✅ CORRECT: Results match expected classification")

if __name__ == "__main__":
    asyncio.run(trace_memory_flow())