#!/usr/bin/env python3
"""
Test Memory Retrieval Fix
=========================
Tests if the fix allows <PERSON>'s personality facts to be retrieved for any user
"""
import asyncio
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def test_memory_retrieval_fix():
    print("🔧 Testing Memory Retrieval Fix")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Test queries that should find <PERSON>'s personality facts
    test_queries = [
        "luna favorite color",
        "luna favorite food", 
        "luna favorite block",
        "your favorite color",
        "what do you like",
        "purple",
        "potatoes",
        "minecraft"
    ]
    
    user_id = 921637353364287489  # Gavin's real Discord ID
    
    print(f"🧪 Testing with User ID: {user_id}")
    print(f"👤 This should now find <PERSON>'s global facts, not just user-specific ones\n")
    
    for query in test_queries:
        print(f"🔍 Query: '{query}'")
        
        start_time = time.time()
        context = await ms.retrieve_relevant_context(
            query=query,
            user_id=user_id,
            channel_id=None,
            max_facts=5
        )
        retrieval_time = (time.time() - start_time) * 1000
        
        if context and context.strip():
            print(f"  ✅ Found context ({retrieval_time:.2f}ms, {len(context)} chars):")
            for i, line in enumerate(context.split('\n')[:3], 1):
                if line.strip():
                    print(f"    {i}. {line.strip()}")
        else:
            print(f"  ❌ No context found ({retrieval_time:.2f}ms)")
        
        print()
    
    # Test direct search to see what's happening at the database level
    print(f"🗄️ Direct Database Tests:")
    print("-" * 30)
    
    for query in ['luna favorite color', 'color', 'luna']:
        print(f"\n🔍 Direct search: '{query}'")
        
        # Test with user filtering (old behavior)
        facts_with_user = ms.warm_storage.search_facts(
            query=query,
            user_id=user_id,  # Filter by user
            limit=5
        )
        print(f"  With user filter ({user_id}): {len(facts_with_user)} facts")
        for fact in facts_with_user[:2]:
            print(f"    - {fact.content[:60]}...")
        
        # Test without user filtering (new behavior for Luna queries)
        facts_without_user = ms.warm_storage.search_facts(
            query=query,
            user_id=None,  # No user filter
            limit=5
        )
        print(f"  Without user filter: {len(facts_without_user)} facts")
        for fact in facts_without_user[:2]:
            print(f"    - {fact.content[:60]}...")
    
    print(f"\n✨ Fix Summary:")
    print(f"  - Luna personality queries should now return facts about Luna")
    print(f"  - User-specific queries still filter by user")
    print(f"  - Performance remains under 5ms target")

if __name__ == "__main__":
    asyncio.run(test_memory_retrieval_fix())