const { app, BrowserWindow, ipc<PERSON>ain, Menu, Tray, nativeImage, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const WebSocket = require('ws');

// Create WebSocket server for receiving logs from Luna
let wss = null;
let mainWindow = null;
let tray = null;
let isQuitting = false;

function createWindow() {
    // Build window options and include icon only if a valid one exists
    const winOptions = {
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        title: 'Luna Log Viewer',
        show: false // Don't show until ready
    };
    const iconPath = path.join(__dirname, 'icon.ico');
    if (fs.existsSync(iconPath)) {
        winOptions.icon = iconPath;
    }
    // Create the browser window
    mainWindow = new BrowserWindow(winOptions);

    // Load the app
    mainWindow.loadFile('index.html');

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('Luna Log Viewer started successfully');
    });

    // Open DevTools in development
    if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
    }

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (wss) {
            wss.close();
        }
    });

    // Minimize-to-tray behavior
    mainWindow.on('minimize', (event) => {
        event.preventDefault();
        mainWindow.hide();
    });

    mainWindow.on('close', (event) => {
        if (!isQuitting) {
            event.preventDefault();
            mainWindow.hide();
        }
    });
}

function startWebSocketServer() {
    // Start WebSocket server on port 8765 for Luna to connect to
    try {
        wss = new WebSocket.Server({ port: 8765 });
    } catch (error) {
        if (error.code === 'EADDRINUSE') {
            console.log('Port 8765 already in use, trying port 8766...');
            try {
                wss = new WebSocket.Server({ port: 8766 });
            } catch (error2) {
                console.log('Port 8766 also in use, trying port 8767...');
                wss = new WebSocket.Server({ port: 8767 });
            }
        } else {
            throw error;
        }
    }
    
    const actualPort = wss.options.port || 8765;
    console.log(`WebSocket server started on port ${actualPort}`);
    
    wss.on('connection', (ws) => {
        console.log('Luna connected to log viewer');
        
        ws.on('message', (message) => {
            try {
                const logData = JSON.parse(message);
                // Forward log data to renderer process
                if (mainWindow && mainWindow.webContents) {
                    mainWindow.webContents.send('new-log', logData);
                }
            } catch (error) {
                console.error('Error parsing log message:', error);
            }
        });
        
        ws.on('close', () => {
            console.log('Luna disconnected from log viewer');
        });
        
        ws.on('error', (error) => {
            console.error('WebSocket error:', error);
        });
    });
    
    wss.on('error', (error) => {
        console.error('WebSocket server error:', error);
    });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
    createWindow();
    startWebSocketServer();
    
    // Auto-launch on Windows startup
    try {
        app.setLoginItemSettings({
            openAtLogin: true,
            path: process.execPath,
            args: []
        });
    } catch (e) {
        console.warn('Failed to set login item settings:', e);
    }
    
    // Create Tray icon and menu
    try {
        const tryPaths = [
            path.join(__dirname, 'icon.ico'),
            path.join(__dirname, 'icon_small.ico')
        ];
        let trayIcon = null;
        for (const p of tryPaths) {
            if (fs.existsSync(p)) { trayIcon = nativeImage.createFromPath(p); break; }
        }
        tray = new Tray(trayIcon || nativeImage.createEmpty());
        const contextMenu = Menu.buildFromTemplate([
            { label: 'Show', click: () => { if (mainWindow) { mainWindow.show(); mainWindow.focus(); } } },
            { label: 'Hide', click: () => { if (mainWindow) { mainWindow.hide(); } } },
            { type: 'separator' },
            { label: 'Start Luna', click: () => { if (mainWindow) mainWindow.webContents.send('ui-start-luna'); } },
            { label: 'Stop Luna', click: () => { if (mainWindow) mainWindow.webContents.send('ui-stop-luna'); } },
            { label: 'Manage Avatars', click: () => { if (mainWindow) mainWindow.webContents.send('ui-open-avatar-manager'); } },
            { label: 'Open DevTools', click: () => { if (mainWindow) mainWindow.webContents.openDevTools({ mode: 'detach' }); } },
            { type: 'separator' },
            { label: 'Quit', click: () => { isQuitting = true; app.quit(); } }
        ]);
        tray.setToolTip('Luna Log Viewer');
        tray.setContextMenu(contextMenu);
        tray.on('click', () => {
            if (!mainWindow) return;
            if (mainWindow.isVisible()) { mainWindow.hide(); } else { mainWindow.show(); mainWindow.focus(); }
        });
    } catch (e) {
        console.warn('Tray initialization failed:', e);
    }
    
    // Create menu
    const template = [
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Avatars',
            submenu: [
                { label: 'Manage Avatars', click: () => { if (mainWindow) mainWindow.webContents.send('ui-open-avatar-manager'); } }
            ]
        },
        {
            label: 'Logs',
            submenu: [
                {
                    label: 'Clear All Logs',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('clear-logs');
                        }
                    }
                },
                {
                    label: 'Export Logs',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('export-logs');
                        }
                    }
                }
            ]
        }
    ];
    
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        isQuitting = true;
        app.quit();
    }
});

// Ensure Luna is terminated when the app quits
app.on('before-quit', () => {
    try {
        if (lunaProcess) {
            console.log('App quitting: force-killing Luna process tree...');
            killLunaProcessTree(lunaProcess);
        }
    } catch (e) {
        console.warn('Failed during before-quit cleanup:', e);
    }
});

// Save or update a single user profile into llm_response/config.py USER_PROFILES
ipcMain.handle('avatars:saveKnownUserProfile', async (event, payload = {}) => {
    try {
        const { id, main_name, aliases } = payload || {};
        const userId = (id || '').toString().trim();
        const mainName = (main_name || '').toString().trim();
        const aliasArr = Array.isArray(aliases) ? aliases : (typeof aliases === 'string' ? aliases.split(',') : []);
        const cleanAliases = aliasArr.map(a => a.toString().trim()).filter(a => a.length > 0);
        if (!userId || !/^[0-9]+$/.test(userId)) {
            return { success: false, error: 'A numeric User ID is required.' };
        }
        if (!mainName) {
            return { success: false, error: 'main_name is required.' };
        }

        const projectRoot = path.resolve(__dirname, '..');
        const configPath = path.join(projectRoot, 'llm_response', 'config.py');
        if (!fs.existsSync(configPath)) return { success: false, error: 'config.py not found' };
        const content = fs.readFileSync(configPath, 'utf8');

        // locate USER_PROFILES block
        const startIdx = content.indexOf('USER_PROFILES');
        if (startIdx < 0) return { success: false, error: 'USER_PROFILES not found' };
        const braceStart = content.indexOf('{', startIdx);
        if (braceStart < 0) return { success: false, error: 'USER_PROFILES block malformed' };
        let depth = 0; let end = -1;
        for (let i = braceStart; i < content.length; i++) {
            const ch = content[i];
            if (ch === '{') depth++;
            else if (ch === '}') { depth--; if (depth === 0) { end = i; break; } }
        }
        if (end < 0) return { success: false, error: 'USER_PROFILES block not closed' };
        const before = content.slice(0, braceStart);
        const block = content.slice(braceStart, end + 1);
        const after = content.slice(end + 1);

        // parse existing users
        const entryRegex = /(\d+)\s*:\s*\{[\s\S]*?"main_name"\s*:\s*"([^"]+)"[\s\S]*?"aliases"\s*:\s*\[((?:[^\]\n]|\n)*?)\][\s\S]*?\}/g;
        const users = new Map();
        let m;
        while ((m = entryRegex.exec(block)) !== null) {
            const idStr = m[1];
            const existingName = m[2];
            const aliasesRaw = m[3];
            const aliasRegex = /"([^"]+)"/g;
            const arr = []; let a;
            while ((a = aliasRegex.exec(aliasesRaw)) !== null) arr.push(a[1]);
            users.set(idStr, { main_name: existingName, aliases: arr });
        }

        // upsert our user
        users.set(userId, { main_name: mainName, aliases: cleanAliases });

        // rebuild block
        const lines = ['{'];
        for (const [uid, u] of users.entries()) {
            const alStr = u.aliases.map(x => `"${x}"`).join(', ');
            lines.push(`    ${uid}: {`);
            lines.push(`        "main_name": "${u.main_name}",`);
            lines.push(`        "aliases": [${alStr}],`);
            lines.push(`        "volume_multiplier": 1.0  `);
            lines.push('    },');
        }
        lines.push('}');
        const newContent = before + lines.join('\n') + after;

        // backup then write
        try { fs.copyFileSync(configPath, configPath + '.bak'); } catch {}
        fs.writeFileSync(configPath, newContent, 'utf8');
        return { success: true };
    } catch (e) {
        return { success: false, error: e?.message || String(e) };
    }
});

// Provide known users from llm_response/config.py (USER_PROFILES)
ipcMain.handle('avatars:listKnownUsers', async () => {
    try {
        // Resolve config.py relative to project root (one level up from luna-log-viewer)
        const projectRoot = path.resolve(__dirname, '..');
        const configPath = path.join(projectRoot, 'llm_response', 'config.py');
        if (!fs.existsSync(configPath)) {
            return { success: false, users: [], error: 'config.py not found' };
        }
        const content = fs.readFileSync(configPath, 'utf8');
        // Naive parse: find USER_PROFILES block and extract entries
        const startIdx = content.indexOf('USER_PROFILES');
        if (startIdx < 0) return { success: true, users: [] };
        // Capture from first '{' after USER_PROFILES to the matching closing '}' of top-level dict
        const braceStart = content.indexOf('{', startIdx);
        if (braceStart < 0) return { success: true, users: [] };
        let depth = 0;
        let end = -1;
        for (let i = braceStart; i < content.length; i++) {
            const ch = content[i];
            if (ch === '{') depth++;
            else if (ch === '}') {
                depth--;
                if (depth === 0) { end = i; break; }
            }
        }
        if (end < 0) return { success: true, users: [] };
        const block = content.slice(braceStart, end + 1);

        // Extract entries like: 123456789: { "main_name": "Name", "aliases": [ ... ] }
        const entryRegex = /(\d+)\s*:\s*\{[\s\S]*?"main_name"\s*:\s*"([^"]+)"[\s\S]*?"aliases"\s*:\s*\[((?:[^\]\n]|\n)*?)\][\s\S]*?\}/g;
        const users = [];
        let m;
        while ((m = entryRegex.exec(block)) !== null) {
            const idStr = m[1];
            const mainName = m[2];
            const aliasesRaw = m[3];
            // Parse aliases array by extracting quoted strings
            const aliasRegex = /"([^"]+)"/g;
            const aliases = [];
            let a;
            while ((a = aliasRegex.exec(aliasesRaw)) !== null) aliases.push(a[1]);
            users.push({ id: idStr, main_name: mainName, aliases });
        }
        return { success: true, users };
    } catch (e) {
        return { success: false, users: [], error: e?.message || String(e) };
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
        startWebSocketServer();
    }
});

// Handle IPC from renderer
ipcMain.handle('get-log-stats', () => {
    // This could return statistics about logs
    return {
        connected: wss && wss.clients.size > 0,
        clientCount: wss ? wss.clients.size : 0
    };
});

// Luna control variables
let lunaProcess = null;
const { spawn } = require('child_process');

// Helper: kill a process tree (Windows: taskkill /T /F; others: SIGKILL)
function killLunaProcessTree(proc) {
    if (!proc) return;
    const pid = proc.pid;
    if (process.platform === 'win32') {
        try {
            const killer = spawn('taskkill', ['/PID', String(pid), '/T', '/F'], { windowsHide: true, stdio: 'ignore' });
            killer.on('error', (err) => console.warn('taskkill error:', err));
        } catch (e) {
            console.warn('taskkill spawn failed:', e);
        }
    } else {
        try {
            // Send SIGKILL to the process; if you spawned detached, consider -pid group
            proc.kill('SIGKILL');
        } catch (e) {
            console.warn('SIGKILL failed:', e);
        }
    }
}

// Handle Luna start/stop commands
ipcMain.on('start-luna', (event) => {
    console.log('Starting Luna...');
    
    if (lunaProcess) {
        console.log('Luna is already running');
        return;
    }
    
    try {
        // Get the parent directory path (one level up from luna-log-viewer)
        const lunaDir = path.resolve(__dirname, '..');
        
        // Start Luna using conda-run to avoid activation issues and enforce UTF-8 via env
        const cmd = 'conda run -n luna-discord python main.py';
        lunaProcess = spawn('cmd.exe', ['/c', cmd], {
            cwd: lunaDir,
            stdio: ['ignore', 'pipe', 'pipe'],
            shell: true,
            windowsHide: true,
            env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
        });
        
        lunaProcess.stdout.on('data', (data) => {
            // Decode explicitly as UTF-8 to avoid mojibake
            console.log(`Luna: ${data.toString('utf8')}`);
        });
        
        lunaProcess.stderr.on('data', (data) => {
            console.error(`Luna Error: ${data.toString('utf8')}`);
        });
        
        lunaProcess.on('close', (code) => {
            console.log(`Luna process exited with code ${code}`);
            lunaProcess = null;
        });
        
        console.log('Luna started successfully');
    } catch (error) {
        console.error('Failed to start Luna:', error);
        lunaProcess = null;
    }
});

ipcMain.on('stop-luna', (event) => {
    console.log('Stopping Luna...');
    
    if (!lunaProcess) {
        console.log('Luna is not running');
        return;
    }
    
    try {
        // Force-kill entire process tree immediately (Windows) or send SIGKILL (others)
        killLunaProcessTree(lunaProcess);
        // Clear reference shortly after to allow close event to fire
        setTimeout(() => { lunaProcess = null; }, 500);
        console.log('Luna force-stop issued');
    } catch (error) {
        console.error('Failed to stop Luna:', error);
    }
});

// ================ Avatar Registry (user profile pictures) =================
// Registry file in Electron userData folder
function getAvatarRegistryPath() {
    const dir = path.join(app.getPath('userData'), 'avatars');
    try { if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true }); } catch {}
    return path.join(dir, 'registry.json');
}

function readAvatarRegistry() {
    const file = getAvatarRegistryPath();
    try {
        if (fs.existsSync(file)) {
            const raw = fs.readFileSync(file, 'utf8');
            return JSON.parse(raw);
        }
    } catch (e) {
        console.warn('Failed to read avatar registry:', e);
    }
    return { by_id: {}, by_name: {} };
}

function writeAvatarRegistry(reg) {
    const file = getAvatarRegistryPath();
    try {
        fs.writeFileSync(file, JSON.stringify(reg, null, 2), 'utf8');
        return true;
    } catch (e) {
        console.warn('Failed to write avatar registry:', e);
        return false;
    }
}

// Load registry
ipcMain.handle('avatars:load', () => {
    return readAvatarRegistry();
});

// Save registry
ipcMain.handle('avatars:save', (event, reg) => {
    const ok = writeAvatarRegistry(reg && typeof reg === 'object' ? reg : { by_id: {}, by_name: {} });
    return { success: ok };
});

// Pick an image and copy it into userData/avatars, return file:// path
ipcMain.handle('avatars:pickImage', async (event, { forUserId, forUserName } = {}) => {
    try {
        const result = await dialog.showOpenDialog({
            title: 'Select Avatar Image',
            properties: ['openFile'],
            filters: [
                { name: 'Images', extensions: ['png', 'jpg', 'jpeg', 'gif', 'webp'] }
            ]
        });
        if (result.canceled || !result.filePaths || !result.filePaths[0]) {
            return { canceled: true };
        }
        const src = result.filePaths[0];
        const avatarsDir = path.join(app.getPath('userData'), 'avatars');
        try { if (!fs.existsSync(avatarsDir)) fs.mkdirSync(avatarsDir, { recursive: true }); } catch {}
        const baseName = `${(forUserId || forUserName || 'user').toString().replace(/[^\w.-]/g, '_')}_${Date.now()}`;
        const ext = path.extname(src) || '.png';
        const dest = path.join(avatarsDir, baseName + ext);
        fs.copyFileSync(src, dest);
        const fileUrl = 'file://' + dest.replace(/\\/g, '/');
        return { canceled: false, path: fileUrl };
    } catch (e) {
        console.warn('Avatar image selection failed:', e);
        return { canceled: true, error: e?.message || String(e) };
    }
});
