<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luna Control Center</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <div class="luna-logo">
                        <span class="luna-icon">🌙</span>
                        <h1>Luna Control Center</h1>
                    </div>
                    <div class="connection-status" id="connection-status">
                        <div class="status-dot disconnected"></div>
                        <span>Waiting for Luna...</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="luna-controls">
                        <button class="btn btn-success" id="start-luna-btn" onclick="startLuna()" disabled>
                            <span>▶️</span> Start Luna
                        </button>
                        <button class="btn btn-danger" id="stop-luna-btn" onclick="stopLuna()" disabled>
                            <span>⏹️</span> Stop Luna
                        </button>
                    </div>
                    <div class="log-stats">
                        <span id="log-count">0 logs</span>
                    </div>
                    <button class="btn btn-secondary" onclick="clearAllLogs()">
                        <span>🗑️</span> Clear All
                    </button>
                    <button class="btn btn-primary" onclick="exportLogs()">
                        <span>💾</span> Export
                    </button>
                </div>
            </div>
        </header>

        <!-- Tab Navigation -->
        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="prompts" onclick="switchTab('prompts')">
                <span class="tab-icon">💭</span>
                <span class="tab-label">Prompts</span>
                <span class="tab-count" id="prompts-count">0</span>
            </button>
            <button class="tab-btn" data-tab="transcripts" onclick="switchTab('transcripts')">
                <span class="tab-icon">💬</span>
                <span class="tab-label">Voice Chat</span>
                <span class="tab-count" id="transcripts-count">0</span>
            </button>
            <button class="tab-btn" data-tab="terminal" onclick="switchTab('terminal')">
                <span class="tab-icon">💻</span>
                <span class="tab-label">Terminal</span>
                <span class="tab-count" id="terminal-count">0</span>
            </button>
            <button class="tab-btn" data-tab="latency" onclick="switchTab('latency')">
                <span class="tab-icon">⏱️</span>
                <span class="tab-label">Performance</span>
                <span class="tab-count" id="latency-count">0</span>
            </button>
            <button class="tab-btn" data-tab="screenshots" onclick="switchTab('screenshots')">
                <span class="tab-icon">📸</span>
                <span class="tab-label">Screenshots</span>
                <span class="tab-count" id="screenshots-count">0</span>
            </button>
        </nav>

        <!-- Content Area -->
        <main class="content">
            <!-- Prompts Tab -->
            <div class="tab-content active" id="prompts-tab">
                <div class="tab-header">
                    <h2>Prompt Logs</h2>
                    <div class="filters">
                        <select id="prompts-filter" onchange="filterLogs('prompts')">
                            <option value="all">All Types</option>
                            <option value="voice">Voice Stream</option>
                            <option value="text-stateful">Text Stateful</option>
                            <option value="text-stateless">Text Stateless</option>
                        </select>
                        <button class="btn btn-ghost" onclick="clearTabLogs('prompts')">Clear Prompts</button>
                    </div>
                </div>
                <div class="log-container" id="prompts-container">
                    <div class="empty-state">
                        <div class="empty-icon">💭</div>
                        <h3>No prompt logs yet</h3>
                        <p>Prompt logs will appear here when Luna processes messages</p>
                    </div>
                </div>
            </div>

            <!-- Transcripts Tab -->
            <div class="tab-content" id="transcripts-tab">
                <div class="tab-header">
                    <h2>Voice Chat Logs</h2>
                    <div class="filters">
                        <input type="text" id="transcripts-search" placeholder="Search voice chat..." oninput="searchLogs('transcripts')">
                        <button class="btn btn-secondary" id="manage-avatars-btn" title="Add or edit user profile pictures" onclick="openAvatarManager()">Manage Avatars</button>
                        <button class="btn btn-ghost" onclick="clearTabLogs('transcripts')">Clear Voice Chat</button>
                    </div>
                </div>
                <div class="log-container" id="transcripts-container">
                    <div class="empty-state">
                        <div class="empty-icon">💬</div>
                        <h3>No voice chat logs yet</h3>
                        <p>Voice chat conversations will appear here (both user transcripts and Luna's responses)</p>
                    </div>
                </div>
            </div>

            <!-- Terminal Tab -->
            <div class="tab-content" id="terminal-tab">
                <div class="tab-header">
                    <h2>Terminal Output</h2>
                    <div class="filters">
                        <select id="terminal-filter" onchange="filterLogs('terminal')">
                            <option value="all">All Levels</option>
                            <option value="info">Info</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                            <option value="debug">Debug</option>
                        </select>
                        <input type="text" id="terminal-search" placeholder="Find in terminal (Ctrl+F)" oninput="searchLogs('terminal')">
                        <button class="btn btn-ghost" onclick="clearTabLogs('terminal')">Clear Terminal</button>
                    </div>
                </div>
                <div class="log-container" id="terminal-container">
                    <div class="empty-state">
                        <div class="empty-icon">💻</div>
                        <h3>No terminal logs yet</h3>
                        <p>Terminal output from main.py will appear here</p>
                    </div>
                </div>
            </div>

            <!-- Performance Tab -->
            <div class="tab-content" id="latency-tab">
                <div class="tab-header">
                    <h2>Performance Metrics</h2>
                    <div class="filters">
                        <button class="btn btn-ghost" onclick="clearTabLogs('latency')">Clear Metrics</button>
                    </div>
                </div>
                <div class="log-container" id="latency-container">
                    <div class="empty-state">
                        <div class="empty-icon">⏱️</div>
                        <h3>No performance data yet</h3>
                        <p>Performance metrics and latency reports will appear here</p>
                    </div>
                </div>
            </div>

            <!-- Screenshots Tab -->
            <div class="tab-content" id="screenshots-tab">
                <div class="tab-header">
                    <h2>Screenshot Analysis</h2>
                    <div class="filters">
                        <button class="btn btn-ghost" onclick="clearTabLogs('screenshots')">Clear Screenshots</button>
                    </div>
                </div>
                <div class="log-container" id="screenshots-container">
                    <div class="empty-state">
                        <div class="empty-icon">📸</div>
                        <h3>No screenshot logs yet</h3>
                        <p>Start a screenshot analysis or enable auto-screenshot to see logs here</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Auto-scroll toggle -->
    <div class="scroll-control">
        <button id="scroll-toggle" class="btn btn-floating active" onclick="toggleAutoScroll()" title="Auto-scroll">
            <span>📍</span>
        </button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="renderer.js"></script>
</body>
</html>
