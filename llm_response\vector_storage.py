"""
Luna Memory Vector Storage System
================================

High-performance vector storage for semantic similarity search using
sentence transformers and FAISS for ultra-fast embedding retrieval.
"""

import asyncio
import logging
import numpy as np
import pickle
import threading
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import hashlib
import os

logger = logging.getLogger(__name__)

@dataclass
class VectorEntry:
    """A single vector entry with metadata"""
    id: str
    content: str
    embedding: np.ndarray
    metadata: Dict[str, Any]
    timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return {
            'id': self.id,
            'content': self.content,
            'embedding': self.embedding.tolist(),
            'metadata': self.metadata,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VectorEntry':
        """Create from dictionary"""
        return cls(
            id=data['id'],
            content=data['content'],
            embedding=np.array(data['embedding'], dtype=np.float32),
            metadata=data['metadata'],
            timestamp=data['timestamp']
        )

class FastEmbeddingCache:
    """Ultra-fast embedding cache for repeated queries"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, np.ndarray] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
        self._lock = threading.RLock()
    
    def _make_key(self, text: str) -> str:
        """Create cache key from text"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def get(self, text: str) -> Optional[np.ndarray]:
        """Get embedding from cache"""
        with self._lock:
            key = self._make_key(text)
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key].copy()
            return None
    
    def put(self, text: str, embedding: np.ndarray):
        """Store embedding in cache"""
        with self._lock:
            key = self._make_key(text)
            
            # Evict old entries if at capacity
            if len(self.cache) >= self.max_size and key not in self.cache:
                oldest_key = min(self.access_times.keys(), 
                               key=lambda k: self.access_times[k])
                del self.cache[oldest_key]
                del self.access_times[oldest_key]
            
            self.cache[key] = embedding.copy()
            self.access_times[key] = time.time()

class VectorStorage:
    """High-performance vector storage with FAISS indexing"""
    
    def __init__(self, dimension: int = 384, storage_path: str = None):
        self.dimension = dimension
        self.storage_path = storage_path or "luna_vector_storage.pkl"
        
        # Fast embedding cache
        self.embedding_cache = FastEmbeddingCache(max_size=2000)
        
        # Vector storage
        self.entries: Dict[str, VectorEntry] = {}
        self.vectors: List[np.ndarray] = []
        self.entry_ids: List[str] = []
        
        # FAISS index for similarity search
        self.index = None
        self._index_lock = threading.RLock()
        
        # Embedding model (lightweight and fast)
        self.embedding_model = None
        self._model_lock = threading.Lock()
        
        # Load existing data
        self._load_storage()
        self._initialize_embedding_model()
    
    def _initialize_embedding_model(self):
        """Initialize the embedding model lazily"""
        if self.embedding_model is not None:
            return
        
        with self._model_lock:
            if self.embedding_model is not None:
                return
            
            try:
                # Use a fast, lightweight sentence transformer
                from sentence_transformers import SentenceTransformer
                
                # Use a small, fast model optimized for speed
                model_name = "all-MiniLM-L6-v2"  # 384 dimensions, very fast
                self.embedding_model = SentenceTransformer(model_name)
                logger.info(f"✅ Initialized embedding model: {model_name}")
                
            except ImportError:
                logger.warning("⚠️ sentence-transformers not available, using fallback embeddings")
                self.embedding_model = None
            except Exception as e:
                logger.error(f"❌ Error initializing embedding model: {e}")
                self.embedding_model = None
    
    def _create_faiss_index(self):
        """Create or recreate FAISS index"""
        try:
            import faiss
            
            with self._index_lock:
                if len(self.vectors) == 0:
                    # Create empty index
                    self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
                    return
                
                # Stack vectors for indexing
                vector_matrix = np.vstack(self.vectors).astype(np.float32)
                
                # Normalize vectors for cosine similarity
                faiss.normalize_L2(vector_matrix)
                
                # Create index
                self.index = faiss.IndexFlatIP(self.dimension)
                self.index.add(vector_matrix)
                
                logger.info(f"✅ FAISS index created with {len(self.vectors)} vectors")
                
        except ImportError:
            logger.warning("⚠️ FAISS not available, using fallback similarity search")
            self.index = None
        except Exception as e:
            logger.error(f"❌ Error creating FAISS index: {e}")
            self.index = None
    
    def _load_storage(self):
        """Load existing vectors from disk"""
        if not os.path.exists(self.storage_path):
            return
        
        try:
            with open(self.storage_path, 'rb') as f:
                data = pickle.load(f)
            
            self.entries = {entry_id: VectorEntry.from_dict(entry_data) 
                          for entry_id, entry_data in data.get('entries', {}).items()}
            
            # Rebuild vectors list
            self.vectors = []
            self.entry_ids = []
            for entry_id, entry in self.entries.items():
                self.vectors.append(entry.embedding)
                self.entry_ids.append(entry_id)
            
            # Recreate FAISS index
            self._create_faiss_index()
            
            logger.info(f"✅ Loaded {len(self.entries)} vectors from storage")
            
        except Exception as e:
            logger.error(f"❌ Error loading vector storage: {e}")
            self.entries = {}
            self.vectors = []
            self.entry_ids = []
    
    def _save_storage(self):
        """Save vectors to disk"""
        try:
            data = {
                'entries': {entry_id: entry.to_dict() 
                          for entry_id, entry in self.entries.items()}
            }
            
            with open(self.storage_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.debug(f"💾 Saved {len(self.entries)} vectors to storage")
            
        except Exception as e:
            logger.error(f"❌ Error saving vector storage: {e}")
    
    async def get_embedding(self, text: str) -> Optional[np.ndarray]:
        """Get embedding for text with caching"""
        if not text.strip():
            return None
        
        # Check cache first
        cached = self.embedding_cache.get(text)
        if cached is not None:
            return cached
        
        # Generate embedding
        if self.embedding_model is None:
            self._initialize_embedding_model()
        
        if self.embedding_model is None:
            # Fallback: create random embedding (for testing)
            embedding = np.random.rand(self.dimension).astype(np.float32)
        else:
            try:
                # Run embedding generation in thread pool
                loop = asyncio.get_event_loop()
                embedding = await loop.run_in_executor(
                    None, 
                    lambda: self.embedding_model.encode(text, convert_to_numpy=True)
                )
                embedding = embedding.astype(np.float32)
            except Exception as e:
                logger.error(f"Error generating embedding: {e}")
                return None
        
        # Cache the embedding
        self.embedding_cache.put(text, embedding)
        
        return embedding
    
    async def add_entry(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Add a new entry to the vector storage"""
        if metadata is None:
            metadata = {}
        
        # Generate embedding
        embedding = await self.get_embedding(content)
        if embedding is None:
            return None
        
        # Create unique ID
        entry_id = hashlib.md5(f"{content}_{time.time()}".encode()).hexdigest()
        
        # Create entry
        entry = VectorEntry(
            id=entry_id,
            content=content,
            embedding=embedding,
            metadata=metadata,
            timestamp=time.time()
        )
        
        # Add to storage
        with self._index_lock:
            self.entries[entry_id] = entry
            self.vectors.append(embedding)
            self.entry_ids.append(entry_id)
            
            # Recreate FAISS index (for small datasets, this is fast)
            if len(self.vectors) % 100 == 0:  # Rebuild every 100 entries
                self._create_faiss_index()
        
        # Save to disk periodically
        if len(self.entries) % 50 == 0:  # Save every 50 entries
            await asyncio.get_event_loop().run_in_executor(None, self._save_storage)
        
        return entry_id
    
    async def search_similar(self, query: str, k: int = 10, threshold: float = 0.7) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Search for similar entries"""
        # Get query embedding
        query_embedding = await self.get_embedding(query)
        if query_embedding is None:
            return []
        
        with self._index_lock:
            if self.index is not None and len(self.vectors) > 0:
                # Use FAISS for fast search
                return await self._faiss_search(query_embedding, k, threshold)
            else:
                # Fallback to numpy similarity
                return await self._numpy_search(query_embedding, k, threshold)
    
    async def _faiss_search(self, query_embedding: np.ndarray, k: int, threshold: float) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Fast FAISS-based similarity search"""
        try:
            import faiss
            
            # Normalize query for cosine similarity
            query_norm = query_embedding.copy().reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_norm)
            
            # Search
            k_search = min(k, len(self.vectors))
            scores, indices = self.index.search(query_norm, k_search)
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if score >= threshold and idx < len(self.entry_ids):
                    entry_id = self.entry_ids[idx]
                    entry = self.entries[entry_id]
                    results.append((entry.content, float(score), entry.metadata))
            
            return results
            
        except Exception as e:
            logger.error(f"Error in FAISS search: {e}")
            return await self._numpy_search(query_embedding, k, threshold)
    
    async def _numpy_search(self, query_embedding: np.ndarray, k: int, threshold: float) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Fallback numpy-based similarity search"""
        if len(self.vectors) == 0:
            return []
        
        try:
            # Stack all vectors
            vector_matrix = np.vstack(self.vectors)
            
            # Compute cosine similarities
            query_norm = np.linalg.norm(query_embedding)
            vector_norms = np.linalg.norm(vector_matrix, axis=1)
            
            similarities = np.dot(vector_matrix, query_embedding) / (vector_norms * query_norm)
            
            # Get top k results above threshold
            valid_indices = np.where(similarities >= threshold)[0]
            top_indices = valid_indices[np.argsort(similarities[valid_indices])[::-1][:k]]
            
            results = []
            for idx in top_indices:
                entry_id = self.entry_ids[idx]
                entry = self.entries[entry_id]
                score = float(similarities[idx])
                results.append((entry.content, score, entry.metadata))
            
            return results
            
        except Exception as e:
            logger.error(f"Error in numpy search: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        return {
            'total_entries': len(self.entries),
            'cache_size': len(self.embedding_cache.cache),
            'dimension': self.dimension,
            'index_type': 'FAISS' if self.index is not None else 'NumPy',
            'model_loaded': self.embedding_model is not None
        }
    
    async def optimize(self):
        """Optimize the storage (rebuild index, clean cache)"""
        logger.info("🔧 Optimizing vector storage...")
        
        # Rebuild FAISS index
        await asyncio.get_event_loop().run_in_executor(None, self._create_faiss_index)
        
        # Save to disk
        await asyncio.get_event_loop().run_in_executor(None, self._save_storage)
        
        logger.info("✅ Vector storage optimization complete")

# Global instance
_vector_storage: Optional[VectorStorage] = None

def get_vector_storage() -> VectorStorage:
    """Get the global vector storage instance"""
    global _vector_storage
    if _vector_storage is None:
        _vector_storage = VectorStorage()
    return _vector_storage

async def initialize_vector_storage() -> bool:
    """Initialize the vector storage system"""
    try:
        storage = get_vector_storage()
        # Ensure embedding model is loaded
        storage._initialize_embedding_model()
        logger.info("✅ Vector storage system initialized")
        return True
    except Exception as e:
        logger.error(f"❌ Error initializing vector storage: {e}")
        return False