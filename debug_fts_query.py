#!/usr/bin/env python3
"""
Debug FTS Query Generation
=========================
Debugs the exact FTS query being generated for user vs Luna queries
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_fts_query():
    print("🔍 Debugging FTS Query Generation")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    user_id = 921637353364287489
    
    # Test queries that should have different user filtering
    test_queries = [
        ("<PERSON>, what's my favorite color?", "Should filter by user - find <PERSON>'s facts"),
        ("<PERSON>, what's your favorite color?", "Should NOT filter by user - find <PERSON>'s facts"),
        ("<PERSON>, what's my favorite game?", "Should filter by user - find nothing")
    ]
    
    print(f"👤 Testing with User ID: {user_id}\n")
    
    for query, expected in test_queries:
        print(f"🔍 Query: '{query}'")
        print(f"   Expected: {expected}")
        
        # Manually replicate the logic to see what happens
        query_lower = query.lower()
        
        # Check user/Luna detection
        user_indicators = ['my ', 'mine', 'i like', 'i love', 'i prefer', 'i said', 'i told', 'what did i']
        is_user_query = any(indicator in query_lower for indicator in user_indicators)
        
        luna_indicators = ['luna', 'your ', 'you like', 'you love', 'you prefer', 'favorite', 'you enjoy', 'do you like', 'what do you']
        is_luna_query = any(indicator in query_lower for indicator in luna_indicators)
        
        if is_user_query:
            search_user_id = user_id
            classification = "USER query (filter by user)"
        elif is_luna_query and not is_user_query:
            search_user_id = None
            classification = "LUNA query (no user filter)"
        else:
            search_user_id = user_id
            classification = "DEFAULT (filter by user)"
        
        print(f"   Classification: {classification}")
        print(f"   search_user_id: {search_user_id}")
        
        # Test the actual search_facts call
        facts = ms.warm_storage.search_facts(
            query=query,
            user_id=search_user_id,
            limit=5
        )
        
        print(f"   Direct search_facts result: {len(facts)} facts")
        for fact in facts[:2]:
            print(f"     - {fact.content[:60]}... (user: {fact.user_id})")
        
        # Now test the full retrieve_relevant_context
        context = await ms.retrieve_relevant_context(
            query=query,
            user_id=user_id,
            channel_id=None,
            max_facts=5
        )
        
        print(f"   Full retrieve_relevant_context result: {len(context) if context else 0} chars")
        if context:
            lines = [line.strip() for line in context.split('\n') if line.strip()]
            for line in lines[:2]:
                print(f"     - {line[:60]}...")
        
        print()
    
    print(f"💡 Key Insights:")
    print(f"   - If direct search_facts works but retrieve_relevant_context doesn't, there's a bug in retrieve_relevant_context")
    print(f"   - If both return wrong results, the user filtering logic needs fixing")

if __name__ == "__main__":
    asyncio.run(debug_fts_query())