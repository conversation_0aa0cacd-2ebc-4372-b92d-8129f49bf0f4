"""
Local Text-to-Speech for VTube Studio Integration

This module provides local TTS functionality specifically for VTube Studio lip-sync.
It uses pyttsx3 to generate speech and routes it to a Virtual Audio Cable device
that VTube Studio can use for lip-sync, without interfering with Luna's Discord TTS.
"""

import asyncio
import logging
import threading
import time
from typing import Optional

try:
    import pyttsx3
    import pyttsx3.drivers
except ImportError:
    logging.error("pyttsx3 library not found. Please install it with: pip install pyttsx3")
    raise

from llm_response.config import VTUBE_STUDIO_ENABLED, VTUBE_STUDIO_AUDIO_DEVICE

logger = logging.getLogger(__name__)

class VTSLocalTTS:
    """Local TTS engine for VTube Studio lip-sync."""
    
    def __init__(self):
        self.engine_lock = threading.Lock()
        self.preferred_voice_id = None
        self.engine_settings = {
            'rate': 200,
            'volume': 0.8
        }
        self._find_preferred_voice()
    
    def _find_preferred_voice(self):
        """Find the preferred female voice for TTS."""
        try:
            temp_engine = pyttsx3.init()
            voices = temp_engine.getProperty('voices')
            if voices:
                # Look for a female voice
                for voice in voices:
                    if voice.name and ('female' in voice.name.lower() or 
                                     'woman' in voice.name.lower() or
                                     'zira' in voice.name.lower() or
                                     'hazel' in voice.name.lower()):
                        self.preferred_voice_id = voice.id
                        logger.info(f"✅ Preferred voice found: {voice.name}")
                        break
            
            # Clean up temp engine
            try:
                temp_engine.stop()
                del temp_engine
            except:
                pass
                
        except Exception as e:
            logger.warning(f"⚠️ Error finding preferred voice: {e}")
    
    def _create_and_configure_engine(self) -> Optional[pyttsx3.Engine]:
        """Create a fresh TTS engine and configure it."""
        try:
            # Create a fresh engine for each TTS call to avoid state issues
            engine = pyttsx3.init()
            
            if not engine:
                logger.error("❌ Failed to create pyttsx3 engine")
                return None
            
            # Configure engine properties
            engine.setProperty('rate', self.engine_settings['rate'])
            engine.setProperty('volume', self.engine_settings['volume'])
            
            # Set preferred voice if found
            if self.preferred_voice_id:
                try:
                    engine.setProperty('voice', self.preferred_voice_id)
                except Exception as e:
                    logger.warning(f"⚠️ Could not set preferred voice: {e}")
            
            return engine
            
        except Exception as e:
            logger.error(f"❌ Error creating TTS engine: {e}")
            return None
    
    def _speak_sync(self, text: str) -> bool:
        """Synchronously speak text using a fresh TTS engine."""
        try:
            with self.engine_lock:
                logger.debug(f"🔊 Speaking locally for VTS: '{text[:50]}...'")
                
                # Create a fresh engine for this TTS call
                engine = self._create_and_configure_engine()
                if not engine:
                    logger.error("❌ Could not create TTS engine")
                    return False
                
                try:
                    # Speak the text
                    engine.say(text)
                    engine.runAndWait()
                    
                    # Clean up the engine immediately
                    engine.stop()
                    del engine
                    
                    return True
                    
                except Exception as e:
                    logger.error(f"❌ Error during TTS speech: {e}")
                    # Try to clean up engine even if speech failed
                    try:
                        engine.stop()
                        del engine
                    except:
                        pass
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error in sync TTS: {e}", exc_info=True)
            return False
    
    async def speak_async(self, text: str) -> bool:
        """Asynchronously speak text using the TTS engine."""
        if not text or not text.strip():
            logger.warning("⚠️ Empty text provided to VTS TTS")
            return False
        
        # Clean the text for better TTS
        clean_text = self._clean_text_for_tts(text)
        
        # Run the synchronous TTS in a thread to avoid blocking
        try:
            success = await asyncio.to_thread(self._speak_sync, clean_text)
            if success:
                logger.info(f"✅ Successfully spoke text for VTS: '{clean_text[:30]}...'")
            return success
        except Exception as e:
            logger.error(f"❌ Error in async TTS: {e}", exc_info=True)
            return False
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean and prepare text for TTS to improve pronunciation."""
        import re
        
        # Remove Discord markdown
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # Italic
        text = re.sub(r'~~(.*?)~~', r'\1', text)      # Strikethrough
        text = re.sub(r'`(.*?)`', r'\1', text)        # Inline code
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)  # Code blocks
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', 
                     '', text)
        
        # Remove mentions and channels
        text = re.sub(r'<@[!&]?[0-9]+>', '', text)
        text = re.sub(r'<#[0-9]+>', '', text)
        
        # Remove emojis (basic Unicode emoji ranges)
        text = re.sub(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]', 
                     '', text)
        
        # Clean up extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Limit length to prevent extremely long TTS
        if len(text) > 500:
            text = text[:497] + "..."
            logger.debug("📝 Truncated long text for TTS")
        
        return text

# Global TTS instance
_vts_tts: Optional[VTSLocalTTS] = None

def get_vts_tts() -> VTSLocalTTS:
    """Get or create the global VTS TTS instance."""
    global _vts_tts
    if _vts_tts is None:
        _vts_tts = VTSLocalTTS()
    return _vts_tts

async def speak_locally_for_vts(text: str, vts_audio_output_device_name: str = None) -> bool:
    """
    Speak text locally for VTube Studio lip-sync.
    
    Args:
        text (str): The text to speak
        vts_audio_output_device_name (str): Audio device name (currently unused with pyttsx3,
                                           but kept for future compatibility)
    
    Returns:
        bool: True if successful, False otherwise
    """
    if not VTUBE_STUDIO_ENABLED:
        logger.debug("VTube Studio integration is disabled")
        return False
    
    if not text or not text.strip():
        logger.warning("⚠️ Empty text provided to VTS TTS")
        return False
    
    # Note: pyttsx3 doesn't directly support audio device selection
    # The audio will go to the system default audio device
    # For VB-CABLE to work, users need to set VB-CABLE as the default audio device
    # or use VB-CABLE's audio routing features
    if vts_audio_output_device_name:
        logger.debug(f"🔊 Target audio device: {vts_audio_output_device_name}")
        logger.debug("ℹ️ Note: Device selection not directly supported by pyttsx3")
        logger.debug("ℹ️ Ensure VB-CABLE is set as default audio device or use audio routing")
    
    tts = get_vts_tts()
    return await tts.speak_async(text)

def cleanup_vts_tts():
    """Clean up the VTS TTS resources."""
    global _vts_tts
    if _vts_tts:
        # No persistent engine to clean up since we create fresh engines for each call
        logger.debug("🧹 VTube Studio TTS cleanup completed")
        _vts_tts = None 