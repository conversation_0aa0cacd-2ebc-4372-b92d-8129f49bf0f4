#!/usr/bin/env python3
"""
Simple test for the Luna response filtering function.
"""

import re

def remove_luna_prefix(text):
    """
    Remove Luna name prefixes from responses to match training data format.
    
    Args:
        text (str): The response text that may contain Luna prefixes
        
    Returns:
        str: The text with <PERSON> prefixes removed
    """
    if not text:
        return text
    
    # Remove various Luna prefix patterns
    patterns = [
        r'^Luna:\s*',           # "Luna: message"
        r'^Luna\s*-\s*',        # "Luna - message"  
        r'^\*\*<PERSON>\*\*:\s*',   # "**Luna**: message"
        r'^\*\*Luna:\*\*\s*',   # "**Luna:**message"
        r'^<PERSON>\s*says:\s*',    # "<PERSON> says: message"
        r'^<PERSON>\s*responds:\s*' # "<PERSON> responds: message"
    ]
    
    cleaned_text = text
    for pattern in patterns:
        cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)
    
    return cleaned_text.strip()

def test_response_filtering():
    """Test the Luna prefix removal function"""
    print("=== RESPONSE FILTERING TEST ===")
    
    test_cases = [
        ("<PERSON>: Hi Gavin! How are you today?", "Hi <PERSON>! How are you today?"),
        ("Luna - hey what's up?", "hey what's up?"),
        ("**<PERSON>**: doing pretty good :)", "doing pretty good :)"),
        ("Luna says: that's awesome!", "that's awesome!"),
        ("hey! doing great, how about you?", "hey! doing great, how about you?"),  # No prefix
        ("Luna responds: lol nice", "lol nice"),
        ("", ""),  # Empty string
        ("luna: lowercase test", "lowercase test"),  # Case insensitive
        ("LUNA: uppercase test", "uppercase test"),  # Case insensitive
    ]
    
    print("Testing Luna prefix removal:")
    all_passed = True
    
    for input_text, expected in test_cases:
        result = remove_luna_prefix(input_text)
        passed = result == expected
        status = "✅" if passed else "❌"
        
        print(f"  {status} Input: '{input_text}'")
        print(f"     Expected: '{expected}'")
        print(f"     Got: '{result}'")
        print()
        
        if not passed:
            all_passed = False
    
    if all_passed:
        print("✅ All response filtering tests passed!")
        return True
    else:
        print("❌ Some response filtering tests failed")
        return False

if __name__ == "__main__":
    print("Luna Response Filter Test")
    print("=" * 40)
    
    success = test_response_filtering()
    
    if success:
        print("\n🎉 Response filtering is working correctly!")
        print("The function will remove Luna: prefixes from responses.")
    else:
        print("\n⚠️ Response filtering needs fixes.")
