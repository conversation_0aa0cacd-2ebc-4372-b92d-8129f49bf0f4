#!/usr/bin/env python3
"""
Debug Vegetable Query
====================
Traces exactly what happens when searching for <PERSON>'s favorite vegetable
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_vegetable_query():
    print("🥔 Debugging 'Luna, what's your favorite vegetable?' Query")
    print("=" * 60)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # The exact search terms from the logs
    query = "luna favorite vegetable"
    user_id = 921637353364287489
    
    print(f"🔍 Query: '{query}'")
    print(f"👤 User ID: {user_id}")
    
    # Step 1: Check what facts exist about <PERSON>'s food preferences
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        print(f"\n📊 Facts containing 'food', 'potatoes', or 'vegetable':")
        cursor = conn.execute("""
            SELECT id, content, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%food%' OR content LIKE '%potatoes%' OR content LIKE '%vegetable%'
            ORDER BY confidence DESC
        """)
        
        food_facts = cursor.fetchall()
        for i, fact in enumerate(food_facts, 1):
            print(f"  {i}. '{fact[1]}' (User: {fact[2]}, Conf: {fact[3]})")
        
        print(f"\n🔍 Testing FTS search with different strategies:")
        
        # Test 1: Exact query from logs
        print(f"\n1. Testing exact query: 'luna favorite vegetable'")
        query_terms = ["luna", "favorite", "vegetable"]
        
        # Add semantic expansions like the code does
        semantic_expansions = {
            'vegetable': ['food', 'potatoes', 'loves'],
            'vegetables': ['food', 'potatoes', 'loves'],
            'eat': ['food', 'loves', 'potatoes'],
            'enjoy': ['loves', 'likes', 'food'],
            'preference': ['favorite', 'likes', 'loves'],
            'preferences': ['favorite', 'likes', 'loves'],
            'prefer': ['favorite', 'likes'],
            'like': ['loves', 'likes', 'favorite']
        }
        
        expanded_terms = []
        for term in query_terms:
            expanded_terms.append(term)
            if term in semantic_expansions:
                expanded_terms.extend(semantic_expansions[term])
                print(f"   Expanded '{term}' → {semantic_expansions[term]}")
        
        # Remove duplicates
        seen = set()
        final_terms = []
        for term in expanded_terms:
            if term not in seen:
                final_terms.append(term)
                seen.add(term)
        
        print(f"   Final expanded terms: {final_terms}")
        
        # Test 2: Try AND logic with all terms
        valid_terms = [term for term in final_terms if len(term) > 2]
        escaped_terms = [f'"{term.replace('"', '""')}"' for term in valid_terms]
        and_query = ' AND '.join(escaped_terms)
        
        print(f"\n2. Testing AND query: {and_query}")
        try:
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 5
            """, (and_query,))
            
            and_results = cursor.fetchall()
            print(f"   AND results: {len(and_results)}")
            for result in and_results:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
        except Exception as e:
            print(f"   AND query failed: {e}")
        
        # Test 3: Try individual terms with priority
        term_priority = {
            'color': 10, 'purple': 10,
            'food': 10, 'potatoes': 10, 'loves': 9, 'likes': 9,
            'block': 10, 'minecraft': 10, 'cobblestone': 10, 'obsidian': 10,
            'favorite': 7,
            'luna': 3,
            'vegetable': 8  # Should be high priority!
        }
        
        print(f"\n3. Testing prioritized single terms:")
        prioritized_terms = []
        for term in valid_terms:
            if term in term_priority:
                priority = term_priority[term]
                prioritized_terms.append((priority, term))
                print(f"   '{term}' → priority {priority}")
        
        prioritized_terms.sort(reverse=True)
        
        if prioritized_terms:
            best_term = prioritized_terms[0][1]
            print(f"   Best term selected: '{best_term}'")
            
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 5
            """, (f'"{best_term}"',))
            
            best_results = cursor.fetchall()
            print(f"   Results for '{best_term}': {len(best_results)}")
            for result in best_results:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
        
        # Test 4: Try key expanded terms individually
        print(f"\n4. Testing key expanded terms individually:")
        key_terms = ['food', 'potatoes', 'loves']
        for term in key_terms:
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 3
            """, (f'"{term}"',))
            
            results = cursor.fetchall()
            print(f"   '{term}' → {len(results)} results:")
            for result in results:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
    
    finally:
        conn.close()
    
    # Test 5: What does the actual memory system return?
    print(f"\n5. Testing actual memory system:")
    context = await ms.retrieve_relevant_context(
        query=query,
        user_id=None,  # No user filter for Luna queries
        channel_id=None,
        max_facts=5
    )
    
    if context:
        print(f"   Memory system returned {len(context)} chars:")
        lines = [line.strip() for line in context.split('\n') if line.strip()]
        for i, line in enumerate(lines[:5], 1):
            print(f"     {i}. {line}")
    else:
        print(f"   Memory system returned no context")

if __name__ == "__main__":
    asyncio.run(debug_vegetable_query())