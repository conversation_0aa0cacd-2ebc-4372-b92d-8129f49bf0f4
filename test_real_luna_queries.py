#!/usr/bin/env python3
"""
Real Luna Query Test
===================
Tests the memory system exactly as it would work when <PERSON> is running
"""
import asyncio
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def test_real_luna_queries():
    print("🤖 Testing Real Luna Conversation Scenarios")
    print("=" * 60)
    
    # Initialize memory system exactly like <PERSON> does
    await initialize_memory_system()
    ms = get_memory_system()
    
    # User ID from your logs
    user_id = 921637353364287489
    
    # These are the exact queries that should work when you talk to <PERSON>
    real_queries = [
        "<PERSON>, what's your favorite color?",
        "Luna, what's your favorite food?", 
        "<PERSON>, what's your favorite block in Minecraft?",
        "<PERSON>, what's your favorite vegetable?",  # This was failing
        "What do you like to eat?",
        "What's your favorite thing?",
        "Do you like potatoes?",
        "Tell me about your preferences",
        "What color do you prefer?",
        "What foods do you enjoy?"
    ]
    
    print(f"👤 Testing with User ID: {user_id}")
    print(f"🧠 These should return <PERSON>'s personality facts, not user-specific facts\n")
    
    success_count = 0
    total_count = len(real_queries)
    
    for i, query in enumerate(real_queries, 1):
        print(f"🔍 Test {i}/{total_count}: '{query}'")
        
        # Test exactly like the real system does
        start_time = time.time()
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=user_id,
            current_channel_id=12345,
            is_dm_channel=False
        )
        retrieval_time = (time.time() - start_time) * 1000
        
        if context and context.strip():
            # Check if it's actually about Luna, not about the user
            has_luna_facts = any(
                keyword in context.lower() 
                for keyword in ['luna', 'purple', 'potatoes', 'cobblestone', 'obsidian']
            )
            
            has_user_facts = 'gavin said' in context.lower()
            
            if has_luna_facts and not has_user_facts:
                print(f"  ✅ SUCCESS ({retrieval_time:.2f}ms): Found Luna facts")
                success_count += 1
                
                # Show relevant facts found
                lines = [line.strip() for line in context.split('\n') if line.strip()]
                relevant_lines = [line for line in lines if any(keyword in line.lower() for keyword in ['luna', 'purple', 'potato', 'cobblestone'])]
                for line in relevant_lines[:2]:
                    print(f"      📝 {line}")
                    
            elif has_user_facts:
                print(f"  ❌ WRONG ({retrieval_time:.2f}ms): Found user facts instead of Luna facts")
                print(f"      📝 {context.split()[0]} (should be about Luna, not Gavin)")
            else:
                print(f"  ⚠️  PARTIAL ({retrieval_time:.2f}ms): Found facts but unclear relevance")
                print(f"      📝 {context[:80]}...")
        else:
            print(f"  ❌ FAILED ({retrieval_time:.2f}ms): No memory context found")
        
        print()
    
    # Test some queries that should NOT return Luna facts (user-specific queries)
    print(f"🔒 Testing User-Specific Queries (should filter by user):")
    user_queries = [
        "What did I say yesterday?",
        "Do you remember our conversation?",
        "What do I like?"
    ]
    
    for query in user_queries:
        print(f"🔍 User query: '{query}'")
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=user_id,
            current_channel_id=12345,
            is_dm_channel=False
        )
        
        if context:
            print(f"  📝 Found: {context[:60]}...")
        else:
            print(f"  ✅ Correctly found nothing (as expected)")
        print()
    
    # Summary
    success_rate = (success_count / total_count) * 100
    print(f"📊 RESULTS SUMMARY:")
    print(f"   ✅ Successful queries: {success_count}/{total_count} ({success_rate:.1f}%)")
    print(f"   🎯 Target: 100% success rate for Luna personality queries")
    
    if success_rate < 100:
        print(f"\n🔧 ISSUES TO FIX:")
        print(f"   - Queries failing to find Luna's facts")
        print(f"   - Queries returning wrong facts (user instead of Luna)")
        print(f"   - Need to improve query mapping (vegetable → potatoes)")
    else:
        print(f"\n🎉 ALL TESTS PASSED! Luna's memory system is working correctly!")
    
    return success_rate >= 90  # 90% success rate is acceptable

if __name__ == "__main__":
    asyncio.run(test_real_luna_queries())