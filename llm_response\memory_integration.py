"""
Luna Memory System Integration
=============================

Integration layer that connects the new ultra-low latency memory system
with Luna's existing conversation processing pipeline.

This module provides:
- Drop-in replacement for existing RAG functions
- Background memory processing hooks
- Performance monitoring and optimization
- Seamless migration from FTS5 to new memory system
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any

# Import existing Luna components
from .rag_utils import get_relevant_memory_context as legacy_get_memory_context
from .processing import log_prompt_to_electron, log_latency_to_electron
from .db_logger import log_message_to_db

# Import new memory system components
from .memory_system import get_memory_system, initialize_memory_system, LunaMemorySystem
from .fact_extraction import get_fact_extractor, ExtractionContext, FactExtractor

logger = logging.getLogger(__name__)

class MemoryIntegration:
    """Integration layer for the new memory system"""
    
    def __init__(self):
        self.memory_system: Optional[LunaMemorySystem] = None
        self.fact_extractor: Optional[FactExtractor] = None
        self.enabled = True
        self.fallback_to_legacy = True
        self.performance_mode = "hybrid"  # "legacy", "new", "hybrid"
        
        # Performance tracking
        self.stats = {
            'new_system_calls': 0,
            'legacy_fallbacks': 0,
            'avg_response_time': 0.0,
            'background_processing_count': 0
        }
    
    async def initialize(self) -> bool:
        """Initialize the memory integration system"""
        try:
            # Initialize memory system
            success = await initialize_memory_system()
            if success:
                self.memory_system = get_memory_system()
                self.fact_extractor = get_fact_extractor()
                logger.info("✅ Memory integration initialized successfully")
                return True
            else:
                logger.warning("⚠️ Memory system initialization failed, using legacy only")
                self.enabled = False
                return False
        except Exception as e:
            logger.error(f"❌ Error initializing memory integration: {e}", exc_info=True)
            self.enabled = False
            return False
    
    async def get_relevant_context(self, query: str, user_id: int, 
                                 channel_id: Optional[int] = None, 
                                 is_dm_channel: bool = False,
                                 max_facts: int = 5) -> str:
        """
        Enhanced context retrieval with fallback to legacy system
        """
        start_time = time.time()
        
        try:
            # Use new system if enabled and available
            if self.enabled and self.memory_system and self.performance_mode in ["new", "hybrid"]:
                try:
                    context = await self.memory_system.retrieve_relevant_context(
                        query=query,
                        user_id=user_id,
                        channel_id=str(channel_id) if channel_id else None,
                        max_facts=max_facts
                    )
                    
                    response_time = (time.time() - start_time) * 1000
                    self.stats['new_system_calls'] += 1
                    self.stats['avg_response_time'] = (
                        (self.stats['avg_response_time'] * (self.stats['new_system_calls'] - 1) + response_time) /
                        self.stats['new_system_calls']
                    )
                    
                    logger.debug(f"🧠 New memory system: {response_time:.1f}ms, {len(context)} chars")
                    
                    # Hybrid mode: supplement with legacy if new system returns little
                    if self.performance_mode == "hybrid" and len(context.strip()) < 50:
                        legacy_context = await self._get_legacy_context(
                            query, user_id, channel_id, is_dm_channel
                        )
                        if legacy_context:
                            context = f"{context}\\n{legacy_context}" if context else legacy_context
                            logger.debug("🔄 Supplemented with legacy context")
                    
                    return context
                    
                except Exception as e:
                    logger.warning(f"New memory system failed, falling back to legacy: {e}")
                    self.stats['legacy_fallbacks'] += 1
                    if self.fallback_to_legacy:
                        return await self._get_legacy_context(query, user_id, channel_id, is_dm_channel)
                    return ""
            
            # Use legacy system
            else:
                return await self._get_legacy_context(query, user_id, channel_id, is_dm_channel)
                
        except Exception as e:
            logger.error(f"Error in memory context retrieval: {e}", exc_info=True)
            return ""
    
    async def _get_legacy_context(self, query: str, user_id: int, 
                                 channel_id: Optional[int], is_dm_channel: bool) -> str:
        """Get context using the legacy RAG system"""
        try:
            context = await legacy_get_memory_context(
                query_text=query,
                current_user_id=user_id,
                current_channel_id=channel_id,
                is_dm_channel=is_dm_channel
            )
            logger.debug(f"📚 Legacy system: {len(context)} chars")
            return context
        except Exception as e:
            logger.error(f"Legacy memory context failed: {e}", exc_info=True)
            return ""
    
    async def process_conversation_for_memory(self, 
                                            messages: List[Dict[str, Any]],
                                            user_id: int,
                                            channel_id: str,
                                            channel_type: str = "text") -> bool:
        """
        Process conversation for memory extraction (background processing)
        """
        if not self.enabled or not self.memory_system:
            return False
        
        try:
            # Queue for background processing to avoid blocking responses
            await self.memory_system.process_conversation_chunk(
                messages=messages,
                user_id=user_id,
                channel_id=channel_id
            )
            
            self.stats['background_processing_count'] += 1
            logger.debug(f"🔄 Queued conversation for memory processing")
            return True
            
        except Exception as e:
            logger.error(f"Error queuing conversation for memory processing: {e}", exc_info=True)
            return False
    
    async def process_message_immediately(self,
                                        message_content: str,
                                        user_id: int, 
                                        channel_id: str,
                                        channel_type: str = "text",
                                        conversation_history: List[Dict[str, Any]] = None) -> List[Any]:
        """
        Immediately process a single message for memory extraction
        (for important messages that need immediate processing)
        """
        if not self.enabled or not self.fact_extractor:
            return []
        
        try:
            # Create extraction context
            context = ExtractionContext(
                user_id=user_id,
                channel_id=channel_id,
                channel_type=channel_type,
                conversation_history=conversation_history or [],
                current_message={"content": message_content, "user_id": user_id}
            )
            
            # Extract facts immediately
            facts = await self.fact_extractor.extract_facts_from_conversation(context)
            
            # Store facts if memory system is available
            if self.memory_system and facts:
                for fact in facts:
                    self.memory_system.warm_storage.store_fact(fact)
                    
                    # Update graph
                    for entity in fact.entities:
                        self.memory_system.memory_graph.add_node(entity, {
                            'type': 'entity',
                            'last_seen': time.time(),
                            'user_id': user_id
                        })
            
            logger.debug(f"🚀 Immediately processed message, extracted {len(facts)} facts")
            return facts
            
        except Exception as e:
            logger.error(f"Error in immediate message processing: {e}", exc_info=True)
            return []
    
    async def get_user_memory_summary(self, user_id: int, limit: int = 10) -> Dict[str, Any]:
        """Get a summary of what Luna remembers about a specific user"""
        if not self.enabled or not self.memory_system:
            return {"error": "Memory system not available"}
        
        try:
            # Get facts about the user from warm storage
            facts = self.memory_system.warm_storage.search_facts(
                user_id=user_id,
                limit=limit
            )
            
            # Organize by memory type
            summary = {
                "user_id": user_id,
                "total_facts": len(facts),
                "facts_by_type": {},
                "recent_facts": [],
                "entities": set(),
                "relationships": []
            }
            
            for fact in facts:
                # Group by type
                fact_type = fact.memory_type.value
                if fact_type not in summary["facts_by_type"]:
                    summary["facts_by_type"][fact_type] = []
                summary["facts_by_type"][fact_type].append({
                    "content": fact.content,
                    "confidence": fact.confidence,
                    "timestamp": fact.timestamp
                })
                
                # Recent facts (last 5)
                if len(summary["recent_facts"]) < 5:
                    summary["recent_facts"].append({
                        "type": fact_type,
                        "content": fact.content,
                        "confidence": fact.confidence
                    })
                
                # Collect entities and relationships
                summary["entities"].update(fact.entities)
                summary["relationships"].extend(fact.relationships)
            
            # Convert set to list for JSON serialization
            summary["entities"] = list(summary["entities"])
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting user memory summary: {e}", exc_info=True)
            return {"error": str(e)}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for the memory integration"""
        base_stats = self.stats.copy()
        
        if self.memory_system:
            memory_stats = self.memory_system.get_performance_stats()
            base_stats.update(memory_stats)
        
        base_stats.update({
            "enabled": self.enabled,
            "performance_mode": self.performance_mode,
            "fallback_enabled": self.fallback_to_legacy
        })
        
        return base_stats
    
    async def optimize_performance(self):
        """Automatically optimize performance based on usage patterns"""
        stats = self.get_performance_stats()
        
        # If legacy fallbacks are high, consider switching modes
        total_calls = stats['new_system_calls'] + stats['legacy_fallbacks']
        if total_calls > 50:
            fallback_rate = stats['legacy_fallbacks'] / total_calls
            
            if fallback_rate > 0.3:  # 30% fallback rate
                logger.warning(f"High fallback rate ({fallback_rate:.1%}), consider performance tuning")
            
            # If average response time is high, adjust caching
            if stats['avg_response_time'] > 10.0:  # 10ms target
                logger.info(f"High average response time ({stats['avg_response_time']:.1f}ms), optimizing cache")
                if self.memory_system:
                    # Increase cache size
                    self.memory_system.hot_cache.max_size = min(2000, self.memory_system.hot_cache.max_size * 2)
    
    async def shutdown(self):
        """Shutdown the memory integration system"""
        if self.memory_system:
            await self.memory_system.stop_background_processing()
        logger.info("🔻 Memory integration shutdown complete")

# Global integration instance
_memory_integration: Optional[MemoryIntegration] = None

def get_memory_integration() -> MemoryIntegration:
    """Get the global memory integration instance"""
    global _memory_integration
    if _memory_integration is None:
        _memory_integration = MemoryIntegration()
    return _memory_integration

async def initialize_memory_integration() -> bool:
    """Initialize the memory integration system"""
    integration = get_memory_integration()
    return await integration.initialize()

# Enhanced context retrieval function (drop-in replacement for existing function)
async def get_relevant_memory_context(query_text: str, 
                                    current_user_id: Optional[int],
                                    current_channel_id: Optional[int],
                                    is_dm_channel: bool) -> str:
    """
    Enhanced memory context retrieval with new system integration
    Drop-in replacement for the existing function in rag_utils.py
    """
    integration = get_memory_integration()
    
    return await integration.get_relevant_context(
        query=query_text,
        user_id=current_user_id,
        channel_id=current_channel_id,
        is_dm_channel=is_dm_channel
    )

# Hook for processing conversations in the background
async def process_conversation_memory(messages: List[Dict[str, Any]],
                                    user_id: int,
                                    channel_id: str,
                                    channel_type: str = "text") -> bool:
    """
    Process conversation for memory extraction (background)
    Call this after processing user messages
    """
    integration = get_memory_integration()
    return await integration.process_conversation_for_memory(
        messages=messages,
        user_id=user_id,
        channel_id=channel_id,
        channel_type=channel_type
    )

# Hook for immediate memory processing of important messages
async def process_important_message(message_content: str,
                                  user_id: int,
                                  channel_id: str,
                                  channel_type: str = "text",
                                  conversation_history: List[Dict[str, Any]] = None):
    """
    Immediately process important messages for memory extraction
    Use for messages that contain critical information
    """
    integration = get_memory_integration()
    return await integration.process_message_immediately(
        message_content=message_content,
        user_id=user_id,
        channel_id=channel_id,
        channel_type=channel_type,
        conversation_history=conversation_history
    )