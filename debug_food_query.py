#!/usr/bin/env python3
"""
Debug Food Query
===============
Debugs why 'luna favorite food' isn't finding potatoes facts
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_food_query():
    print("🥔 Debugging 'luna favorite food' Query")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Test the actual memory system call
    print("🧪 Testing memory system call:")
    context = await ms.retrieve_relevant_context(
        query="luna favorite food",
        user_id=921637353364287489,
        channel_id=None,
        max_facts=5
    )
    print(f"Result: '{context}'")
    
    # Debug the search_facts call directly
    print(f"\n🔍 Testing search_facts directly:")
    facts = ms.warm_storage.search_facts(
        query="luna favorite food",
        user_id=None,  # No user filter for Luna queries
        limit=10
    )
    print(f"Found {len(facts)} facts:")
    for i, fact in enumerate(facts, 1):
        print(f"  {i}. {fact.content} (conf: {fact.confidence})")
    
    # Test the database directly with our improved logic
    print(f"\n🔍 Testing FTS directly:")
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # Test what our new logic should do
        query_terms = ['luna', 'favorite', 'food']
        
        # Step 1: Try AND all terms
        print("1. Trying AND all terms: luna AND favorite AND food")
        cursor = conn.execute("""
            SELECT COUNT(*) FROM memory_facts_fts WHERE memory_facts_fts MATCH ?
        """, ('"luna" AND "favorite" AND "food"',))
        count = cursor.fetchone()[0]
        print(f"   Count: {count}")
        
        if count == 0:
            print("2. AND failed, trying important terms individually:")
            important_terms = ['luna', 'favorite', 'food']
            for term in important_terms:
                cursor = conn.execute("""
                    SELECT mf.content, mf.confidence 
                    FROM memory_facts mf
                    WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                    LIMIT 3
                """, (f'"{term}"',))
                results = cursor.fetchall()
                print(f"   '{term}' -> {len(results)} results:")
                for result in results:
                    print(f"     {result[0][:60]}... (conf: {result[1]})")
        
        # Test what happens when we search for food-related terms
        print(f"\n3. Testing food-related fallbacks:")
        food_terms = ['food', 'potatoes', 'loves', 'eats']
        for term in food_terms:
            cursor = conn.execute("""
                SELECT mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 2
            """, (f'"{term}"',))
            results = cursor.fetchall()
            print(f"   '{term}' -> {len(results)} results:")
            for result in results:
                if 'luna' in result[0].lower():
                    print(f"     ✅ {result[0][:60]}... (conf: {result[1]})")
                else:
                    print(f"     ❌ {result[0][:60]}... (conf: {result[1]})")
    
    finally:
        conn.close()
    
    print(f"\n💡 Issue Analysis:")
    print(f"   - The fallback is choosing 'luna' term instead of 'food' term")
    print(f"   - We need to prioritize terms that give better Luna facts")
    print(f"   - 'food' should rank higher than 'luna' for food queries")

if __name__ == "__main__":
    asyncio.run(debug_food_query())