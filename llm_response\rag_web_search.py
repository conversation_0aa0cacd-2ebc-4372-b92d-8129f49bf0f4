"""
Web Search RAG Module for Luna Discord Bot

Integrates Google Custom Search API with vector-based caching system for efficient web search functionality.
Uses FAISS for similarity search and Redis-style caching to avoid repeated API calls.
Summarizes search results using the same LLM model as the brain system.
"""

import os
import json
import hashlib
import logging
import asyncio
import pickle
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import aiohttp
import numpy as np
from llama_cpp import Llama
import faiss
from sentence_transformers import SentenceTransformer
from dataclasses import dataclass
import re
try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None
try:
    import tiktoken
except ImportError:
    tiktoken = None

# Load environment variables from local.env
try:
    from dotenv import load_dotenv
    load_dotenv('local.env')
except ImportError:
    pass  # dotenv not available, use system env vars

# Import thread-safe model wrappers instead of direct brain access
try:
    # Use the same thread-safe wrappers as processing.py
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from shared_model import call_model_safe
    from .initialization import get_llama_cpp_client
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from shared_model import call_model_safe
    from initialization import get_llama_cpp_client

try:
    from .config import (
        GEMMA_CPP_MODEL_PATH,
        WEB_SEARCH_MAX_RESULTS,
        WEB_SEARCH_CACHE_EXPIRY_DAYS,
        WEB_SEARCH_SIMILARITY_THRESHOLD,
        WEB_SEARCH_MAX_CACHE_SIZE,
        WEB_SEARCH_SUMMARY_MAX_TOKENS,
        WEB_SEARCH_SUMMARY_TEMPERATURE,
        WEB_SEARCH_RESULTS_FOR_SUMMARY,
        WEB_SEARCH_ENABLE_CACHE
    )
except ImportError:
    from config import (
        GEMMA_CPP_MODEL_PATH,
        WEB_SEARCH_MAX_RESULTS,
        WEB_SEARCH_CACHE_EXPIRY_DAYS,
        WEB_SEARCH_SIMILARITY_THRESHOLD,
        WEB_SEARCH_MAX_CACHE_SIZE,
        WEB_SEARCH_SUMMARY_MAX_TOKENS,
        WEB_SEARCH_SUMMARY_TEMPERATURE,
        WEB_SEARCH_RESULTS_FOR_SUMMARY,
        WEB_SEARCH_ENABLE_CACHE
    )

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Represents a single web search result"""
    title: str
    snippet: str
    url: str
    timestamp: datetime
    
    def to_dict(self) -> dict:
        return {
            'title': self.title,
            'snippet': self.snippet,
            'url': self.url,
            'timestamp': self.timestamp.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'SearchResult':
        return cls(
            title=data['title'],
            snippet=data['snippet'],
            url=data['url'],
            timestamp=datetime.fromisoformat(data['timestamp'])
        )

@dataclass 
class CachedSearch:
    """Represents a cached search with results and summary"""
    query: str
    query_hash: str
    results: List[SearchResult]
    summary: str
    timestamp: datetime
    embedding: np.ndarray
    
    def to_dict(self) -> dict:
        return {
            'query': self.query,
            'query_hash': self.query_hash,
            'results': [r.to_dict() for r in self.results],
            'summary': self.summary,
            'timestamp': self.timestamp.isoformat(),
            'embedding': self.embedding.tolist()
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'CachedSearch':
        return cls(
            query=data['query'],
            query_hash=data['query_hash'],
            results=[SearchResult.from_dict(r) for r in data['results']],
            summary=data['summary'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            embedding=np.array(data['embedding'])
        )

class WebSearchRAG:
    """Web Search RAG system with vector caching and LLM summarization"""
    
    def __init__(self):
        # Google Custom Search configuration
        self.google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.google_cx = os.getenv('GOOGLE_SEARCH_CX')
        
        # HARDCODED FALLBACK OPTIONS (for testing/debugging)
        # Uncomment and fill these in if environment variables don't work:
        # self.google_api_key = "your_api_key_here"
        # self.google_cx = "your_search_engine_cx_here"
        
        # Debug: Log credential status (without exposing actual keys)
        api_key_status = "SET" if self.google_api_key else "NOT SET"
        cx_status = "SET" if self.google_cx else "NOT SET"
        logger.info(f"Google API credentials - API_KEY: {api_key_status}, CX: {cx_status}")
        if self.google_api_key:
            logger.info(f"API key length: {len(self.google_api_key)} characters")
        if self.google_cx:
            logger.info(f"Search CX length: {len(self.google_cx)} characters")
        
        # Embedding model for similarity search
        self.embedding_model = None
        self.faiss_index = None
        self.cached_searches: Dict[str, CachedSearch] = {}
        
        # Cache configuration (from config.py)
        self.cache_dir = "web_search_cache"
        self.cache_file = os.path.join(self.cache_dir, "search_cache.pkl")
        self.faiss_index_file = os.path.join(self.cache_dir, "search_index.faiss")
        self.max_cache_size = WEB_SEARCH_MAX_CACHE_SIZE
        self.cache_expiry_days = WEB_SEARCH_CACHE_EXPIRY_DAYS
        
        # Search configuration (from config.py)
        self.max_results = WEB_SEARCH_MAX_RESULTS
        self.min_similarity_threshold = WEB_SEARCH_SIMILARITY_THRESHOLD
        
        # Summarization configuration (from config.py)
        self.summary_max_tokens = WEB_SEARCH_SUMMARY_MAX_TOKENS
        self.summary_temperature = WEB_SEARCH_SUMMARY_TEMPERATURE
        self.results_for_summary = WEB_SEARCH_RESULTS_FOR_SUMMARY
        
        # Cache control (from config.py)
        self.cache_enabled = WEB_SEARCH_ENABLE_CACHE
        
        # Log configuration for debugging
        logger.info(f"Web Search Configuration:")
        logger.info(f"  Max Results: {self.max_results}")
        logger.info(f"  Results for Summary: {self.results_for_summary}")
        logger.info(f"  Similarity Threshold: {self.min_similarity_threshold}")
        logger.info(f"  Cache Expiry: {self.cache_expiry_days} days")
        logger.info(f"  Summary Max Tokens: {self.summary_max_tokens}")
        logger.info(f"  Cache Enabled: {self.cache_enabled}")
        logger.info(f"  Summary Temperature: {self.summary_temperature}")
        
        # Initialize components
        self._initialize_cache_dir()
        self._load_embedding_model()
        self._load_cache()
    
    def _initialize_cache_dir(self):
        """Create cache directory if it doesn't exist"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            logger.info(f"Created web search cache directory: {self.cache_dir}")
    
    def _load_embedding_model(self):
        """Load sentence transformer model for embeddings"""
        try:
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Loaded sentence embedding model for web search")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            # Fallback to simple hash-based matching without embeddings
            self.embedding_model = None
    
    def _load_cache(self):
        """Load existing cache and FAISS index"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self.cached_searches = {
                        k: CachedSearch.from_dict(v) for k, v in cache_data.items()
                    }
                logger.info(f"Loaded {len(self.cached_searches)} cached web searches")
            
            if os.path.exists(self.faiss_index_file) and self.embedding_model:
                self.faiss_index = faiss.read_index(self.faiss_index_file)
                logger.info(f"Loaded FAISS index with {self.faiss_index.ntotal} entries")
            elif self.embedding_model:
                # Create new FAISS index
                dimension = 384  # all-MiniLM-L6-v2 embedding dimension
                self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
                logger.info("Created new FAISS index for web search")
                
        except Exception as e:
            logger.error(f"Error loading web search cache: {e}")
            self.cached_searches = {}
            if self.embedding_model:
                dimension = 384
                self.faiss_index = faiss.IndexFlatIP(dimension)
    
    def _save_cache(self):
        """Save cache and FAISS index to disk"""
        try:
            # Clean expired entries first
            self._clean_expired_entries()
            
            # Save cache
            cache_data = {k: v.to_dict() for k, v in self.cached_searches.items()}
            with open(self.cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            # Save FAISS index
            if self.faiss_index and self.embedding_model:
                faiss.write_index(self.faiss_index, self.faiss_index_file)
            
            logger.info(f"Saved web search cache with {len(self.cached_searches)} entries")
        except Exception as e:
            logger.error(f"Error saving web search cache: {e}")
    
    def _clean_expired_entries(self):
        """Remove expired cache entries"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, cached_search in self.cached_searches.items():
            if (current_time - cached_search.timestamp).days > self.cache_expiry_days:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cached_searches[key]
        
        if expired_keys:
            logger.info(f"Removed {len(expired_keys)} expired cache entries")
            # Rebuild FAISS index after cleanup
            self._rebuild_faiss_index()
    
    def _rebuild_faiss_index(self):
        """Rebuild FAISS index after cache cleanup"""
        if not self.embedding_model:
            return
            
        try:
            dimension = 384
            self.faiss_index = faiss.IndexFlatIP(dimension)
            
            embeddings = []
            for cached_search in self.cached_searches.values():
                embeddings.append(cached_search.embedding)
            
            if embeddings:
                embeddings_array = np.vstack(embeddings).astype('float32')
                self.faiss_index.add(embeddings_array)
            
            logger.info(f"Rebuilt FAISS index with {len(embeddings)} entries")
        except Exception as e:
            logger.error(f"Error rebuilding FAISS index: {e}")
    
    def _generate_query_hash(self, query: str) -> str:
        """Generate consistent hash for query"""
        normalized_query = re.sub(r'\s+', ' ', query.lower().strip())
        return hashlib.md5(normalized_query.encode()).hexdigest()
    
    def _get_query_embedding(self, query: str) -> Optional[np.ndarray]:
        """Get embedding for query"""
        if not self.embedding_model:
            return None
        try:
            embedding = self.embedding_model.encode(query, normalize_embeddings=True)
            return embedding.astype('float32')
        except Exception as e:
            logger.error(f"Error generating query embedding: {e}")
            return None
    
    def _find_similar_cached_search(self, query: str, query_embedding: Optional[np.ndarray]) -> Optional[CachedSearch]:
        """Find similar cached search using FAISS"""
        if query_embedding is not None and self.faiss_index and self.faiss_index.ntotal > 0:
            try:
                # Search for most similar cached query
                query_embedding = query_embedding.reshape(1, -1)
                distances, indices = self.faiss_index.search(query_embedding, 1)
                
                if len(distances[0]) > 0 and distances[0][0] >= self.min_similarity_threshold:
                    # Find the cached search corresponding to this index
                    cached_searches_list = list(self.cached_searches.values())
                    if indices[0][0] < len(cached_searches_list):
                        similar_search = cached_searches_list[indices[0][0]]
                        logger.info(f"Found similar cached search: '{similar_search.query}' (similarity: {distances[0][0]:.3f})")
                        return similar_search
            except Exception as e:
                logger.error(f"Error finding similar cached search: {e}")
        
        # Fallback to exact hash matching
        query_hash = self._generate_query_hash(query)
        if query_hash in self.cached_searches:
            cached_search = self.cached_searches[query_hash]
            logger.info(f"Found exact cached search for: '{query}'")
            return cached_search
        
        return None
    
    async def _perform_google_search(self, query: str) -> List[SearchResult]:
        """Perform Google Custom Search API call"""
        if not self.google_api_key or not self.google_cx:
            logger.error("Google API key or CX not configured for web search")
            return []
        
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.google_api_key,
                'cx': self.google_cx,
                'q': query,
                'num': self.max_results
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        for item in data.get('items', []):
                            result = SearchResult(
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                url=item.get('link', ''),
                                timestamp=datetime.now()
                            )
                            results.append(result)
                        
                        logger.info(f"Google search returned {len(results)} results for: '{query}'")
                        return results
                    else:
                        logger.error(f"Google search failed with status {response.status}")
                        return []
        
        except Exception as e:
            logger.error(f"Error performing Google search: {e}")
            return []
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        if tiktoken:
            try:
                # Use a generic tokenizer - adjust encoding name as needed
                encoding = tiktoken.get_encoding("cl100k_base")
                return len(encoding.encode(text))
            except Exception:
                pass
        # Fallback: rough estimate (1 token ≈ 0.75 words)
        return len(text.split()) * 4 // 3
    
    async def _fetch_page_content(self, url: str, timeout: int = 10) -> str:
        """Fetch and extract main content from a webpage"""
        if not BeautifulSoup:
            return ""  # BeautifulSoup not available
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.get(url, headers={'User-Agent': 'Mozilla/5.0 (compatible; LunaBot/1.0)'}) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # Remove script and style elements
                        for script in soup(["script", "style", "nav", "header", "footer", "aside"]):
                            script.decompose()
                        
                        # Try to find main content areas
                        content_selectors = [
                            'main', 'article', '.content', '#content', '.post-content', 
                            '.entry-content', '.article-content', '.page-content'
                        ]
                        
                        main_content = None
                        for selector in content_selectors:
                            main_content = soup.select_one(selector)
                            if main_content:
                                break
                        
                        if not main_content:
                            # Fallback to body
                            main_content = soup.find('body')
                        
                        if main_content:
                            # Extract text and clean it up
                            text = main_content.get_text(separator=' ', strip=True)
                            # Clean up excessive whitespace
                            text = re.sub(r'\s+', ' ', text)
                            # Limit length to avoid extremely long content
                            return text[:4000]  # Reasonable limit
                        
        except Exception as e:
            logger.debug(f"Failed to fetch content from {url}: {e}")
        
        return ""
    
    async def _summarize_search_results(self, query: str, results: List[SearchResult]) -> str:
        """Summarize search results using brain LLM"""
        if not results:
            return "No search results found."
        
        try:
            # Prepare enhanced context from search results with full page content
            context_parts = []
            max_prompt_tokens = 3500  # Leave room for summary output
            
            # Start with prompt template to estimate base token usage
            prompt_intro = f"""You are Luna, a helpful AI assistant. Your job is to provide a comprehensive answer based ONLY on the information that is directly relevant to the user's request.

REQUEST: "{query}"

Below are detailed web search results. Analyze them carefully and provide a complete, informative answer that addresses the request. Important guidelines:
- Focus ONLY on information directly related to the request
- Do NOT suggest visiting websites or provide links - give the complete answer here
- Be thorough: include all relevant facts, numbers, explanations, and context
- If multiple sources provide details, combine them for a comprehensive answer
- If sources contradict each other, explain the different perspectives
- If the request involves specific games, software, or technical terms, focus on that exact context

DETAILED SEARCH RESULTS:
"""
            
            prompt_outro = "\n\nCOMPREHENSIVE ANSWER (provide complete information, do NOT refer to websites):"
            
            current_tokens = self._estimate_tokens(prompt_intro + prompt_outro)
            logger.info(f"Building enhanced context for {len(results)} results (token budget: {max_prompt_tokens})")
            
            # Process results and fetch full content
            for i, result in enumerate(results[:self.results_for_summary], 1):
                # Fetch full page content
                full_content = await self._fetch_page_content(result.url)
                
                # Combine all available information
                title = result.title or "No title"
                snippet = result.snippet or "No snippet"
                meta_info = f"Source: {result.url}"
                
                # Build comprehensive result entry
                if full_content:
                    result_text = f"{i}. **{title}**\n   Original snippet: {snippet}\n   Full content: {full_content}\n   {meta_info}"
                else:
                    result_text = f"{i}. **{title}**\n   Content: {snippet}\n   {meta_info}"
                
                # Check if adding this result would exceed token limit
                result_tokens = self._estimate_tokens(result_text)
                if current_tokens + result_tokens > max_prompt_tokens:
                    logger.info(f"Reached token limit. Using {i-1} results out of {len(results)} for summarization")
                    break
                
                context_parts.append(result_text)
                current_tokens += result_tokens
                logger.debug(f"Added result {i}, current tokens: {current_tokens}")
            
            context = "\n\n".join(context_parts)
            logger.info(f"Final context: {len(context_parts)} results, ~{current_tokens} tokens")
            
            # Create final prompt
            prompt = prompt_intro + context + prompt_outro
            
            # Use thread-safe model wrapper for summarization  
            brain_client = get_llama_cpp_client()
            if brain_client:
                # Use thread-safe wrapper to prevent concurrent access issues
                response = call_model_safe(
                    brain_client,
                    prompt,
                    max_tokens=self.summary_max_tokens,
                    temperature=self.summary_temperature,
                    stop=["USER:", "ASSISTANT:", "\n\n\n"],
                    stream=False
                )
                
                if response and 'choices' in response:
                    summary = response['choices'][0]['text'].strip()
                    logger.info(f"Generated search summary for: '{query}'")
                    return summary
                else:
                    logger.warning("Thread-safe brain model returned empty response for search summarization")
            else:
                logger.warning("Thread-safe brain model not available for search summarization")
            
            # Fallback: simple concatenation
            return f"Found information about {query}: " + " ".join([r.snippet[:100] for r in results[:2]])
            
        except Exception as e:
            logger.error(f"Error summarizing search results: {e}")
            # Fallback summary
            return f"Found {len(results)} search results about {query}. " + results[0].snippet[:100] if results else "No results found."
    
    async def search_web(self, query: str) -> Optional[str]:
        """Main web search function with caching"""
        try:
            logger.info(f"Web search requested for: '{query}'")
            
            # Generate embedding for similarity search (only if cache is enabled)
            query_embedding = None
            cached_search = None
            
            if self.cache_enabled:
                query_embedding = self._get_query_embedding(query)
                
                # Check for cached results
                cached_search = self._find_similar_cached_search(query, query_embedding)
                if cached_search:
                    logger.info(f"Using cached search results for: '{query}'")
                    return cached_search.summary
            else:
                logger.info(f"Cache disabled - performing fresh search for: '{query}'")
            
            # Perform new search
            results = await self._perform_google_search(query)
            if not results:
                return "I couldn't find any search results for that query."
            
            # Summarize results
            summary = await self._summarize_search_results(query, results)
            
            # Cache the results (only if caching is enabled)
            if self.cache_enabled:
                query_hash = self._generate_query_hash(query)
                cached_search = CachedSearch(
                    query=query,
                    query_hash=query_hash,
                    results=results,
                    summary=summary,
                    timestamp=datetime.now(),
                    embedding=query_embedding if query_embedding is not None else np.array([])
                )
                
                # Add to cache
                self.cached_searches[query_hash] = cached_search
                
                # Add to FAISS index
                if query_embedding is not None and self.faiss_index is not None:
                    try:
                        self.faiss_index.add(query_embedding.reshape(1, -1))
                    except Exception as e:
                        logger.error(f"Error adding to FAISS index: {e}")
                
                # Save cache (limit frequency to avoid performance issues)
                if len(self.cached_searches) % 10 == 0:  # Save every 10 searches
                    self._save_cache()
                    
            logger.info(f"Completed web search for: '{query}'")
            return summary
            
        except Exception as e:
            logger.error(f"Error in web search: {e}")
            return f"I encountered an error while searching for '{query}'. Please try again."
    
    def get_cache_stats(self) -> dict:
        """Get statistics about the search cache"""
        return {
            'total_cached_searches': len(self.cached_searches),
            'faiss_index_entries': self.faiss_index.ntotal if self.faiss_index else 0,
            'cache_size_mb': os.path.getsize(self.cache_file) / (1024*1024) if os.path.exists(self.cache_file) else 0,
            'oldest_entry': min([s.timestamp for s in self.cached_searches.values()]).isoformat() if self.cached_searches else None,
            'newest_entry': max([s.timestamp for s in self.cached_searches.values()]).isoformat() if self.cached_searches else None
        }

# Global instance
_web_search_rag = None

def get_web_search_context(query: str) -> str:
    """Get web search context for brain processing"""
    global _web_search_rag
    if _web_search_rag is None:
        _web_search_rag = WebSearchRAG()
    
    # This is called synchronously from brain, but we need async for web search
    # We'll return a placeholder and handle async in the brain action execution
    logger.info(f"Web search context requested for: '{query}'")
    return f"Web search requested: {query}"

async def perform_web_search(query: str) -> Optional[str]:
    """Perform async web search (called from brain action execution)"""
    global _web_search_rag
    if _web_search_rag is None:
        _web_search_rag = WebSearchRAG()
    
    return await _web_search_rag.search_web(query)

def get_search_cache_stats() -> dict:
    """Get web search cache statistics"""
    global _web_search_rag
    if _web_search_rag is None:
        _web_search_rag = WebSearchRAG()
    
    return _web_search_rag.get_cache_stats()
