#!/usr/bin/env python3
"""
Test Enhanced Memory Context
============================
Tests the exact same function call that appears in the logs
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def test_enhanced_memory_context():
    print("🔍 Testing enhanced_get_memory_context (exact function from logs)")
    print("=" * 70)
    
    # Initialize memory system
    await initialize_memory_system()
    
    # Exact parameters from the logs
    query_text = "Luna, what's your favorite vegetable?"
    current_user_id = 921637353364287489
    current_channel_id = 12345
    is_dm_channel = False
    
    print(f"🔍 Query: '{query_text}'")
    print(f"👤 User ID: {current_user_id}")
    print(f"📺 Channel ID: {current_channel_id}")
    print(f"📱 DM Channel: {is_dm_channel}")
    
    # Call the exact same function
    result = await enhanced_get_memory_context(
        query_text=query_text,
        current_user_id=current_user_id,
        current_channel_id=current_channel_id,
        is_dm_channel=is_dm_channel
    )
    
    print(f"\n📊 Result length: {len(result) if result else 0} chars")
    
    if result:
        print(f"\n📝 Memory facts returned:")
        lines = [line.strip() for line in result.split('\n') if line.strip()]
        for i, line in enumerate(lines, 1):
            print(f"  {i}. {line}")
        
        # Analyze what types of facts were returned
        if 'potatoes' in result.lower() or 'food' in result.lower():
            print(f"\n✅ CORRECT: Contains food/potatoes facts")
        elif 'purple' in result.lower() or 'color' in result.lower():
            print(f"\n❌ WRONG: Contains color facts instead")
        elif 'minecraft' in result.lower() or 'block' in result.lower():
            print(f"\n❌ WRONG: Contains Minecraft facts instead")
        else:
            print(f"\n❓ UNCLEAR: Contains other facts")
    else:
        print(f"\n❌ No result returned")
    
    # Let's also trace what happens inside enhanced_get_memory_context
    print(f"\n🔍 Tracing enhanced_get_memory_context logic:")
    
    # Replicate the key term extraction
    import re
    key_terms = []
    words = re.findall(r'\b\w+\b', query_text.lower())
    
    # Check the stop words logic - this might be the issue!
    important_indicators = {'my', 'your', 'luna', 'i', 'you'}
    stop_words = {'what', 'is', 'are', 'do', 'know', 'about', 'the', 'a', 'an', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
    
    print(f"  Original words: {words}")
    
    for word in words:
        if word in important_indicators or (word not in stop_words and len(word) > 2):
            key_terms.append(word)
    
    search_query = ' '.join(key_terms) if key_terms else query_text
    
    print(f"  Key terms: {key_terms}")
    print(f"  Search query: '{search_query}'")
    
    # Test this search query directly with memory system with smart user filtering
    ms = get_memory_system()
    
    # Replicate the smart user filtering logic
    query_lower = query_text.lower()
    
    # Check for user-specific indicators (asking about the user, not Luna)
    user_indicators = ['my ', 'mine', 'i like', 'i love', 'i prefer', 'i said', 'i told', 'what did i']
    is_user_query = any(indicator in query_lower for indicator in user_indicators)
    
    # Check for Luna-specific indicators (asking about Luna's preferences)
    luna_indicators = ['luna', 'your ', 'you like', 'you love', 'you prefer', 'favorite', 'you enjoy', 'do you like', 'what do you']
    is_luna_query = any(indicator in query_lower for indicator in luna_indicators)
    
    # Decision logic for user filtering
    if is_user_query:
        test_user_id = current_user_id  # Filter by user for user-specific queries
        classification = "USER query (filter by user)"
    elif is_luna_query and not is_user_query:
        test_user_id = None  # Don't filter by user for Luna personality queries
        classification = "LUNA query (no user filter)"
    else:
        test_user_id = current_user_id  # Default to user filtering
        classification = "DEFAULT (filter by user)"
    
    print(f"  User filtering classification: {classification}")
    print(f"  test_user_id: {test_user_id}")
    
    direct_context = await ms.retrieve_relevant_context(
        query=search_query,
        user_id=test_user_id,  # Use smart filtering
        channel_id=None,
        max_facts=5
    )
    
    print(f"\n🔄 Direct memory system call with search query '{search_query}':")
    if direct_context:
        direct_lines = [line.strip() for line in direct_context.split('\n') if line.strip()]
        for i, line in enumerate(direct_lines[:3], 1):
            print(f"    {i}. {line}")
    else:
        print(f"    No results")
    
    # Compare results
    if result and direct_context:
        if result.strip() == direct_context.strip():
            print(f"\n✅ CONSISTENT: Both methods return the same result")
        else:
            print(f"\n❌ INCONSISTENT: Methods return different results!")
            print(f"    enhanced_get_memory_context: {len(result)} chars")
            print(f"    direct memory system: {len(direct_context)} chars")

if __name__ == "__main__":
    asyncio.run(test_enhanced_memory_context())