#!/usr/bin/env python3
"""
Debug Person-Specific Queries
==============================
Analyzes why queries about specific people aren't finding the right facts
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_person_queries():
    print("👥 Debugging Person-Specific Queries")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Test queries from the logs
    queries = [
        "<PERSON> what do you know about <PERSON>?",
        "<PERSON>, what do you know about <PERSON>?"
    ]
    
    for query in queries:
        print(f"\n🔍 Query: '{query}'")
        
        # Extract search terms like the system does
        import re
        key_terms = []
        words = re.findall(r'\b\w+\b', query.lower())
        
        important_indicators = {'my', 'your', 'luna', 'i', 'you'}
        stop_words = {'what', 'is', 'are', 'do', 'know', 'about', 'the', 'a', 'an', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
        
        for word in words:
            if word in important_indicators or (word not in stop_words and len(word) > 2):
                key_terms.append(word)
        
        search_query = ' '.join(key_terms)
        print(f"   Search terms: {key_terms}")
        print(f"   Search query: '{search_query}'")
        
        # Check what facts exist about these people
        person_name = None
        if 'mari' in query.lower():
            person_name = 'mari'
        elif 'ethan' in query.lower():
            person_name = 'ethan'
        
        if person_name:
            conn = sqlite3.connect(ms.warm_storage.db_path)
            try:
                print(f"\n📊 Facts containing '{person_name}':")
                cursor = conn.execute("""
                    SELECT id, content, user_id, confidence
                    FROM memory_facts 
                    WHERE LOWER(content) LIKE ?
                    ORDER BY confidence DESC
                """, (f'%{person_name}%',))
                
                person_facts = cursor.fetchall()
                if person_facts:
                    for i, fact in enumerate(person_facts, 1):
                        print(f"     {i}. '{fact[1]}' (User: {fact[2]}, Conf: {fact[3]})")
                else:
                    print(f"     ❌ No facts found about {person_name}")
                
                # Test FTS search for the person's name
                print(f"\n🔍 FTS search for '{person_name}':")
                cursor = conn.execute("""
                    SELECT mf.id, mf.content, mf.confidence 
                    FROM memory_facts mf
                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                    LIMIT 5
                """, (f'"{person_name}"',))
                
                fts_results = cursor.fetchall()
                if fts_results:
                    for result in fts_results:
                        print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
                else:
                    print(f"     ❌ No FTS results for '{person_name}'")
                
                # Test the current search query
                print(f"\n🔍 FTS search for current query '{search_query}':")
                
                # Test AND logic first
                query_terms = search_query.split()
                escaped_terms = [f'"{term.replace('"', '""')}"' for term in query_terms if len(term) > 2]
                and_query = ' AND '.join(escaped_terms)
                
                print(f"     AND query: {and_query}")
                try:
                    cursor = conn.execute("""
                        SELECT mf.id, mf.content, mf.confidence 
                        FROM memory_facts mf
                        WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                        ORDER BY mf.confidence DESC
                        LIMIT 5
                    """, (and_query,))
                    
                    and_results = cursor.fetchall()
                    print(f"     AND results: {len(and_results)}")
                    for result in and_results[:3]:
                        print(f"       {result[0]}: {result[1]} (conf: {result[2]})")
                    
                    if len(and_results) == 0:
                        print(f"     ❌ AND query failed, testing priority terms")
                        
                        # Test priority term selection
                        term_priority = {
                            # Colors and preferences
                            'color': 10, 'purple': 10,
                            'food': 10, 'potatoes': 10, 'vegetable': 10, 'vegetables': 10, 'loves': 9, 'likes': 9,
                            'block': 10, 'minecraft': 10, 'cobblestone': 10, 'obsidian': 10,
                            # People names (high priority - these are often the main subject)
                            'mari': 15, 'ethan': 15, 'gavin': 15, 'alex': 15, 'sarah': 15, 'mike': 15, 'anna': 15,
                            'john': 15, 'emma': 15, 'david': 15, 'lisa': 15, 'tom': 15, 'kate': 15,
                            # Medium priority
                            'favorite': 7,
                            # Low priority (general terms)
                            'luna': 3, 'you': 3, 'user': 3
                        }
                        
                        prioritized_terms = []
                        for term in query_terms:
                            if len(term) > 2 and term in term_priority:
                                priority = term_priority[term]
                                prioritized_terms.append((priority, term))
                                print(f"       '{term}' → priority {priority}")
                        
                        prioritized_terms.sort(reverse=True)
                        
                        # Test each priority term
                        for priority, term in prioritized_terms:
                            cursor = conn.execute("""
                                SELECT COUNT(*) FROM memory_facts_fts WHERE memory_facts_fts MATCH ?
                            """, (f'"{term}"',))
                            
                            count = cursor.fetchone()[0]
                            print(f"       '{term}' (priority {priority}) → {count} results")
                            
                            if count > 0:
                                cursor = conn.execute("""
                                    SELECT mf.id, mf.content, mf.confidence 
                                    FROM memory_facts mf
                                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                                    ORDER BY mf.confidence DESC
                                    LIMIT 3
                                """, (f'"{term}"',))
                                
                                results = cursor.fetchall()
                                print(f"         First result: {results[0][1] if results else 'None'}")
                                break
                
                except Exception as e:
                    print(f"     ❌ FTS error: {e}")
            
            finally:
                conn.close()
        
        # Test the actual memory system call
        print(f"\n🧠 Testing actual memory system call:")
        try:
            context = await ms.retrieve_relevant_context(
                query=search_query,
                user_id=None,  # No user filter for Luna queries
                channel_id=None,
                max_facts=5
            )
            
            if context:
                print(f"     ✅ Found {len(context)} chars")
                lines = [line.strip() for line in context.split('\n') if line.strip()]
                for i, line in enumerate(lines[:3], 1):
                    print(f"       {i}. {line}")
                    
                # Check if the results are relevant
                if person_name and person_name in context.lower():
                    print(f"     ✅ Results contain {person_name} (relevant)")
                elif person_name:
                    print(f"     ❌ Results don't contain {person_name} (not relevant)")
            else:
                print(f"     ❌ No context found")
        except Exception as e:
            print(f"     ❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_person_queries())