#!/usr/bin/env python3
import re

def extract_key_terms(query_text):
    # Remove common question words and extract key nouns/concepts
    key_terms = []
    words = re.findall(r'\b\w+\b', query_text.lower())
    stop_words = {'what', 'is', 'are', 'do', 'you', 'know', 'about', 'the', 'a', 'an', 'your', 'my', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
    
    for word in words:
        if word not in stop_words and len(word) > 2:
            key_terms.append(word)
    
    # Use the most relevant terms or fall back to the full query
    search_query = ' '.join(key_terms[:3]) if key_terms else query_text
    
    return words, key_terms, search_query

# Test cases
test_queries = [
    "Tell me about potatoes",
    "What's your favorite color?", 
    "Do you remember what you like to eat?",
    "What do you know about potatoes?",
    "<PERSON> likes what?",
    "Hi <PERSON>"
]

print("🔍 Testing key term extraction:")
for query in test_queries:
    words, key_terms, search_query = extract_key_terms(query)
    print(f"  '{query}'")
    print(f"    words: {words}")
    print(f"    key_terms: {key_terms}")
    print(f"    search_query: '{search_query}'")
    print()