py-cord[voice]>=2.6.1
# google-generativeai>=0.4.0 # Replaced by Vertex AI
# groq>=0.9.0 # Removed Groq
google-cloud-aiplatform>=1.40.0 # Added Vertex AI SDK
pillow>=9.0.0
pydub>=0.25.1
faster-whisper>=0.7.0
numpy>=1.25.2,<2.0.0
python-dotenv>=0.19.0
# openai>=1.0.0 # Removed - replacing Ollama API calls with direct llama.cpp
./llama_cpp_python-0.3.9+cu124-cp312-cp312-win_amd64.whl
colorlog>=6.7.0
nltk>=3.7.0
scikit-learn>=1.0.0
torch>=1.10.0
transformers>=4.16.0
tqdm>=4.62.0
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
pytz>=2023.3 # For timezone handling in utils.format_datetime
requests>=2.20.0 # For making HTTP requests (e.g., to LM Studio)
websockets>=11.0.0 # For VTube Studio WebSocket API communication
pyttsx3>=2.90 # For local TTS to VTube Studio audio device (fallback)
pygame>=2.0.0 # For local audio playback synchronization with VTube Studio
pytesseract==0.3.8
aiohttp>=3.9.0  # For high-performance async HTTP requests in latency testing
psutil>=5.9.0   # For system performance monitoring in latency tests
# ImageGrab from PIL is used for screenshot capture