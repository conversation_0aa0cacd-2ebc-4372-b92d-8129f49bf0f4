#!/usr/bin/env python3
"""
Test script to verify GPU allocation for Luna's LLM models.
This script will load each model and show which GPU it's using.
"""

import os
import sys
import logging
from load_environment import load_env_vars

# Load environment variables first
load_env_vars()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_main_response_model():
    """Test the main response model (luna-tune) on RTX 4070"""
    logger.info("=" * 60)
    logger.info("TESTING MAIN RESPONSE MODEL (luna-tune on RTX 4070)")
    logger.info("=" * 60)
    
    try:
        from llm_response.initialization import initialize_llama_cpp
        client = initialize_llama_cpp()
        if client:
            logger.info("✅ Main response model loaded successfully")
            # Test a simple generation to verify GPU usage
            response = client("Hello", max_tokens=5, temperature=0.1)
            logger.info(f"Test response: {response['choices'][0]['text']}")
        else:
            logger.error("❌ Failed to load main response model")
    except Exception as e:
        logger.error(f"❌ Error loading main response model: {e}")

def test_decision_model():
    """Test the decision model (gemma3) on RX 6650XT"""
    logger.info("=" * 60)
    logger.info("TESTING DECISION MODEL (gemma3 on RX 6650XT)")
    logger.info("=" * 60)
    
    try:
        from llm_response.decision import get_decision_model
        client = get_decision_model()
        if client:
            logger.info("✅ Decision model loaded successfully")
            # Test a simple generation to verify GPU usage
            response = client("Yes or no?", max_tokens=3, temperature=0.1)
            logger.info(f"Test response: {response['choices'][0]['text']}")
        else:
            logger.error("❌ Failed to load decision model")
    except Exception as e:
        logger.error(f"❌ Error loading decision model: {e}")

def test_brain_model():
    """Test the brain model (gemma3) on RX 6650XT"""
    logger.info("=" * 60)
    logger.info("TESTING BRAIN MODEL (gemma3 on RX 6650XT)")
    logger.info("=" * 60)
    
    try:
        from llm_brain import get_gemma_cpp_client
        client = get_gemma_cpp_client()
        if client:
            logger.info("✅ Brain model loaded successfully")
            # Test a simple generation to verify GPU usage
            response = client("Think", max_tokens=5, temperature=0.1)
            logger.info(f"Test response: {response['choices'][0]['text']}")
        else:
            logger.error("❌ Failed to load brain model")
    except Exception as e:
        logger.error(f"❌ Error loading brain model: {e}")

def check_environment():
    """Check environment variables"""
    logger.info("=" * 60)
    logger.info("ENVIRONMENT VARIABLES")
    logger.info("=" * 60)
    
    env_vars = [
        "GGML_BACKEND",
        "GGML_VK_VISIBLE_DEVICES", 
        "GGML_VK_FORCE_MAX_ALLOCATION_SIZE",
        "GGML_VK_SUBALLOCATION_BLOCK_SIZE",
        "GGML_VK_DEBUG"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "NOT SET")
        logger.info(f"{var}: {value}")

def main():
    """Main test function"""
    logger.info("Starting GPU allocation test for Luna Discord Bot")
    
    # Check environment first
    check_environment()
    
    # Test each model
    test_main_response_model()
    test_decision_model() 
    test_brain_model()
    
    logger.info("=" * 60)
    logger.info("GPU ALLOCATION TEST COMPLETE")
    logger.info("=" * 60)
    logger.info("Check the logs above to verify:")
    logger.info("1. Main response model (luna-tune) uses RTX 4070 (Device 0)")
    logger.info("2. Decision model (gemma3) uses RX 6650XT (Device 1)")
    logger.info("3. Brain model (gemma3) uses RX 6650XT (Device 1)")
    logger.info("If you see verbose Vulkan output, check which GPU each model loads on.")

if __name__ == "__main__":
    main()
