#!/usr/bin/env python3
"""
Test Fixed Memory System
=========================
Direct test of the fixed memory system for person queries
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def test_fixed_memory_system():
    print("🧪 Testing Fixed Memory System")
    print("=" * 40)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Test queries that should return no results
    test_queries = [
        ("luna you mari", "Should return no results (Mari unknown)"),
        ("luna you ethan", "Should return no results (Ethan unknown)"),
        ("luna you gavin", "Should return Gavin facts (<PERSON> known)"),
        ("luna favorite color", "Should return <PERSON>'s color facts"),
    ]
    
    for query, expected in test_queries:
        print(f"\n🔍 Query: '{query}'")
        print(f"   Expected: {expected}")
        
        try:
            context = await ms.retrieve_relevant_context(
                query=query,
                user_id=None,  # No user filter
                channel_id=None,
                max_facts=5
            )
            
            if context:
                lines = [line.strip() for line in context.split('\n') if line.strip()]
                print(f"   Result: ✅ Found {len(lines)} facts")
                for i, line in enumerate(lines[:2], 1):
                    print(f"     {i}. {line}")
                
                # Check relevance
                query_lower = query.lower()
                context_lower = context.lower()
                
                if 'mari' in query_lower and 'mari' not in context_lower:
                    print(f"   ❌ Query about Mari but results don't mention Mari")
                elif 'ethan' in query_lower and 'ethan' not in context_lower:
                    print(f"   ❌ Query about Ethan but results don't mention Ethan")
                elif 'gavin' in query_lower and 'gavin' in context_lower:
                    print(f"   ✅ Query about Gavin and results mention Gavin")
                elif 'color' in query_lower and 'purple' in context_lower:
                    print(f"   ✅ Query about color and results mention color")
                else:
                    print(f"   ❓ Unclear relevance")
                    
            else:
                print(f"   Result: ❌ No context found")
                if 'mari' in query.lower() or 'ethan' in query.lower():
                    print(f"   ✅ Correct - should have no results for unknown people")
                else:
                    print(f"   ❌ Unexpected - should have found results")
                    
        except Exception as e:
            print(f"   Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_fixed_memory_system())