# How to Extract GGUF Models from Ollama

This guide explains how to locate and extract GGUF model files from Ollama for use with llama.cpp.

## 📍 **Step 1: List Your Models**

First, see what models you have:
```bash
ollama list
```

This shows model names, IDs, and sizes. Note the **model name** you want to extract.

## 🔍 **Step 2: Get Model Details**

Get detailed information about your model:
```bash
ollama show YOUR_MODEL_NAME:latest --verbose
```

This will show the model's blob digest (the actual file identifier).

## 📂 **Step 3: Find Ollama Storage Location**

### **Windows:**
```powershell
# Default location
$env:USERPROFILE\.ollama\models\

# Check if custom location is set
echo $env:OLLAMA_MODELS
```

### **Linux/macOS:**
```bash
# Default locations
~/.ollama/models/           # Linux
/usr/share/ollama/.ollama/  # System-wide Linux
~/Library/Application Support/ollama/models/  # macOS

# Check if custom location is set
echo $OLLAMA_MODELS
```

## 🗂️ **Step 4: Locate the Model Manifest**

Find your model's manifest file:
```bash
# Windows PowerShell
ls "$env:USERPROFILE\.ollama\models\manifests\registry.ollama.ai\library\YOUR_MODEL_NAME\"

# Linux/macOS
ls ~/.ollama/models/manifests/registry.ollama.ai/library/YOUR_MODEL_NAME/
```

## 📄 **Step 5: Read the Manifest**

Get the blob reference from the manifest:
```bash
# Windows PowerShell
Get-Content "$env:USERPROFILE\.ollama\models\manifests\registry.ollama.ai\library\YOUR_MODEL_NAME\latest"

# Linux/macOS
cat ~/.ollama/models/manifests/registry.ollama.ai/library/YOUR_MODEL_NAME/latest
```

Look for the **largest blob** (usually the model weights). The digest will look like:
```json
"digest":"sha256:507743594807df86aa8ad408b7b0921ff9b31181508839f93012dc21843e962c"
```

## 📦 **Step 6: Extract the GGUF File**

Copy the blob to your desired location:

### **Windows PowerShell:**
```powershell
# Set the blob hash (without "sha256:" prefix)
$blobHash = "sha256-507743594807df86aa8ad408b7b0921ff9b31181508839f93012dc21843e962c"

# Copy to your models directory
Copy-Item "$env:USERPROFILE\.ollama\models\blobs\$blobHash" ".\models\your-model.gguf"
```

### **Linux/macOS:**
```bash
# Set the blob hash (replace sha256: with sha256-)
BLOB_HASH="sha256-507743594807df86aa8ad408b7b0921ff9b31181508839f93012dc21843e962c"

# Copy to your models directory
cp ~/.ollama/models/blobs/$BLOB_HASH ./models/your-model.gguf
```

## 🛠️ **Step 7: Verify the GGUF File**

Check that the file is valid:
```bash
# Check file size (should match the size from ollama list)
ls -lh ./models/your-model.gguf

# Test with llama.cpp (if you have it)
llama-cpp-python -m llama_cpp.server --model ./models/your-model.gguf --n_ctx 2048 --help
```

## 🔄 **Quick Script for Future Use**

Here's a PowerShell script to automate this process:

```powershell
# extract_ollama_model.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$ModelName,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputName = "extracted-model.gguf"
)

# Get model manifest
$manifestPath = "$env:USERPROFILE\.ollama\models\manifests\registry.ollama.ai\library\$ModelName\latest"

if (-not (Test-Path $manifestPath)) {
    Write-Error "Model '$ModelName' not found. Check 'ollama list' for available models."
    exit 1
}

# Read manifest and extract blob hash
$manifest = Get-Content $manifestPath | ConvertFrom-Json
$modelLayer = $manifest.layers | Where-Object { $_.mediaType -eq "application/vnd.ollama.image.model" }

if (-not $modelLayer) {
    Write-Error "Could not find model layer in manifest"
    exit 1
}

# Extract blob hash
$blobHash = $modelLayer.digest -replace "sha256:", "sha256-"
$blobPath = "$env:USERPROFILE\.ollama\models\blobs\$blobHash"

if (-not (Test-Path $blobPath)) {
    Write-Error "Blob file not found: $blobPath"
    exit 1
}

# Create models directory if it doesn't exist
if (-not (Test-Path "models")) {
    New-Item -ItemType Directory -Name "models"
}

# Copy the model
Copy-Item $blobPath "models\$OutputName"
Write-Host "✅ Successfully extracted '$ModelName' to 'models\$OutputName'"

# Show file info
$fileInfo = Get-Item "models\$OutputName"
Write-Host "📁 File size: $([math]::Round($fileInfo.Length / 1GB, 2)) GB"
```

**Usage:**
```powershell
.\extract_ollama_model.ps1 -ModelName "luna-tune" -OutputName "luna-model.gguf"
```

## 📋 **Summary**

1. **List models**: `ollama list`
2. **Get details**: `ollama show MODEL_NAME --verbose`
3. **Find manifest**: `~/.ollama/models/manifests/registry.ollama.ai/library/MODEL_NAME/latest`
4. **Read manifest**: Look for the largest blob digest
5. **Copy blob**: From `blobs/` directory to your destination
6. **Rename**: Give it a `.gguf` extension

The extracted GGUF file can then be used directly with llama.cpp, llama-cpp-python, or any other GGUF-compatible inference engine! 