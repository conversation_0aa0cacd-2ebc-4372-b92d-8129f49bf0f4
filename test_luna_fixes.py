#!/usr/bin/env python3
"""
Test script to verify <PERSON>'s response formatting fixes.
This script tests:
1. Updated system prompt
2. Removal of name prefixes from user messages
3. Response filtering to remove <PERSON>: prefixes
"""

import asyncio
import logging
import sys
import os
import time
from typing import Dict, List, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_system_prompt_update():
    """Test that the system prompt was updated correctly"""
    print("\n=== SYSTEM PROMPT UPDATE TEST ===")
    
    try:
        with open('luna_prompt.txt', 'r', encoding='utf-8') as f:
            system_prompt = f.read().strip()
        
        print("Updated system prompt:")
        print(f"'{system_prompt}'")
        print(f"Length: {len(system_prompt)} chars")
        
        # Check for key improvements
        improvements = [
            "casual",
            "lowercase", 
            "emoticons",
            "don't add speaker names",
            "respond directly"
        ]
        
        found_improvements = []
        for improvement in improvements:
            if improvement.lower() in system_prompt.lower():
                found_improvements.append(improvement)
        
        print(f"\nFound improvements: {found_improvements}")
        print(f"Score: {len(found_improvements)}/{len(improvements)}")
        
        if len(found_improvements) >= 4:
            print("✅ System prompt successfully updated!")
        else:
            print("❌ System prompt may need more improvements")
            
    except Exception as e:
        print(f"❌ Error reading system prompt: {e}")

def test_response_filtering():
    """Test the Luna prefix removal function"""
    print("\n=== RESPONSE FILTERING TEST ===")
    
    try:
        from utils import remove_luna_prefix
        
        test_cases = [
            ("Luna: Hi Gavin! How are you today?", "Hi Gavin! How are you today?"),
            ("Luna - hey what's up?", "hey what's up?"),
            ("**Luna**: doing pretty good :)", "doing pretty good :)"),
            ("Luna says: that's awesome!", "that's awesome!"),
            ("hey! doing great, how about you?", "hey! doing great, how about you?"),  # No prefix
            ("Luna responds: lol nice", "lol nice"),
            ("", ""),  # Empty string
        ]
        
        print("Testing Luna prefix removal:")
        all_passed = True
        
        for input_text, expected in test_cases:
            result = remove_luna_prefix(input_text)
            passed = result == expected
            status = "✅" if passed else "❌"
            
            print(f"  {status} Input: '{input_text}'")
            print(f"     Expected: '{expected}'")
            print(f"     Got: '{result}'")
            print()
            
            if not passed:
                all_passed = False
        
        if all_passed:
            print("✅ All response filtering tests passed!")
        else:
            print("❌ Some response filtering tests failed")
            
    except Exception as e:
        print(f"❌ Error testing response filtering: {e}")

def test_message_formatting():
    """Test the message formatting changes"""
    print("\n=== MESSAGE FORMATTING TEST ===")
    
    print("Testing training-compatible message format:")
    
    # Simulate old vs new formatting
    user_message = "Hi, Luna."
    user_display_name = "Gavin"
    
    # Old format (problematic)
    old_format = f"{user_display_name}: {user_message}"
    old_gemma3 = f"<start_of_turn>user\n{old_format}<end_of_turn>"
    
    # New format (training-compatible)
    new_format = user_message  # No name prefix
    new_gemma3 = f"<start_of_turn>user\n{new_format}<end_of_turn>"
    
    print("Old format (problematic):")
    print(f"  User input: '{user_message}'")
    print(f"  Formatted: '{old_format}'")
    print(f"  Gemma3: '{old_gemma3}'")
    print("  Problem: Adds name prefix that wasn't in training data")
    
    print("\nNew format (training-compatible):")
    print(f"  User input: '{user_message}'")
    print(f"  Formatted: '{new_format}'")
    print(f"  Gemma3: '{new_gemma3}'")
    print("  ✅ Matches training data format (no name prefixes)")

def test_expected_behavior():
    """Test expected Luna behavior after fixes"""
    print("\n=== EXPECTED BEHAVIOR TEST ===")
    
    print("Expected Luna responses after fixes:")
    
    test_scenarios = [
        {
            "user_input": "Hi, Luna.",
            "old_response": "Luna: Hi Gavin! How are you today?",
            "expected_new": "hey! how's it going? :)",
            "improvements": ["lowercase", "casual", "no Luna prefix", "emoticon"]
        },
        {
            "user_input": "I'm doing alright, what about you?",
            "old_response": "Luna: I'm doing pretty good, thanks for asking! How was your day so far?",
            "expected_new": "doing pretty good! what've you been up to today?",
            "improvements": ["lowercase", "casual", "no Luna prefix", "natural flow"]
        },
        {
            "user_input": "What's your favorite game?",
            "old_response": "Luna: Let's go to the game center!! I love going there and playing games.",
            "expected_new": "ooh i love going to game centers! so many fun games to try XD",
            "improvements": ["lowercase", "casual", "emoticons", "personality"]
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\nScenario {i}:")
        print(f"  User: '{scenario['user_input']}'")
        print(f"  Old response: '{scenario['old_response']}'")
        print(f"  Expected new: '{scenario['expected_new']}'")
        print(f"  Improvements: {', '.join(scenario['improvements'])}")

def test_integration_readiness():
    """Test if the fixes are ready for Discord integration"""
    print("\n=== INTEGRATION READINESS TEST ===")
    
    checks = []
    
    # Check 1: System prompt updated
    try:
        with open('luna_prompt.txt', 'r') as f:
            prompt = f.read()
        if "casual" in prompt.lower() and "lowercase" in prompt.lower():
            checks.append(("System prompt updated", True))
        else:
            checks.append(("System prompt updated", False))
    except:
        checks.append(("System prompt updated", False))
    
    # Check 2: Response filtering function exists
    try:
        from utils import remove_luna_prefix
        test_result = remove_luna_prefix("Luna: test")
        if test_result == "test":
            checks.append(("Response filtering works", True))
        else:
            checks.append(("Response filtering works", False))
    except:
        checks.append(("Response filtering works", False))
    
    # Check 3: Processing file has been modified
    try:
        with open('llm_response/processing.py', 'r') as f:
            content = f.read()
        if "remove_luna_prefix" in content and "training-compatible" in content.lower():
            checks.append(("Processing file updated", True))
        else:
            checks.append(("Processing file updated", False))
    except:
        checks.append(("Processing file updated", False))
    
    print("Integration readiness checklist:")
    all_passed = True
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"  {status} {check_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All fixes implemented! Ready for Discord testing.")
    else:
        print("\n⚠️ Some fixes may need attention before Discord testing.")

if __name__ == "__main__":
    print("Luna Response Fixes Verification")
    print("=" * 50)
    
    test_system_prompt_update()
    test_response_filtering()
    test_message_formatting()
    test_expected_behavior()
    test_integration_readiness()
    
    print("\n" + "=" * 50)
    print("FIXES SUMMARY:")
    print("1. ✅ Updated system prompt to match training personality")
    print("2. ✅ Added response filtering to remove Luna: prefixes")
    print("3. ✅ Modified message formatting to remove name prefixes")
    print("4. ✅ Made changes training-data compatible")
    
    print("\nNEXT STEPS:")
    print("1. Test with actual Discord bot")
    print("2. Monitor responses for improvements")
    print("3. Fine-tune if needed based on results")
