#!/usr/bin/env python3
"""
Test Real Luna Scenarios
========================
Tests the exact scenarios that would happen when <PERSON> is launched
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def test_real_scenarios():
    print("🎯 Testing Real Luna Launch Scenarios")
    print("=" * 60)
    
    # Initialize memory system
    await initialize_memory_system()
    
    # Real user ID from logs
    user_id = 921637353364287489
    
    # Test scenarios that would actually happen when <PERSON> is launched
    scenarios = [
        # Luna personality queries (should find Luna facts, no user filter)
        ("<PERSON>, what's your favorite color?", "Should find: <PERSON>'s favorite color is purple"),
        ("<PERSON>, what's your favorite food?", "Should find: <PERSON> loves potatoes facts"),
        ("<PERSON>, what's your favorite vegetable?", "Should find: Luna loves potatoes facts"),
        ("<PERSON>, what's your favorite block in Minecraft?", "Should find: <PERSON>'s favorite Minecraft block is cobblestone"),
        ("What do you like to eat?", "Should find: <PERSON> loves potatoes facts"),
        ("What's your favorite color?", "Should find: <PERSON>'s favorite color is purple"),
        
        # User-specific queries (should find user facts, filter by user)
        ("<PERSON>, what's my favorite color?", "Should find: User's color preference (if any)"),
        ("What did I say my favorite game was?", "Should find: User's game preference (if any)"),
        ("Luna, what's my favorite food?", "Should find: User's food preference (if any)"),
        
        # Edge cases
        ("Luna, do you prefer anything?", "Should find: Luna preference facts"),
        ("What do I like?", "Should find: User preference facts"),
    ]
    
    success_count = 0
    total_count = len(scenarios)
    
    for i, (query, expected) in enumerate(scenarios, 1):
        print(f"\n{i:2d}. 🔍 Query: '{query}'")
        print(f"    Expected: {expected}")
        
        try:
            # Test the exact function that Luna uses
            context = await enhanced_get_memory_context(
                query_text=query,
                current_user_id=user_id,
                current_channel_id=12345,
                is_dm_channel=False
            )
            
            if context:
                # Analyze the results
                context_lower = context.lower()
                
                # Check what type of facts were found
                has_luna_color = 'luna' in context_lower and 'purple' in context_lower
                has_luna_food = 'luna' in context_lower and ('potatoes' in context_lower or 'food' in context_lower)
                has_luna_minecraft = 'luna' in context_lower and ('minecraft' in context_lower or 'cobblestone' in context_lower)
                has_user_facts = 'gavin said' in context_lower or 'user said' in context_lower
                
                # Determine if the result is correct based on the query
                query_lower = query.lower()
                is_luna_query = any(indicator in query_lower for indicator in ['luna', 'your', 'you like', 'do you'])
                is_user_query = any(indicator in query_lower for indicator in ['my ', 'what did i', 'i said'])
                
                is_correct = False
                result_description = ""
                
                if 'color' in query_lower and is_luna_query and has_luna_color:
                    is_correct = True
                    result_description = "✅ Found Luna's color facts"
                elif ('food' in query_lower or 'vegetable' in query_lower or 'eat' in query_lower) and is_luna_query and has_luna_food:
                    is_correct = True
                    result_description = "✅ Found Luna's food facts"
                elif ('minecraft' in query_lower or 'block' in query_lower) and is_luna_query and has_luna_minecraft:
                    is_correct = True
                    result_description = "✅ Found Luna's Minecraft facts"
                elif is_user_query and has_user_facts:
                    is_correct = True
                    result_description = "✅ Found user facts (correctly filtered)"
                elif is_luna_query and (has_luna_color or has_luna_food or has_luna_minecraft):
                    is_correct = True
                    result_description = "✅ Found Luna facts (general)"
                elif 'prefer' in query_lower and is_luna_query and (has_luna_color or has_luna_food or has_luna_minecraft):
                    is_correct = True
                    result_description = "✅ Found Luna preference facts"
                else:
                    result_description = "❌ Found unexpected or wrong facts"
                
                if is_correct:
                    success_count += 1
                
                print(f"    Result: {result_description}")
                
                # Show first fact for context
                first_line = context.split('\n')[0].strip() if context else ''
                if first_line:
                    print(f"    First fact: {first_line}")
                    
            else:
                print(f"    Result: ❌ No memories found")
                if 'my ' in query.lower():
                    print(f"             (Note: User facts may not exist in test data)")
                
        except Exception as e:
            print(f"    Result: ❌ Error: {e}")
    
    print(f"\n📊 Test Results:")
    print(f"   ✅ Successful: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    print(f"   ❌ Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print(f"\n🎉 PERFECT! All scenarios work correctly for Luna launch!")
    elif success_count >= total_count * 0.9:
        print(f"\n✅ EXCELLENT! {success_count/total_count*100:.1f}% success rate - ready for Luna launch!")
    else:
        print(f"\n⚠️  Still needs improvement for reliable Luna launch")

if __name__ == "__main__":
    asyncio.run(test_real_scenarios())