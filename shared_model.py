import threading
import os
import logging
import time
from typing import Dict, Any
from collections import defaultdict

try:
    from llama_cpp import Llama
except ImportError:  # Fallback if llama_cpp is not yet installed when this file is imported
    Llama = Any  # type: ignore

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
#  Dual-GPU optimized model registry
# ---------------------------------------------------------------------------

# Map arbitrary keys (e.g. "gemma3_4070", "qwen3_amd") -> Llama instance
_models: Dict[str, Llama] = {}
_models_lock = threading.Lock()  # Guards model construction

# GPU-specific inference locks to prevent concurrent model calls on the same GPU
_nvidia_inference_lock = threading.Lock()
_amd_inference_lock = threading.Lock()

# Smart cross-backend coordination
_backend_state_lock = threading.RLock()
_active_backends = set()  # Track which backends are currently active
_backend_last_used = defaultdict(float)  # Track last usage time per backend

# Performance constants
BACKEND_ISOLATION_DELAY = 0.001  # Minimal delay between backend switches (1ms)
MAX_CONCURRENT_BACKENDS = 1  # Allow only 1 backend active at a time for stability

# Global GPU context lock to prevent CUDA/Vulkan memory conflicts
# This serializes ALL GPU access to prevent cross-backend memory corruption
_global_gpu_context_lock = threading.Lock()

# GPU device mapping for optimal performance
GPU_CONFIGS = {
    "nvidia_4070": {
        "device": 0,  # NVIDIA RTX 4070
        "backend": "cuda",
        "env_vars": {
            "CUDA_VISIBLE_DEVICES": "0",
            "GGML_BACKEND": "cuda"
        }
    },
    "amd_6650xt": {
        "device": 1,  # AMD RX 6650XT  
        "backend": "vulkan",
        "env_vars": {
            "GGML_BACKEND": "vulkan",
            "GGML_VK_DEVICE": "1",
            "GGML_VK_VISIBLE_DEVICES": "1",
            "CUDA_VISIBLE_DEVICES": ""
        }
    }
}

# ---------------------------------------------------------------------------
#  Public helpers
# ---------------------------------------------------------------------------

def get_or_create_model(key: str, *, model_path: str, gpu_target: str = "nvidia_4070", **llama_kwargs) -> Llama:
    """Return an existing Llama model or construct it once in a threadsafe way with GPU targeting.

    Parameters
    ----------
    key
        Arbitrary identifier used as the lookup key (e.g. "gemma3_4070", "qwen3_amd").
    model_path
        Path to the GGUF/GGML model file.
    gpu_target
        Target GPU configuration: "nvidia_4070" or "amd_6650xt"
    **llama_kwargs
        Additional keyword arguments forwarded to `llama_cpp.Llama`.
    """
    with _models_lock:
        model = _models.get(key)
        if model is None:
            # Configure environment for target GPU
            gpu_config = GPU_CONFIGS.get(gpu_target, GPU_CONFIGS["nvidia_4070"])
            
            # Set environment variables for proper GPU targeting
            old_env = {}
            for env_key, env_value in gpu_config["env_vars"].items():
                old_env[env_key] = os.environ.get(env_key)
                os.environ[env_key] = env_value
            
            try:
                # Configure GPU-specific parameters
                if gpu_target == "nvidia_4070":
                    # Optimized for NVIDIA RTX 4070 with CUDA Graphs
                    llama_kwargs.update({
                        "n_gpu_layers": -1,  # Full GPU offload
                        "main_gpu": 0,       # NVIDIA GPU
                        "use_mmap": True,
                        "use_mlock": True,
                        "verbose": False
                    })
                elif gpu_target == "amd_6650xt":
                    # Optimized for AMD RX 6650XT with Vulkan
                    llama_kwargs.update({
                        "n_gpu_layers": -1,  # Full GPU offload  
                        "main_gpu": 0,       # Will be device 1 but appears as 0 due to VISIBLE_DEVICES
                        "use_mmap": True,
                        "use_mlock": True,
                        "verbose": False
                    })
                
                logger.info(f"Creating model {key} on {gpu_target} with backend {gpu_config['backend']}")
                model = Llama(model_path=model_path, **llama_kwargs)
                _models[key] = model
                logger.info(f"✅ Model {key} loaded successfully on {gpu_target}")
            
            finally:
                # Restore original environment
                for env_key, old_value in old_env.items():
                    if old_value is None:
                        os.environ.pop(env_key, None)
                    else:
                        os.environ[env_key] = old_value
        
        return model


def call_model_safe(model: "Llama", prompt: str, gpu_target: str = "nvidia_4070", **kwargs):
    """Thread-safe wrapper around `model()` for *non-streaming* inference with full cross-backend isolation.
    
    Uses global GPU context lock to completely prevent CUDA/Vulkan memory conflicts.
    Production testing showed that optimized coordination still allows access violations.
    """
    # Use global context lock first to prevent CUDA/Vulkan memory conflicts
    with _global_gpu_context_lock:
        # Then use GPU-specific lock for per-device concurrency control
        inference_lock = _nvidia_inference_lock if gpu_target == "nvidia_4070" else _amd_inference_lock
        
        with inference_lock:
            try:
                kwargs.setdefault("stream", False)
                result = model(prompt, **kwargs)
                return result
                
            except Exception as e:
                logger.error(f"Inference error on {gpu_target}: {e}")
                # Clean up any partial state that might cause issues
                try:
                    import gc
                    gc.collect()
                except:
                    pass
                raise


def stream_model_safe(model: "Llama", prompt: str, gpu_target: str = "nvidia_4070", **kwargs):
    """Thread-safe wrapper for *streaming* inference with full cross-backend isolation.
    
    Uses global GPU context lock to completely prevent CUDA/Vulkan memory conflicts.
    Production testing showed that optimized coordination still allows access violations.
    """
    # Use global context lock first to prevent CUDA/Vulkan memory conflicts
    with _global_gpu_context_lock:
        # Then use GPU-specific lock for per-device concurrency control
        inference_lock = _nvidia_inference_lock if gpu_target == "nvidia_4070" else _amd_inference_lock
        
        with inference_lock:
            try:
                kwargs["stream"] = True  # Hard-enforce streaming mode
                for chunk in model(prompt, **kwargs):
                    yield chunk
                    
            except Exception as e:
                logger.error(f"Stream error on {gpu_target}: {e}")
                # Clean up any partial state that might cause issues
                try:
                    import gc
                    gc.collect()
                except:
                    pass
                raise


# Convenience functions for specific model types
def call_gemma3_safe(model: "Llama", prompt: str, **kwargs):
    """Convenience wrapper for Gemma3 model on NVIDIA RTX 4070."""
    return call_model_safe(model, prompt, gpu_target="nvidia_4070", **kwargs)


def call_qwen3_safe(model: "Llama", prompt: str, **kwargs):
    """Convenience wrapper for Qwen3 model on AMD RX 6650XT."""
    return call_model_safe(model, prompt, gpu_target="amd_6650xt", **kwargs)


def stream_gemma3_safe(model: "Llama", prompt: str, **kwargs):
    """Convenience wrapper for streaming Gemma3 model on NVIDIA RTX 4070."""
    return stream_model_safe(model, prompt, gpu_target="nvidia_4070", **kwargs)


def stream_qwen3_safe(model: "Llama", prompt: str, **kwargs):
    """Convenience wrapper for streaming Qwen3 model on AMD RX 6650XT."""
    return stream_model_safe(model, prompt, gpu_target="amd_6650xt", **kwargs) 