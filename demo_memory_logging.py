#!/usr/bin/env python3
"""
Demo Memory Logging
===================
Demonstrates the enhanced memory injection logging without running a full conversation
"""
import asyncio
import time
import copy
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType
from llm_response.processing import enhanced_get_memory_context

async def demo_memory_logging():
    print("🎬 Demonstrating Memory Injection Logging")
    print("=" * 60)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Store test facts
    facts = [
        MemoryFact(
            content="<PERSON>'s favorite color is purple",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="general",
            confidence=0.95,
            timestamp=time.time(),
            entities=["Luna", "purple", "color"],
            relationships=[("Luna", "prefers", "purple")]
        ),
        MemoryFact(
            content="<PERSON> loves potatoes and thinks they're the best food",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="general", 
            confidence=0.95,
            timestamp=time.time(),
            entities=["Luna", "potatoes", "food"],
            relationships=[("Luna", "loves", "potatoes")]
        )
    ]
    
    for fact in facts:
        ms.warm_storage.store_fact(fact)
    
    # Rebuild FTS
    import sqlite3
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
    finally:
        conn.close()
    
    print("✅ Test facts stored and indexed")
    
    # Demo the memory retrieval and application
    print("\n🧠 Memory Retrieval Demo:")
    query = "Luna, what's your favorite color?"
    
    # Step 1: Memory retrieval (this will show the injection logging)
    rag_context = await enhanced_get_memory_context(
        query_text=query,
        current_user_id=12345,
        current_channel_id=None,
        is_dm_channel=False
    )
    
    # Step 2: Simulate applying to API messages (this shows application logging)
    if rag_context:
        print("\n📋 Simulating API Message Preparation:")
        
        # Mock messages
        original_messages = [
            {"role": "system", "content": "You are Luna, a Discord chatbot. You have a sarcastic personality."},
            {"role": "user", "content": query}
        ]
        
        # Create API copy
        api_messages = copy.deepcopy(original_messages)
        
        # Apply memory context (this triggers the application logging)
        original_system_content = api_messages[0]["content"]
        enhanced_system_prompt = f"{original_system_content}\n\nRelevant conversation context:\n{rag_context}"
        api_messages[0]["content"] = enhanced_system_prompt
        
        # Manual logging to demonstrate what happens during real conversation
        print(f"\n🔄 APPLYING MEMORY TO LLM PROMPT:")
        print(f"   📋 Original system prompt: {len(original_system_content)} chars")
        print(f"   ➕ Adding memory context: {len(rag_context)} chars")
        print(f"   📝 Final enhanced prompt: {len(enhanced_system_prompt)} chars")
        print(f"   🔒 Applied to API copy only (original messages clean)")
        
        # Show a preview of what was added
        memory_lines = rag_context.split('\n')[:3]
        print(f"   👁️ Memory preview added to prompt:")
        for line in memory_lines:
            if line.strip():
                print(f"      • {line.strip()[:70]}{'...' if len(line.strip()) > 70 else ''}")
        print(f"   ===============================================")
        
        print(f"\n✨ During actual conversations, you'll see both:")
        print(f"   1. 🧠 MEMORY INJECTION logs (when memories are found)")
        print(f"   2. 🔄 APPLYING MEMORY logs (when they're added to prompts)")
        print(f"   3. No contamination of original conversation history!")
        
    else:
        print("❌ No memory context retrieved")

if __name__ == "__main__":
    asyncio.run(demo_memory_logging())