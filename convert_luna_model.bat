@echo off
REM Convert Luna-Tune Ollama model to GGUF format
REM This script converts luna-tune:latest to a GGUF file for llama.cpp

echo 🚀 Converting Luna-Tune model from Ollama to GGUF format...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python not found. Please install Python first.
    pause
    exit /b 1
)

REM Check if Ollama is available
ollama --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Ollama not found. Please install Ollama first.
    pause
    exit /b 1
)

REM Run the conversion
python convert_ollama_to_gguf.py luna-tune:latest --output luna-tune-latest.gguf

echo.
echo 🎉 Conversion completed! Check the models/ directory for luna-tune-latest.gguf
echo 💡 You can now use this model with llama.cpp or llama-cpp-python
echo.
pause 