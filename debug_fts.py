#!/usr/bin/env python3
import asyncio
import time
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType
from llm_response.processing import enhanced_get_memory_context

async def debug_fts_search():
    print("🔍 Debugging FTS search issues...")
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Store a simple test fact
    test_fact = MemoryFact(
        content="<PERSON> loves potatoes",
        memory_type=MemoryType.FACTUAL,
        user_id=12345,
        channel_id="test_channel",
        confidence=0.9,
        timestamp=time.time(),
        entities=["Luna", "potatoes"],
        relationships=[("Luna", "loves", "potatoes")]
    )
    
    fact_id = ms.warm_storage.store_fact(test_fact)
    print(f"✅ Stored test fact ID: {fact_id}")
    
    # Rebuild FTS
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
        print("🔧 Rebuilt FTS index")
        
        # Check what's in the database
        cursor = conn.execute("SELECT id, content, entities FROM memory_facts WHERE id = ?", (fact_id,))
        row = cursor.fetchone()
        if row:
            print(f"📊 Database entry: ID={row[0]}, content='{row[1]}', entities='{row[2]}'")
        
        # Test different FTS queries directly
        test_queries = [
            'potatoes',
            '"potatoes"',
            'Luna',
            '"Luna"',
            'Luna potatoes',
            '"Luna potatoes"'
        ]
        
        print("\n🧪 Testing FTS queries directly:")
        for query in test_queries:
            try:
                escaped_query = query.replace('"', '""')
                cursor = conn.execute("""
                    SELECT mf.id, mf.content FROM memory_facts mf
                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                """, (f'"{escaped_query}"',))
                results = cursor.fetchall()
                print(f"  Query '{query}' -> {len(results)} results")
                for result in results:
                    print(f"    {result[0]}: {result[1][:50]}...")
            except Exception as e:
                print(f"  Query '{query}' -> ERROR: {e}")
        
        # Test the warm storage search directly
        print(f"\n🔍 Testing warm storage search:")
        for query in ['potatoes', 'Luna', 'Luna potatoes']:
            facts = ms.warm_storage.search_facts(query=query, user_id=12345, limit=5)
            print(f"  Query '{query}' -> {len(facts)} facts")
            for fact in facts:
                print(f"    {fact.content[:50]}...")
    
    finally:
        conn.close()
    
    # Test the enhanced function
    print(f"\n🚀 Testing enhanced function:")
    test_cases = [
        "potatoes",
        "Tell me about potatoes", 
        "What's your favorite food?",
        "Luna likes what?"
    ]
    
    for query in test_cases:
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=12345
        )
        print(f"  '{query}' -> {len(context) if context else 0} chars: {context[:50] if context else 'None'}...")

if __name__ == "__main__":
    asyncio.run(debug_fts_search())