"""
Luna's Memory Type Classification System

This module implements a sophisticated memory categorization system that classifies
memories into five distinct types, each optimized for different retrieval patterns
and use cases in Discord conversations.

Memory Types:
- Working: Temporary conversation context and current session state
- Factual: Concrete facts, preferences, and verifiable information
- Episodic: Specific events, stories, and time-bound experiences
- Semantic: General knowledge, concepts, and abstract understanding
- Behavioral: Patterns, habits, and predictive user behavior
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class MemoryType(Enum):
    """Memory classification types for Luna's memory system."""
    WORKING = "working"        # Temporary conversation context
    FACTUAL = "factual"        # Concrete facts and preferences
    EPISODIC = "episodic"      # Specific events and stories
    SEMANTIC = "semantic"      # General knowledge and concepts
    BEHAVIORAL = "behavioral"  # Patterns and habits

@dataclass
class TypedMemoryFact:
    """A memory fact with type classification and metadata."""
    content: str
    memory_type: MemoryType
    confidence: float
    entities: List[str]
    relationships: List[Tuple[str, str, str]]  # (entity1, relation, entity2)
    timestamp: float
    user_id: Optional[int] = None
    channel_id: Optional[str] = None
    context_hash: Optional[str] = None
    
    # Type-specific metadata
    working_metadata: Optional[Dict[str, Any]] = None
    factual_metadata: Optional[Dict[str, Any]] = None
    episodic_metadata: Optional[Dict[str, Any]] = None
    semantic_metadata: Optional[Dict[str, Any]] = None
    behavioral_metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        data = asdict(self)
        data['memory_type'] = self.memory_type.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TypedMemoryFact':
        """Create from dictionary."""
        data['memory_type'] = MemoryType(data['memory_type'])
        return cls(**data)

class MemoryTypeClassifier:
    """Classifies memories into appropriate types using pattern matching and ML."""
    
    def __init__(self):
        self.factual_patterns = {
            'preferences': [
                r'\b(favorite|favourite|prefer|like|love|hate|dislike)\b',
                r'\bis\s+(my|their|his|her)\s+(favorite|favourite)',
                r'\b(don\'t|doesnt|do not)\s+(like|enjoy)',
            ],
            'facts': [
                r'\bis\s+(a|an|the)',
                r'\bhas\s+(a|an|the)',
                r'\bworks\s+(at|for|in)',
                r'\blives\s+(in|at|near)',
                r'\b(birthday|age|born)\b',
            ],
            'abilities': [
                r'\bcan\s+(do|make|play|sing|dance)',
                r'\bis\s+(good|bad)\s+at',
                r'\bknows\s+how\s+to',
            ]
        }
        
        self.episodic_patterns = [
            r'\b(yesterday|today|tomorrow|last\s+week|next\s+week)\b',
            r'\b(happened|occurred|went|came|saw|did)\b',
            r'\b(remember|recall)\s+when',
            r'\bonce\s+(i|we|they)',
            r'\bthat\s+time\s+when',
            r'\b(story|tale|experience)\b',
        ]
        
        self.behavioral_patterns = [
            r'\b(always|never|usually|often|sometimes|rarely)\b',
            r'\b(tends|habit|pattern|routine)\b',
            r'\bevery\s+(day|week|month|morning|evening)',
            r'\b(typical|normal|usual)ly\b',
        ]
        
        self.semantic_patterns = [
            r'\bin\s+general',
            r'\bmeaning\s+of',
            r'\bdefinition\s+of',
            r'\bis\s+defined\s+as',
            r'\btype\s+of',
            r'\bcategory\s+of',
            r'\bexample\s+of',
        ]
        
        self.working_indicators = [
            'current conversation',
            'just said',
            'talking about',
            'mentioned',
            'context'
        ]

    def classify_memory(self, content: str, entities: List[str], 
                       conversation_context: Optional[str] = None) -> Tuple[MemoryType, float, Dict[str, Any]]:
        """
        Classify a memory into its appropriate type with confidence score.
        
        Returns:
            Tuple of (MemoryType, confidence_score, type_specific_metadata)
        """
        content_lower = content.lower()
        scores = defaultdict(float)
        metadata = {}
        
        # Score each memory type
        scores[MemoryType.WORKING] = self._score_working(content_lower, conversation_context)
        scores[MemoryType.FACTUAL] = self._score_factual(content_lower, entities)
        scores[MemoryType.EPISODIC] = self._score_episodic(content_lower)
        scores[MemoryType.SEMANTIC] = self._score_semantic(content_lower)
        scores[MemoryType.BEHAVIORAL] = self._score_behavioral(content_lower)
        
        # Get the highest scoring type
        best_type = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[best_type]
        
        # Generate type-specific metadata
        if best_type == MemoryType.FACTUAL:
            metadata = self._extract_factual_metadata(content, entities)
        elif best_type == MemoryType.EPISODIC:
            metadata = self._extract_episodic_metadata(content)
        elif best_type == MemoryType.BEHAVIORAL:
            metadata = self._extract_behavioral_metadata(content)
        elif best_type == MemoryType.SEMANTIC:
            metadata = self._extract_semantic_metadata(content)
        elif best_type == MemoryType.WORKING:
            metadata = self._extract_working_metadata(content, conversation_context)
        
        return best_type, confidence, metadata

    def _score_working(self, content: str, context: Optional[str]) -> float:
        """Score for working memory (temporary conversation context)."""
        score = 0.0
        
        # Check for working memory indicators
        for indicator in self.working_indicators:
            if indicator in content:
                score += 0.3
        
        # Temporal indicators for current conversation
        if any(word in content for word in ['now', 'currently', 'right now', 'at the moment']):
            score += 0.4
        
        # Short content is more likely to be working memory
        if len(content) < 50:
            score += 0.2
        
        # Context dependency
        if context and len(context) > 0:
            score += 0.3
            
        return min(score, 1.0)

    def _score_factual(self, content: str, entities: List[str]) -> float:
        """Score for factual memory (concrete facts and preferences)."""
        import re
        score = 0.0
        
        # Check pattern matches
        for category, patterns in self.factual_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    if category == 'preferences':
                        score += 0.5
                    elif category == 'facts':
                        score += 0.4
                    elif category == 'abilities':
                        score += 0.3
        
        # Entity presence boosts factual score
        if entities:
            score += 0.2 * min(len(entities), 3)
        
        # Definitive statements
        if any(word in content for word in ['is', 'has', 'owns', 'lives', 'works']):
            score += 0.2
            
        return min(score, 1.0)

    def _score_episodic(self, content: str) -> float:
        """Score for episodic memory (specific events and stories)."""
        import re
        score = 0.0
        
        # Time-bound patterns
        for pattern in self.episodic_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.4
        
        # Narrative indicators
        if any(word in content for word in ['story', 'experience', 'event', 'happened']):
            score += 0.3
        
        # Past tense verbs
        past_verbs = ['went', 'came', 'saw', 'did', 'was', 'were', 'had']
        if any(f' {verb} ' in content for verb in past_verbs):
            score += 0.2
            
        return min(score, 1.0)

    def _score_semantic(self, content: str) -> float:
        """Score for semantic memory (general knowledge and concepts)."""
        import re
        score = 0.0
        
        # Semantic patterns
        for pattern in self.semantic_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.4
        
        # Abstract concepts
        if any(word in content for word in ['concept', 'idea', 'theory', 'principle']):
            score += 0.3
        
        # General statements
        if any(phrase in content for phrase in ['in general', 'usually', 'typically']):
            score += 0.2
            
        return min(score, 1.0)

    def _score_behavioral(self, content: str) -> float:
        """Score for behavioral memory (patterns and habits)."""
        import re
        score = 0.0
        
        # Behavioral patterns
        for pattern in self.behavioral_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                score += 0.5
        
        # Frequency indicators
        frequency_words = ['always', 'never', 'often', 'sometimes', 'rarely', 'usually']
        if any(word in content for word in frequency_words):
            score += 0.3
        
        # Habit indicators
        if any(word in content for word in ['habit', 'routine', 'pattern', 'tends']):
            score += 0.4
            
        return min(score, 1.0)

    def _extract_factual_metadata(self, content: str, entities: List[str]) -> Dict[str, Any]:
        """Extract metadata specific to factual memories."""
        metadata = {
            'fact_type': 'general',
            'entities_involved': entities,
            'verifiable': True,
            'preference_strength': None
        }
        
        # Classify fact type
        content_lower = content.lower()
        if any(word in content_lower for word in ['favorite', 'favourite', 'prefer', 'like', 'love']):
            metadata['fact_type'] = 'preference'
            # Estimate preference strength
            if any(word in content_lower for word in ['love', 'adore', 'obsessed']):
                metadata['preference_strength'] = 'high'
            elif any(word in content_lower for word in ['like', 'enjoy', 'prefer']):
                metadata['preference_strength'] = 'medium'
            else:
                metadata['preference_strength'] = 'low'
        elif any(word in content_lower for word in ['can', 'able', 'skill', 'talent']):
            metadata['fact_type'] = 'ability'
        elif any(word in content_lower for word in ['lives', 'works', 'studies', 'age', 'birthday']):
            metadata['fact_type'] = 'personal_info'
        
        return metadata

    def _extract_episodic_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata specific to episodic memories."""
        import re
        
        metadata = {
            'event_type': 'general',
            'time_indicators': [],
            'narrative_elements': [],
            'emotional_valence': 'neutral'
        }
        
        # Extract time indicators
        time_patterns = [
            r'\b(yesterday|today|tomorrow)\b',
            r'\b(last|next)\s+(week|month|year|time)\b',
            r'\b(morning|afternoon|evening|night)\b'
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            metadata['time_indicators'].extend(matches)
        
        # Detect emotional valence
        positive_words = ['happy', 'excited', 'great', 'amazing', 'fun', 'love']
        negative_words = ['sad', 'angry', 'frustrated', 'terrible', 'awful', 'hate']
        
        if any(word in content.lower() for word in positive_words):
            metadata['emotional_valence'] = 'positive'
        elif any(word in content.lower() for word in negative_words):
            metadata['emotional_valence'] = 'negative'
        
        return metadata

    def _extract_behavioral_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata specific to behavioral memories."""
        import re
        
        metadata = {
            'pattern_type': 'general',
            'frequency': None,
            'consistency': None,
            'triggers': []
        }
        
        # Extract frequency information
        frequency_map = {
            'always': 'very_high',
            'usually': 'high',
            'often': 'high',
            'sometimes': 'medium',
            'occasionally': 'low',
            'rarely': 'very_low',
            'never': 'none'
        }
        
        for word, freq in frequency_map.items():
            if word in content.lower():
                metadata['frequency'] = freq
                break
        
        # Detect pattern types
        if any(word in content.lower() for word in ['morning', 'evening', 'daily']):
            metadata['pattern_type'] = 'temporal'
        elif any(word in content.lower() for word in ['when', 'if', 'after', 'before']):
            metadata['pattern_type'] = 'conditional'
        
        return metadata

    def _extract_semantic_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata specific to semantic memories."""
        metadata = {
            'knowledge_type': 'general',
            'abstraction_level': 'medium',
            'domain': 'general'
        }
        
        # Classify knowledge type
        content_lower = content.lower()
        if any(word in content_lower for word in ['definition', 'meaning', 'is defined as']):
            metadata['knowledge_type'] = 'definition'
            metadata['abstraction_level'] = 'high'
        elif any(word in content_lower for word in ['example', 'instance', 'case']):
            metadata['knowledge_type'] = 'example'
            metadata['abstraction_level'] = 'low'
        elif any(word in content_lower for word in ['concept', 'theory', 'principle']):
            metadata['knowledge_type'] = 'concept'
            metadata['abstraction_level'] = 'high'
        
        return metadata

    def _extract_working_metadata(self, content: str, context: Optional[str]) -> Dict[str, Any]:
        """Extract metadata specific to working memories."""
        metadata = {
            'session_relevance': 'high',
            'context_dependency': 'medium',
            'temporal_scope': 'current',
            'decay_rate': 'fast'
        }
        
        # Determine context dependency
        if context and len(context) > 100:
            metadata['context_dependency'] = 'high'
        elif not context:
            metadata['context_dependency'] = 'low'
        
        # Determine temporal scope
        if any(word in content.lower() for word in ['now', 'currently', 'right now']):
            metadata['temporal_scope'] = 'immediate'
        elif any(word in content.lower() for word in ['today', 'this session']):
            metadata['temporal_scope'] = 'current'
        
        return metadata

class TypedMemoryStorage:
    """Storage system optimized for different memory types."""
    
    def __init__(self):
        self.type_stores: Dict[MemoryType, List[TypedMemoryFact]] = {
            memory_type: [] for memory_type in MemoryType
        }
        self.type_indices: Dict[MemoryType, Dict[str, Set[int]]] = {
            memory_type: defaultdict(set) for memory_type in MemoryType
        }
        
        # Working memory has limited capacity and fast decay
        self.working_memory_capacity = 50
        self.working_memory_ttl = 3600  # 1 hour
        
        # Different retention policies per type
        self.retention_policies = {
            MemoryType.WORKING: {'max_items': 50, 'ttl_hours': 1},
            MemoryType.FACTUAL: {'max_items': 5000, 'ttl_hours': None},  # No expiry
            MemoryType.EPISODIC: {'max_items': 2000, 'ttl_hours': 24 * 30},  # 30 days
            MemoryType.SEMANTIC: {'max_items': 3000, 'ttl_hours': None},  # No expiry
            MemoryType.BEHAVIORAL: {'max_items': 1000, 'ttl_hours': 24 * 7},  # 7 days
        }

    def store_memory(self, memory: TypedMemoryFact) -> bool:
        """Store a typed memory with appropriate indexing."""
        memory_type = memory.memory_type
        
        # Apply retention policy
        self._apply_retention_policy(memory_type)
        
        # Store the memory
        store = self.type_stores[memory_type]
        index = self.type_indices[memory_type]
        
        # Add to store
        memory_id = len(store)
        store.append(memory)
        
        # Update indices
        for entity in memory.entities:
            index[f"entity:{entity.lower()}"].add(memory_id)
        
        # Content tokens for search
        content_tokens = memory.content.lower().split()
        for token in content_tokens[:10]:  # Limit indexing
            if len(token) > 2:
                index[f"token:{token}"].add(memory_id)
        
        # Type-specific indexing
        if memory_type == MemoryType.FACTUAL and memory.factual_metadata:
            fact_type = memory.factual_metadata.get('fact_type', 'general')
            index[f"fact_type:{fact_type}"].add(memory_id)
        
        logger.debug(f"Stored {memory_type.value} memory: {memory.content[:50]}...")
        return True

    def retrieve_by_type(self, memory_type: MemoryType, 
                        query_tokens: List[str] = None, 
                        entities: List[str] = None,
                        limit: int = 10) -> List[TypedMemoryFact]:
        """Retrieve memories of a specific type with optional filtering."""
        store = self.type_stores[memory_type]
        index = self.type_indices[memory_type]
        
        if not store:
            return []
        
        # If no filters, return most recent
        if not query_tokens and not entities:
            return store[-limit:]
        
        # Find candidate memory IDs
        candidate_ids = set(range(len(store)))
        
        # Filter by entities
        if entities:
            entity_ids = set()
            for entity in entities:
                entity_ids.update(index.get(f"entity:{entity.lower()}", set()))
            candidate_ids &= entity_ids
        
        # Filter by query tokens
        if query_tokens:
            token_ids = set()
            for token in query_tokens:
                token_ids.update(index.get(f"token:{token.lower()}", set()))
            if token_ids:
                candidate_ids &= token_ids
        
        # Get memories and sort by timestamp (most recent first)
        candidates = [(i, store[i]) for i in candidate_ids if i < len(store)]
        candidates.sort(key=lambda x: x[1].timestamp, reverse=True)
        
        return [memory for _, memory in candidates[:limit]]

    def _apply_retention_policy(self, memory_type: MemoryType):
        """Apply retention policy to prevent memory overflow."""
        policy = self.retention_policies[memory_type]
        store = self.type_stores[memory_type]
        
        current_time = time.time()
        
        # Remove expired memories
        if policy['ttl_hours'] is not None:
            ttl_seconds = policy['ttl_hours'] * 3600
            store[:] = [m for m in store if current_time - m.timestamp < ttl_seconds]
        
        # Enforce size limits
        max_items = policy['max_items']
        if len(store) >= max_items:
            # Keep most recent memories
            keep_count = int(max_items * 0.8)  # Keep 80% when cleaning
            store[:] = store[-keep_count:]
            
            # Rebuild index for this type
            self._rebuild_index(memory_type)

    def _rebuild_index(self, memory_type: MemoryType):
        """Rebuild index for a specific memory type."""
        store = self.type_stores[memory_type]
        index = self.type_indices[memory_type]
        
        # Clear and rebuild
        index.clear()
        
        for memory_id, memory in enumerate(store):
            # Entity indexing
            for entity in memory.entities:
                index[f"entity:{entity.lower()}"].add(memory_id)
            
            # Content token indexing
            content_tokens = memory.content.lower().split()
            for token in content_tokens[:10]:
                if len(token) > 2:
                    index[f"token:{token}"].add(memory_id)

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about stored memories by type."""
        stats = {}
        current_time = time.time()
        
        for memory_type in MemoryType:
            store = self.type_stores[memory_type]
            policy = self.retention_policies[memory_type]
            
            # Count valid (non-expired) memories
            if policy['ttl_hours'] is not None:
                ttl_seconds = policy['ttl_hours'] * 3600
                valid_count = sum(1 for m in store if current_time - m.timestamp < ttl_seconds)
            else:
                valid_count = len(store)
            
            stats[memory_type.value] = {
                'total_count': len(store),
                'valid_count': valid_count,
                'capacity': policy['max_items'],
                'utilization': valid_count / policy['max_items'] if policy['max_items'] else 0
            }
        
        return stats

class MemoryTypeManager:
    """High-level manager for typed memory operations."""
    
    def __init__(self):
        self.classifier = MemoryTypeClassifier()
        self.storage = TypedMemoryStorage()
        self._processing_queue = deque()
        self._processing_lock = asyncio.Lock()

    async def process_and_store_memory(self, content: str, entities: List[str],
                                     relationships: List[Tuple[str, str, str]],
                                     user_id: Optional[int] = None,
                                     channel_id: Optional[str] = None,
                                     conversation_context: Optional[str] = None) -> TypedMemoryFact:
        """Process and store a memory with automatic type classification."""
        
        # Classify the memory
        memory_type, confidence, type_metadata = self.classifier.classify_memory(
            content, entities, conversation_context
        )
        
        # Create typed memory fact
        memory = TypedMemoryFact(
            content=content,
            memory_type=memory_type,
            confidence=confidence,
            entities=entities,
            relationships=relationships,
            timestamp=time.time(),
            user_id=user_id,
            channel_id=channel_id,
            context_hash=hash(conversation_context) if conversation_context else None
        )
        
        # Add type-specific metadata
        if memory_type == MemoryType.WORKING:
            memory.working_metadata = type_metadata
        elif memory_type == MemoryType.FACTUAL:
            memory.factual_metadata = type_metadata
        elif memory_type == MemoryType.EPISODIC:
            memory.episodic_metadata = type_metadata
        elif memory_type == MemoryType.SEMANTIC:
            memory.semantic_metadata = type_metadata
        elif memory_type == MemoryType.BEHAVIORAL:
            memory.behavioral_metadata = type_metadata
        
        # Store the memory
        self.storage.store_memory(memory)
        
        logger.info(f"Stored {memory_type.value} memory with {confidence:.2f} confidence: {content[:100]}")
        return memory

    async def retrieve_relevant_memories(self, query: str, 
                                       memory_types: Optional[List[MemoryType]] = None,
                                       entities: Optional[List[str]] = None,
                                       user_id: Optional[int] = None,
                                       limit_per_type: int = 5) -> Dict[MemoryType, List[TypedMemoryFact]]:
        """Retrieve relevant memories across specified types."""
        if memory_types is None:
            memory_types = list(MemoryType)
        
        query_tokens = query.lower().split()
        results = {}
        
        for memory_type in memory_types:
            memories = self.storage.retrieve_by_type(
                memory_type=memory_type,
                query_tokens=query_tokens,
                entities=entities,
                limit=limit_per_type
            )
            
            # Filter by user if specified
            if user_id is not None:
                memories = [m for m in memories if m.user_id == user_id]
            
            results[memory_type] = memories
        
        return results

    async def get_working_memory_context(self, max_items: int = 10) -> List[TypedMemoryFact]:
        """Get current working memory for conversation context."""
        return self.storage.retrieve_by_type(
            memory_type=MemoryType.WORKING,
            limit=max_items
        )

    async def get_user_behavioral_patterns(self, user_id: int, limit: int = 20) -> List[TypedMemoryFact]:
        """Get behavioral patterns for a specific user."""
        behavioral_memories = self.storage.retrieve_by_type(
            memory_type=MemoryType.BEHAVIORAL,
            limit=limit * 2  # Get more to filter by user
        )
        
        # Filter by user
        user_patterns = [m for m in behavioral_memories if m.user_id == user_id]
        return user_patterns[:limit]

    async def get_factual_knowledge(self, entities: List[str], 
                                  fact_types: Optional[List[str]] = None) -> List[TypedMemoryFact]:
        """Get factual knowledge about specific entities."""
        factual_memories = self.storage.retrieve_by_type(
            memory_type=MemoryType.FACTUAL,
            entities=entities,
            limit=50
        )
        
        # Filter by fact types if specified
        if fact_types:
            filtered = []
            for memory in factual_memories:
                if memory.factual_metadata and memory.factual_metadata.get('fact_type') in fact_types:
                    filtered.append(memory)
            return filtered
        
        return factual_memories

    def get_memory_type_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the memory type system."""
        return self.storage.get_memory_stats()

# Global instance for use throughout Luna
memory_type_manager = MemoryTypeManager()

async def initialize_memory_types():
    """Initialize the memory type system."""
    logger.info("Memory type system initialized with 5 categories")
    logger.info("Available memory types: Working, Factual, Episodic, Semantic, Behavioral")
    return True