import os
import logging
import re
import discord
import time
import httpx
import base64
import mimetypes
import asyncio
from openai import AsyncOpenAI, OpenAI # Import AsyncOpenAI
import io
from PIL import Image

# Import necessary components from other modules within the package
from .config import (
    SCREENSHOT_COOLDOWN, CALL_NOTIFICATION_CHANNEL_ID_STR, # Removed USER_ALIASES
    LM_STUDIO_VISION_URL, LM_STUDIO_VISION_API_KEY, VISION_MODEL,
    IMAGE_ANALYSIS_MAX_TOKENS, IMAGE_ANALYSIS_TEMP, IMAGE_DOWNLOAD_TIMEOUT,
    CALL_MSG_MAX_OUTPUT_TOKENS, CALL_MSG_TEMP, TRANSCRIPT_LOG_CHANNEL_ID,
    PROMPT_LOG_CHANNEL_ID # Added for process_image_mention logging
)
from .initialization import get_lm_studio_client, create_lm_studio_client
# Removed safe_model_call import - using direct client calls instead
from .db_logger import log_message_to_db
# Import process_user_message carefully to avoid circular dependency if possible
# If commands.py is imported by processing.py, this will cause issues.
# Consider refactoring process_image_mention or passing process_user_message as an arg.
# For now, assume it might be imported later or handled differently.
# from .processing import process_user_message # Commented out for now

# Import necessary components from outside the package
from text_to_speech import StreamingSpeechPlayer
from utils import remove_emojis, format_datetime # format_datetime might not be needed here anymore
from screenshot_util import optimized_screenshot_manager, analyze_image_url_fast
from log_viewer_client import get_log_viewer_client

logger = logging.getLogger(__name__)

# Track screenshot usage to prevent abuse
last_screenshot_time = 0

# --- Screenshot Command Handling (Requires Multimodal Model - Placeholder/Disabled) ---
async def handle_screenshot_command(sink, text, user_id, display_name=None, text_channel=None):
    """Handles screenshot command using the new ultra-fast optimized system."""
    global last_screenshot_time
    viewer_client = get_log_viewer_client()
    suppress_discord_text = True  # Route all screenshot output to log viewer instead of Discord
    # Determine if this was triggered in voice context
    voice_context = False
    try:
        voice_context = bool(
            sink and (
                getattr(sink, 'voice_client', None) is not None or
                (hasattr(sink, 'bot') and getattr(sink.bot, 'voice_clients', None))
            )
        )
    except Exception:
        voice_context = False

    # Rate limit screenshots
    current_time = time.time()
    if current_time - last_screenshot_time < SCREENSHOT_COOLDOWN:
        cooldown_message = f"Please wait a moment before requesting another screenshot."
        # Log to viewer instead of Discord text
        viewer_client.send_log("screenshot_analysis", {
            "stage": "info",
            "content": cooldown_message,
            "source": "manual" if text_channel else "voice"
        })
        if text_channel and not voice_context:
            # Text channel request: inform user in channel
            try:
                await text_channel.send(cooldown_message)
            except Exception as e:
                logger.error(f"Failed to send cooldown message to text channel: {e}")
        else:
            # Voice response only when no text channel context
            voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
            if voice_client:
                if voice_client.is_playing():
                    voice_client.stop()
                sink.is_speaking = True
                player = StreamingSpeechPlayer(
                    voice_client,
                    after_play_callback=lambda e: sink._after_play(e, user_id)
                )
                await player.start()
                await player.add_text(cooldown_message)
                await player.finalize()
        return True

    # Extract custom analysis prompt if present
    custom_prompt = None
    
    # Check for test mode
    if "test api" in text.lower():
        # Note: test_api_performance function was removed in optimization
        # Route notice to terminal logs in viewer
        viewer_client.send_terminal_log("API performance test no longer available - using optimized system", level="info")
        return True
        
    # Check for tiny test mode
    if "tiny test" in text.lower():
        processing_message = "Testing with 64x64 image..."
        viewer_client.send_log("screenshot_analysis", {"stage": "start", "content": processing_message, "source": "manual" if text_channel else "voice", "prompt": "tiny test"})
        result = await optimized_screenshot_manager.capture_and_analyze_fast("tiny test")
        if result["success"]:
            total_time = result.get("total_time", 0)
            analysis = result["analysis"]
            viewer_client.send_log("screenshot_analysis", {"stage": "result", "content": analysis, "processing_time": total_time, "source": "manual" if text_channel else "voice", "prompt": "tiny test"})
        else:
            viewer_client.send_log("screenshot_analysis", {"stage": "error", "content": result.get('error', 'Unknown error'), "source": "manual" if text_channel else "voice", "prompt": "tiny test"})
        return True
    
    prompt_patterns = [
        r"screenshot (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        r"take a screenshot (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        r"screenshot (?:and|&) (find|look for|search for|locate|spot|identify) (.*)",
        r"screenshot (?:and tell me about|and look for|and find) (.*)",
        r"look for (.*?)(?:\.|\?|$|in the|on the)",
        r"find (.*?)(?:\.|\?|$|in the|on the)",
        r"search for (.*?)(?:\.|\?|$|in the|on the)",
        # Text reading patterns
        r"what does (?:that|the) text say",
        r"what (?:does )?(?:that|the|this) (?:text |message |writing )?say",
        r"read (?:that|the|this) (?:text|message|writing)",
        r"what is (?:that|the|this) text",
        r"what's (?:that|the|this) text",
        r"can you read (?:that|the|this)"
    ]
    
    for pattern in prompt_patterns:
        match = re.search(pattern, text.lower())
        if match:
            if len(match.groups()) == 1:
                custom_prompt = match.group(1).strip()
            elif len(match.groups()) >= 2:
                action = match.group(1).strip()
                target = match.group(2).strip()
                custom_prompt = f"{action} {target}"
            else:
                # Pattern with no groups - check if it's a text reading request
                if any(phrase in text.lower() for phrase in ["text say", "read", "what does", "what is", "what's"]):
                    custom_prompt = "read and transcribe all visible text in the image"
            break

    # Let the user know we're taking a screenshot
    processing_message = "Analyzing your screen..." if not custom_prompt else f"Looking for '{custom_prompt}'..."
    
    # Always log start of analysis to viewer
    viewer_client.send_log("screenshot_analysis", {
        "stage": "start",
        "content": processing_message,
        "source": "manual" if text_channel else "voice",
        "prompt": custom_prompt
    })
    if not text_channel:
        # Voice response
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(processing_message)
            await player.finalize()

    # Use the new ultra-fast optimized screenshot system with context
    if custom_prompt:
        # Add context about it being Gavin's screen to custom prompts
        contextual_prompt = f"I'm looking at Gavin's screen right now. {custom_prompt}"
    else:
        contextual_prompt = None
    
    result = await optimized_screenshot_manager.capture_and_analyze_fast(contextual_prompt)
    
    if not result["success"]:
        error_message = f"Screenshot failed: {result.get('error', 'Unknown error')}"
        # Log error to viewer
        viewer_client.send_log("screenshot_analysis", {
            "stage": "error",
            "content": error_message,
            "source": "manual" if text_channel else "voice",
            "prompt": custom_prompt
        })
        if text_channel and not voice_context:
            # Text channel request: inform user in channel
            try:
                await text_channel.send(error_message)
            except Exception as e:
                logger.error(f"Failed to send error message to text channel: {e}")
        else:
            voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
            if voice_client:
                if voice_client.is_playing(): voice_client.stop()
                sink.is_speaking = True
                player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
                await player.start()
                await player.add_text(error_message)
                await player.finalize()
        return True

    analysis = result["analysis"]
    total_time = result.get("total_time", 0)
    
    # Update rate limit
    last_screenshot_time = time.time()

    # Add analysis to chat session for context
    if sink and hasattr(sink, 'chat_session') and sink.chat_session and "messages" in sink.chat_session:
        sink.chat_session["messages"].append({"role": "assistant", "content": analysis})

    # Performance info
    perf_info = f" (completed in {total_time:.2f}s)" if total_time > 0 else ""
    
    # Deliver the response based on trigger source
    if text_channel and not voice_context:
        # Text-triggered: send to Discord text channel and update history/DB
        try:
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant", channel_type="text", channel_id=str(text_channel.id))
            bot_user_id = sink.bot.user.id if sink and hasattr(sink, 'bot') and sink.bot.user else "assistant"
            log_message_to_db(bot_user_id, "assistant", analysis, time.time(), "text", text_channel.id)
            # Send the analysis as a normal message in the text channel
            await text_channel.send(analysis)
        except Exception as e:
            logger.error(f"Error updating history/DB for screenshot response: {e}")
    else:
        # Only voice
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients and len(sink.bot.voice_clients) > 0 else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(analysis)
            await player.finalize()
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant")
            
            # Log voice response to database just like regular Luna responses
            log_message_to_db(sink.bot.user.id, "assistant", analysis, time.time(), "voice", voice_client.channel.id if voice_client else None)
    
    # Route the analysis to the log viewer
    viewer_client.send_log("screenshot_analysis", {
        "stage": "result",
        "content": analysis,
        "source": "manual" if text_channel else "voice",
        "prompt": custom_prompt
    })
    
    return True

# --- Call Message Generation ---
async def generate_call_message(target_name: str, system_prompt: str) -> str:
    """Generates a short, engaging message body for calling a user."""
    lm_studio_client = get_lm_studio_client() # Get the initialized client
    if not lm_studio_client:
        logger.error("Cannot generate call message: LM Studio client not initialized.")
        return f"Hey {target_name}, someone's asking for you!" # Fallback

    prompt = f"""You need to generate a short, casual message body to notify '{target_name}' that someone in the voice chat is asking for them. Keep it brief and friendly, encouraging them to join the voice channel.

    Examples:
    - "Hey {target_name}, hop in the voice channel! We need you."
    - "Yo {target_name}, someone's asking for you in voice chat."
    - "{target_name}! Get in here, you're being summoned!"
    - "Paging {target_name}, you're wanted in the voice channel."

    Generate ONLY the message body below (don't include the name again unless it fits naturally):
    """

    try:
        # Use a stateless call for this simple generation
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

        # Use the ultra-fast llama.cpp generate endpoint for better latency
        from llm_response.processing import _ultra_fast_llama_cpp_non_streaming_call
        
        message_body = await _ultra_fast_llama_cpp_non_streaming_call(
            messages=messages,
            model=os.environ.get("LM_STUDIO_MODEL_NAME", "luna:latest"),
            temperature=CALL_MSG_TEMP,
            max_tokens=CALL_MSG_MAX_OUTPUT_TOKENS,
            bot=None  # No bot instance available in commands context
        )

        message_body = message_body.strip() if message_body else ""
        if not message_body:
            raise ValueError("LLM returned empty message body.")
        logger.info(f"Generated call message body for {target_name}: {message_body}")
        return message_body
    except Exception as e:
        logger.error(f"Error generating call message body for {target_name}: {e}", exc_info=True)
        # Fallback message
        return f"someone's asking for you!"

# --- Image Analysis for URLs and Attachments ---
async def analyze_image_url_optimized(image_url: str, user_text: str | None = None) -> str | None:
    """Fast image URL analysis using the optimized system with intent-aware prompting."""
    try:
        logger.info(f"Starting optimized analysis of image URL: {image_url}")

        # Choose prompt based on user's text intent (simple intent detection)
        prompt = None
        try:
            lt = (user_text or "").lower()
            text_intent = any(phrase in lt for phrase in [
                "what does", "text say", "read this", "read that", "read the",
                "transcribe", "ocr", "what's that text", "what is that text"
            ])
            if text_intent:
                prompt = (
                    "Read and transcribe all visible text in the image. "
                    "Return ONLY the exact text you see, preserving line breaks. "
                    "If text is unclear, provide best-effort transcription and mark uncertain parts with (?)"
                )
            elif any(phrase in lt for phrase in ["color", "eyes", "what is she wearing", "what is he wearing", "what's in her hand", "what's in his hand"]):
                prompt = (
                    "Answer the user's question concisely using only the image content. "
                    "Focus on concrete visual facts relevant to the likely question (e.g., colors, clothing, held objects). "
                    "Respond in one short sentence."
                )
        except Exception:
            # Fallback to default prompt
            prompt = None

        if not prompt:
            prompt = "I'm looking at an image that Gavin shared. Describe this image briefly in 2-3 sentences."

        # Use our optimized image URL analysis with the selected prompt
        result = await analyze_image_url_fast(image_url, prompt)

        logger.info(f"Image URL analysis completed successfully")
        # Normalize return to a plain string
        if isinstance(result, dict):
            if result.get("success") and isinstance(result.get("analysis"), str):
                return result.get("analysis")
            # Fall back to error description if provided
            err = result.get("error") or "Unknown error"
            logger.warning(f"Image URL analysis returned non-success: {err}")
            return None
        if isinstance(result, str):
            return result
        return None
                
    except Exception as e:
        logger.error(f"Failed to analyze image URL: {e}")
        return f"Could not analyze image: {e}"

# --- Process Image Mention (Refactored to route via main pipeline) ---
async def process_image_mention(*, message, image_url, bot, system_prompt, process_user_message_func, chat_session=None, force_respond=False, mc_bridge=None):
    """Process a message that mentions Luna and includes an image attachment.

    Refactor: analyze image, then call process_user_message with image_analysis_text
    so the main response model integrates the vision context instead of replying directly.
    """
    logger.info(f"Processing image mention from {message.author.display_name} with URL: {image_url}")
    viewer_client = get_log_viewer_client()
    
    # Start log to viewer instead of Discord
    try:
        viewer_client.send_log("screenshot_analysis", {
            "stage": "start",
            "content": "Analyzing image attachment...",
            "source": "image_mention",
            "prompt": "image attachment"
        })
    except Exception:
        pass

    # Step 1: Analyze the image using optimized system
    image_analysis = await analyze_image_url_optimized(image_url, message.content or None)

    if not image_analysis:
        # Route error to viewer instead of Discord
        try:
            viewer_client.send_log("screenshot_analysis", {
                "stage": "error",
                "content": "Could not analyze that image. Vision may be offline.",
                "source": "image_mention",
                "prompt": "image attachment"
            })
        except Exception:
            pass
        return

    # Coerce to string if a dict slipped through
    if isinstance(image_analysis, dict):
        image_analysis = image_analysis.get("analysis")
    if not isinstance(image_analysis, str):
        logger.error("Image analysis is not a string; aborting image mention processing")
        try:
            viewer_client.send_log("screenshot_analysis", {
                "stage": "error",
                "content": "Vision analysis returned invalid format.",
                "source": "image_mention",
                "prompt": "image attachment"
            })
        except Exception:
            pass
        return

    logger.info(f"Image analysis complete: {image_analysis[:100]}...")

    # Step 2: Get or create a chat session for this channel or use the provided one
    if not hasattr(bot, 'chat_sessions'):
        bot.chat_sessions = {}

    if chat_session is None:
        session_key = f"text_{message.channel.id}"
        chat_session = bot.chat_sessions.get(session_key)
        if not chat_session:
            logger.info(f"Creating new chat session for channel {message.channel.id}")
            session_id = f"channel_{message.channel.id}_{int(time.time())}"
            chat_session = {
                "id": session_id,
                "messages": []
            }
            bot.chat_sessions[session_key] = chat_session

    # Do NOT persist analysis as a chat message; we inject via process_user_message's image context.
    # Also purge any previously persisted vision analysis messages to prevent accumulation in history.
    try:
        if chat_session and "messages" in chat_session and isinstance(chat_session["messages"], list):
            before_count = len(chat_session["messages"])
            chat_session["messages"] = [
                m for m in chat_session["messages"]
                if not (
                    isinstance(m, dict)
                    and m.get("role") == "assistant"
                    and isinstance(m.get("content"), str)
                    and m["content"].lstrip().startswith("[Vision Analysis]")
                )
            ]
            after_count = len(chat_session["messages"])
            if after_count != before_count:
                logger.info(f"Purged {before_count - after_count} prior vision analysis message(s) from chat_session to avoid duplication")
    except Exception as persist_err:
        logger.warning(f"Failed to purge prior vision analysis messages: {persist_err}")

    # Step 3: Route through main response pipeline with injected image analysis
    try:
        # Build conversation_history from chat_session if available (now without prior vision analysis messages)
        conversation_history = []
        if chat_session and "messages" in chat_session:
            conversation_history = chat_session["messages"].copy()

        # Call main processing; do NOT send analysis directly to channel
        # Show typing indicator and apply timeout to prevent long hangs
        async with message.channel.typing():
            try:
                await asyncio.wait_for(
                    process_user_message_func(
                        bot=bot,
                        text=message.content or "[shared an image]",
                        user_id=message.author.id,
                        conversation_history=conversation_history,
                        system_prompt=system_prompt,
                        sink=None,
                        display_name=message.author.display_name,
                        text_channel=message.channel,
                        image_analysis_text=image_analysis,
                        chat_session=chat_session,
                        force_respond=force_respond,
                        mc_bridge=mc_bridge
                    ),
                    timeout=12.0
                )
            except asyncio.TimeoutError:
                logger.warning("process_user_message timed out for image mention; sending fallback notice")
                try:
                    await message.channel.send("Still thinking about your image… I'll respond in a moment.")
                except Exception:
                    pass
    except Exception as e:
        logger.error(f"Failed to route image mention through main pipeline: {e}", exc_info=True)
    finally:
        # Step 4: Log analysis to the viewer for debugging/inspection
        try:
            viewer_client.send_log("screenshot_analysis", {
                "stage": "result",
                "content": image_analysis,
                "source": "image_mention",
                "prompt": "image attachment"
            })
        except Exception:
            pass

    logger.info(f"Completed processing image mention from {message.author.display_name}")