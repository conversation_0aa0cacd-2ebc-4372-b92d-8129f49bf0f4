#!/usr/bin/env python3
import asyncio
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType
from llm_response.processing import enhanced_get_memory_context, ULTRA_MEMORY_AVA<PERSON>ABLE

async def test_memory():
    print("🧠 Testing memory system context retrieval...")
    print(f"ULTRA_MEMORY_AVAILABLE: {ULTRA_MEMORY_AVAILABLE}")
    
    # Initialize memory system
    await initialize_memory_system()
    
    # Get memory system
    ms = get_memory_system()
    
    # Manually add some test facts
    test_facts = [
        MemoryFact(
            content="<PERSON> loves potatoes and considers them the best food",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="test_channel",
            confidence=0.9,
            timestamp=time.time(),
            entities=["Luna", "potatoes"],
            relationships=[("Luna", "loves", "potatoes")]
        ),
        MemoryFact(
            content="<PERSON> is testing the memory system upgrades",
            memory_type=MemoryType.EPISODIC,
            user_id=12345,
            channel_id="test_channel",
            confidence=0.8,
            timestamp=time.time(),
            entities=["Gavin", "memory system"],
            relationships=[("<PERSON>", "is testing", "memory system")]
        )
    ]
    
    # Store the test facts
    for fact in test_facts:
        fact_id = ms.warm_storage.store_fact(fact)
        print(f"  Stored fact {fact_id}: {fact.content[:50]}...")
    print(f"✅ Stored {len(test_facts)} test facts")
    
    # Manually update FTS table (in case it's not auto-updating)
    import sqlite3
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # Check if FTS table needs manual population
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
        print("🔧 Rebuilt FTS index")
        
        # Check table contents
        cursor = conn.execute("SELECT id, content FROM memory_facts LIMIT 5")
        print("\n📊 Database contents:")
        for row in cursor.fetchall():
            print(f"  ID {row[0]}: {row[1][:50]}...")
            
        cursor = conn.execute("SELECT COUNT(*) FROM memory_facts_fts")
        fts_count = cursor.fetchone()[0]
        print(f"  FTS table has {fts_count} entries")
    finally:
        conn.close()
    
    # Test direct memory system
    print("🔍 Testing direct memory system retrieval...")
    result = await ms.retrieve_relevant_context('potatoes', user_id=12345, max_facts=3)
    print(f"Direct memory system result for 'potatoes': '{result}' (type: {type(result)}, len: {len(result) if result else 0})")
    
    result2 = await ms.retrieve_relevant_context('Gavin testing memory', user_id=12345, max_facts=3)
    print(f"Direct memory system result for 'Gavin testing memory': '{result2}' (type: {type(result2)}, len: {len(result2) if result2 else 0})")
    
    # Test individual fact search
    facts = ms.warm_storage.search_facts(query="potatoes", user_id=12345, limit=5)
    print(f"Direct fact search results: {len(facts)} facts found")
    for i, fact in enumerate(facts):
        print(f"  Fact {i}: {fact.content[:100]}... (confidence: {fact.confidence})")
    
    # Test enhanced context function (used in processing.py) - with direct implementation
    print("\n🔍 Testing enhanced_get_memory_context function...")
    
    # Test the enhanced function directly with error catching
    async def test_enhanced_direct(query, user_id):
        print(f"  Testing enhanced context for: '{query}'")
        try:
            # Direct call to memory system (bypassing enhanced function)
            memory_system = get_memory_system()
            direct_result = await memory_system.retrieve_relevant_context(
                query=query,
                user_id=user_id,
                channel_id=None,
                max_facts=8
            )
            print(f"    Direct result: '{direct_result}' (len: {len(direct_result) if direct_result else 0})")
            
            # Now test enhanced function
            enhanced_result = await enhanced_get_memory_context(
                query_text=query,
                current_user_id=user_id
            )
            print(f"    Enhanced result: '{enhanced_result}' (len: {len(enhanced_result) if enhanced_result else 0})")
            
            return enhanced_result
        except Exception as e:
            print(f"    Error in enhanced test: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    await test_enhanced_direct("What do you know about potatoes?", 12345)
    await test_enhanced_direct("What's your favorite color?", 12345)
    
    # Check memory metrics
    print(f"Retrieval times: {ms.metrics.get('retrieval_time', [])}")
    print(f"Processing count: {len(ms.metrics.get('extraction_time', []))}")

if __name__ == "__main__":
    asyncio.run(test_memory())