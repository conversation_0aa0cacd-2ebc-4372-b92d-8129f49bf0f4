import os
import io
import base64
import logging
import asyncio
from datetime import datetime
import time
import json
import httpx
from PIL import Image, ImageGrab
from llm_response.config import LM_STUDIO_VISION_URL, LM_STUDIO_VISION_API_KEY, VISION_MODEL
import threading

logger = logging.getLogger(__name__)

# Lock for screenshot operations
_screenshot_inference_lock = asyncio.Lock()  # Async lock for inference

# No longer using llama.cpp - all vision tasks now use LM Studio API

class OptimizedScreenshotManager:
    """Ultra-fast screenshot system optimized for LM Studio vision analysis."""
    
    def __init__(self):
        os.makedirs('screenshots', exist_ok=True)
        # Use configuration from config.py
        self.lm_studio_url = LM_STUDIO_VISION_URL or "http://127.0.0.1:1234/v1/chat/completions"
        self.api_key = LM_STUDIO_VISION_API_KEY or "lm-studio-key"
        self.model_name = VISION_<PERSON>ODEL or "gemma-3-4b-it-qat"
        
        # Load <PERSON>'s personality for vision tasks
        self.system_prompt = self._load_system_prompt()
        logger.info("Optimized Screenshot Manager initialized with <PERSON>'s personality")
    
    def _load_system_prompt(self):
        """Load Luna's system prompt for vision tasks."""
        try:
            # Try vision-specific prompt first
            if os.path.exists("system_prompt_vision.txt"):
                with open("system_prompt_vision.txt", "r", encoding="utf-8") as f:
                    prompt = f.read().strip()
                    logger.info("Loaded vision-specific system prompt")
                    return prompt
            # Fallback to general system prompt
            elif os.path.exists("system_prompt.txt"):
                with open("system_prompt.txt", "r", encoding="utf-8") as f:
                    prompt = f.read().strip()
                    logger.info("Loaded general system prompt for vision")
                    return prompt
            else:
                logger.warning("No system prompt file found, using default")
                return "You are Luna, a sassy AI assistant. Respond to vision tasks in a witty, casual way."
        except Exception as e:
            logger.error(f"Error loading system prompt: {e}")
            return "You are Luna, a sassy AI assistant. Respond to vision tasks in a witty, casual way."
        
    async def capture_and_analyze_fast(self, custom_prompt: str = None) -> dict:
        """
        Ultra-fast screenshot capture and analysis using base64 + LM Studio.
        This is the ONLY method we use - no fallbacks, no failed attempts.
        """
        start_time = time.monotonic()
        
        try:
            # Step 1: Fast screenshot capture
            capture_start = time.monotonic()
            screenshot = await asyncio.to_thread(ImageGrab.grab)
            capture_time = time.monotonic() - capture_start
            logger.info(f"Screenshot capture: {capture_time:.3f}s")
            
            # Step 2: Optimize image for fast analysis (use 1024x576 - our optimal size)
            process_start = time.monotonic()
            
            # Resize to optimal dimensions (1024 width, maintain aspect ratio)
            screenshot.thumbnail((1024, 1024), Image.LANCZOS)
            
            # Convert to RGB and compress
            if screenshot.mode != 'RGB':
                screenshot = screenshot.convert('RGB')
            
            output_buffer = io.BytesIO()
            screenshot.save(output_buffer, format='JPEG', quality=85, optimize=True)
            image_bytes = output_buffer.getvalue()
            
            process_time = time.monotonic() - process_start
            logger.info(f"Image processing: {process_time:.3f}s, size: {len(image_bytes)} bytes, dimensions: {screenshot.size}")
            
            # Step 3: Fast LM Studio API call (base64 only - we know this works)
            api_start = time.monotonic()
            analysis_result = await self._fast_lm_studio_call(image_bytes, custom_prompt)
            api_time = time.monotonic() - api_start
            
            total_time = time.monotonic() - start_time
            logger.info(f"TOTAL SCREENSHOT ANALYSIS: {total_time:.3f}s (capture: {capture_time:.3f}s, process: {process_time:.3f}s, api: {api_time:.3f}s)")
            
            # Save screenshot for reference
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshots/screenshot_{timestamp}.jpg"
            with open(filename, 'wb') as f:
                f.write(image_bytes)
            
            # analysis_result may be str (legacy) or dict with 'analysis' and 'analysis_struct'
            if isinstance(analysis_result, dict):
                summary_text = analysis_result.get("analysis", "").strip()
                analysis_struct = analysis_result.get("analysis_struct")
            else:
                summary_text = str(analysis_result).strip()
                analysis_struct = None

            return {
                "success": True,
                "analysis": summary_text,
                "analysis_struct": analysis_struct,
                "total_time": total_time,
                "image_size": len(image_bytes),
                "filename": filename,
                "bytes": image_bytes  # For compatibility
            }
            
        except Exception as e:
            logger.error(f"Screenshot analysis failed: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "analysis": f"Screenshot failed: {e}"
            }
    
    async def _fast_lm_studio_call(self, image_bytes: bytes, custom_prompt: str = None) -> dict:
        """Fast screenshot analysis using LM Studio API for vision models.
        Requests structured JSON plus returns a compact summary string.
        """
        # Prepare prompt text for structured output
        base_task = (
            "You are analyzing a screenshot. Output ONLY valid minified JSON matching this schema: "
            "{\n"
            "  \"scene_overview\": string,\n"
            "  \"key_objects\": [{\"label\": string, \"attributes\": [string]}],\n"
            "  \"text_blocks\": [{\"text\": string}],\n"
            "  \"ui_elements\": [{\"type\": string, \"label\": string, \"state\": string}],\n"
            "  \"inferred_actions\": [{\"task\": string, \"rationale\": string, \"confidence\": number}],\n"
            "  \"anomalies_or_warnings\": [string]\n"
            "}\n"
            "Include OCR text in text_blocks. Do not add extra commentary."
        )
        if custom_prompt:
            prompt_text = f"{base_task} Focus: {custom_prompt}"
        else:
            prompt_text = base_task

        try:
            # Convert image to base64
            image_b64 = base64.b64encode(image_bytes).decode('utf-8')
            
            # Use async lock to prevent concurrent requests
            async with _screenshot_inference_lock:
                # Prepare messages for vision model
                messages = [
                    {"role": "system", "content": self.system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt_text},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}}
                        ]
                    }
                ]
                
                # Call LM Studio API
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        self.lm_studio_url,
                        json={
                            "model": self.model_name,
                            "messages": messages,
                            "max_tokens": 300,
                            "temperature": 0.2
                        },
                        headers={
                            "Authorization": f"Bearer {self.api_key}",
                            "Content-Type": "application/json"
                        }
                    )
                    response.raise_for_status()
                    
                    result = response.json()
                    content = result["choices"][0]["message"]["content"].strip()
                    # Attempt to parse JSON response
                    parsed = None
                    try:
                        parsed = json.loads(content)
                    except Exception:
                        # Try to extract JSON substring if model added wrappers
                        try:
                            start = content.find('{')
                            end = content.rfind('}')
                            if start != -1 and end != -1 and end > start:
                                parsed = json.loads(content[start:end+1])
                        except Exception:
                            parsed = None

                    # Build compact human summary from parsed JSON
                    summary = content
                    if isinstance(parsed, dict):
                        parts = []
                        if parsed.get("scene_overview"):
                            parts.append(parsed["scene_overview"]) 
                        ko = parsed.get("key_objects") or []
                        if ko:
                            labels = ", ".join([o.get("label", "?") for o in ko[:5]])
                            parts.append(f"Objects: {labels}")
                        tb = parsed.get("text_blocks") or []
                        if tb:
                            sample_texts = "; ".join([t.get("text", "") for t in tb[:2] if t.get("text")])
                            if sample_texts:
                                parts.append(f"OCR: {sample_texts}")
                        ia = parsed.get("inferred_actions") or []
                        if ia:
                            tasks = ", ".join([a.get("task", "") for a in ia[:2] if a.get("task")])
                            if tasks:
                                parts.append(f"Actions: {tasks}")
                        summary = " \u2014 ".join([p for p in parts if p])

                    return {"analysis": summary.strip(), "analysis_struct": parsed}
                    
        except Exception as e:
            logger.error(f"LM Studio vision API call failed: {e}")
            return {"analysis": f"Screenshot analysis failed: {e}", "analysis_struct": None}
    
    def get_mime_type(self, filename):
        """Simple MIME type detection for compatibility."""
        if filename.lower().endswith(('.jpg', '.jpeg')):
            return 'image/jpeg'
        elif filename.lower().endswith('.png'):
            return 'image/png'
        elif filename.lower().endswith('.gif'):
            return 'image/gif'
        else:
            return 'image/jpeg'  # Default
    
    async def analyze_image_url(self, image_url: str, prompt: str = None) -> str:
        """
        Analyze an image from URL using LM Studio API.
        """
        try:
            # Download image
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(image_url)
                response.raise_for_status()
                image_bytes = response.content
            
            # Process image same way as screenshot
            image = Image.open(io.BytesIO(image_bytes))
            
            # Optimize same as screenshot
            image.thumbnail((1024, 1024), Image.LANCZOS)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='JPEG', quality=85, optimize=True)
            optimized_bytes = output_buffer.getvalue()
            
            # Analyze using LM Studio API
            analysis_result = await self._fast_lm_studio_call(optimized_bytes, prompt)
            if isinstance(analysis_result, dict):
                return analysis_result.get("analysis", "")
            return str(analysis_result)
            
        except Exception as e:
            logger.error(f"Image URL analysis failed: {e}")
            return f"Could not analyze image: {e}"

# Create global instances
optimized_screenshot_manager = OptimizedScreenshotManager()
screenshot_manager = optimized_screenshot_manager  # Backward compatibility

# Legacy compatibility functions
async def capture_screenshot():
    """Legacy compatibility - redirects to optimized system"""
    result = await optimized_screenshot_manager.capture_and_analyze_fast()
    return result

async def analyze_image_url_fast(image_url: str, prompt: str = None) -> str:
    """Fast image URL analysis function."""
    return await optimized_screenshot_manager.analyze_image_url(image_url, prompt) 