"""
Emotion Detection for VTube Studio Integration

This module analyzes <PERSON>'s generated text to determine appropriate expressions
and emotions for her VTube Studio avatar. It uses intelligent keyword analysis,
sentiment detection, and context awareness to make <PERSON> more expressive.
"""

import logging
import re
from typing import Optional, Dict, List, Tuple
from dataclasses import dataclass

# Import latency tracking
try:
    from llm_response.latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

logger = logging.getLogger(__name__)

@dataclass
class EmotionMatch:
    """Represents a detected emotion with confidence and hotkey."""
    emotion: str
    hotkey_id: str
    confidence: float
    keywords_matched: List[str]

class LunaEmotionDetector:
    """Intelligent emotion detection for <PERSON>'s VTube Studio expressions."""
    
    def __init__(self):
        # Define emotion patterns with keywords and their weights
        self.emotion_patterns = {
            # Happy/Joyful expressions - <PERSON>'s style
            "happy": {
                "hotkey_id": "25ccec687e6a43e99e037a3bbe34ac5f",
                "keywords": {
                    # <PERSON>'s happy expressions
                    "minecraft": 2.5, "winning": 2.0, "victory": 2.0, "perfect": 2.0,
                    "excellent": 2.0, "brilliant": 2.0, "genius": 2.0, "love it": 2.0,
                    "absolutely": 1.5, "totally": 1.5, "exactly": 1.5, "precisely": 1.5,
                    # Positive Luna sass
                    "obviously": 1.5, "clearly": 1.5, "finally": 1.5,
                },
                "patterns": [
                    r"!{2,}",  # Multiple exclamation marks
                    r"of course",  # Luna's confident agreement
                    r"definitely",  # Strong affirmation
                ]
            },
            
            # Sad/disappointed expressions - Luna's style  
            # NOTE: Commented out - no Luna Sad hotkey created yet
            # "sad": {
            #     "hotkey_id": "fae3c40e3aea4b188d05aecbb21f212d", 
            #     "keywords": {
            #         # Luna's disappointment/sadness
            #         "disappointment": 3.0, "tragedy": 3.0, "unfortunate": 2.5, "disappointing": 2.5,
            #         "ugh": 2.0, "sigh": 2.5, "waste": 2.0, "pathetic": 2.5,
            #         "terrible": 2.0, "awful": 2.0, "horrible": 2.0, "disaster": 2.5,
            #         # Luna's sad expressions
            #         "can't believe": 2.0, "so bad": 2.0, "really bad": 2.0,
            #     },
            #     "patterns": [
            #         r"\.\.\.",  # Ellipsis indicating trailing off/sadness
            #         r"it's.*disappointing",  # Luna's disappointment patterns
            #         r"such.*tragedy",  # Drama expressions
            #     ]
            # },
            
            # Thinking/curious expressions - Luna's style
            "thinking": {
                "hotkey_id": "be71fd4278d649569d98d9835c2326e2",
                "keywords": {
                    # Luna's thoughtful expressions
                    "honestly": 2.5, "actually": 2.0, "technically": 2.5, "basically": 2.0,
                    "realistically": 2.5, "practically": 2.0, "theoretically": 2.5,
                    "fundamental": 2.5, "conceptually": 2.5, "essentially": 2.0,
                    # Luna's questioning style
                    "let me think": 3.0, "hmm": 2.0, "interesting": 1.5, "curious": 1.5,
                },
                "patterns": [
                    r"it's like",  # Luna's explanation pattern
                    r"think about it",  # Thoughtful prompts
                    r"consider this",  # Contemplative
                ]
            },
            
            # Surprised/shocked expressions - Luna's style
            "surprised": {
                "hotkey_id": "8e50a42a4a814c409282174238991cd5",
                "keywords": {
                    # Luna's surprised expressions
                    "wait what": 3.0, "hold on": 2.5, "hang on": 2.5, "excuse me": 2.5,
                    "seriously": 2.0, "really": 1.5, "actually": 1.5, "literally": 2.0,
                    "unbelievable": 2.5, "incredible": 2.0, "impossible": 2.5,
                    # Luna's shock expressions
                    "no way": 3.0, "can't be": 2.5, "are you kidding": 3.0,
                },
                "patterns": [
                    r"wait.*what",  # Luna's confused surprise
                    r"you.*kidding",  # Disbelief
                    r"!{3,}",  # Many exclamation marks
                ]
            },
            
            # Angry/frustrated expressions - Luna's style
            "angry": {
                "hotkey_id": "b2b84ce3000c47b384230835e39ace49",
                "keywords": {
                    # Luna's frustration/anger
                    "ridiculous": 2.5, "stupid": 2.0, "dumb": 2.0, "idiotic": 2.5,
                    "frustrating": 2.5, "annoying": 2.0, "irritating": 2.0, "infuriating": 3.0,
                    # Luna's strong disapproval
                    "bullshit": 3.0, "nonsense": 2.0, "garbage": 2.5, "trash": 2.0,
                    "pathetic": 2.5, "useless": 2.0, "worthless": 2.5, "terrible": 1.5,
                },
                "patterns": [
                    r"don't.*appreciate",  # Luna's complaint about lack of appreciation
                    r"people.*don't",  # Frustration with people
                    r"ugh+",  # Angry expressions
                ]
            },
            
            # Confused expressions - Luna's style
            "confused": {
                "hotkey_id": "d11c3b03414a4c838f0cb143fef2bc65",
                "keywords": {
                    # Luna's confusion
                    "what": 1.5, "huh": 2.5, "confused": 2.5, "confusing": 2.0,
                    "makes no sense": 3.0, "doesn't make sense": 3.0, "nonsensical": 2.5,
                    # Luna's questioning confusion
                    "why would": 2.0, "how does": 2.0, "what's the point": 2.5,
                    "don't understand": 2.5, "can't figure": 2.0,
                },
                "patterns": [
                    r"\?\?\?+",  # Multiple question marks
                    r"what.*even",  # Luna's confused questioning
                    r"how.*supposed",  # Confused about expectations
                ]
            },
        }
    
    def detect_emotion(self, text: str) -> Optional[EmotionMatch]:
        """
        Analyze text and detect the most appropriate emotion.
        
        Args:
            text (str): The text to analyze
            
        Returns:
            Optional[EmotionMatch]: The best emotion match, or None if no strong match
        """
        mark_latency_timestamp("emotion_detection_start")
        
        if not text or not text.strip():
            mark_latency_timestamp("emotion_detection_end")
            return None
        
        text_lower = text.lower()
        best_matches = []
        
        # Score each emotion
        for emotion, config in self.emotion_patterns.items():
            score = 0.0
            matched_keywords = []
            
            # Check keywords
            for keyword, weight in config["keywords"].items():
                if keyword in text_lower:
                    score += weight
                    matched_keywords.append(keyword)
                    logger.debug(f"🎭 Found keyword '{keyword}' for {emotion} (weight: {weight})")
            
            # Check patterns
            for pattern in config.get("patterns", []):
                if re.search(pattern, text_lower):
                    score += 1.5  # Pattern bonus
                    matched_keywords.append(f"pattern:{pattern}")
                    logger.debug(f"🎭 Found pattern '{pattern}' for {emotion}")
            
            if score > 0:
                confidence = min(score / 5.0, 1.0)  # Normalize to 0-1
                best_matches.append(EmotionMatch(
                    emotion=emotion,
                    hotkey_id=config["hotkey_id"],
                    confidence=confidence,
                    keywords_matched=matched_keywords
                ))
        
        if not best_matches:
            logger.debug("🎭 No emotions detected in text")
            mark_latency_timestamp("emotion_detection_end")
            return None
        
        # Sort by confidence and return the best match
        best_matches.sort(key=lambda x: x.confidence, reverse=True)
        best_match = best_matches[0]
        
        # Only return if confidence is above threshold
        if best_match.confidence >= 0.3:  # Minimum confidence threshold
            logger.info(f"🎭 Detected emotion: {best_match.emotion} "
                       f"(confidence: {best_match.confidence:.2f}, "
                       f"keywords: {best_match.keywords_matched})")
            mark_latency_timestamp("emotion_detection_end")
            return best_match
        else:
            logger.debug(f"🎭 Best emotion {best_match.emotion} below confidence threshold "
                        f"({best_match.confidence:.2f} < 0.3)")
            mark_latency_timestamp("emotion_detection_end")
            return None
    
    def get_contextual_emotion(self, text: str, previous_emotion: str = None) -> Optional[EmotionMatch]:
        """
        Get emotion with additional context awareness.
        
        Args:
            text (str): Current text to analyze
            previous_emotion (str): Previously detected emotion for context
            
        Returns:
            Optional[EmotionMatch]: Best emotion match with context consideration
        """
        mark_latency_timestamp("emotion_context_start")
        
        emotion_match = self.detect_emotion(text)
        
        if not emotion_match:
            mark_latency_timestamp("emotion_context_end")
            return None
        
        # Context-based adjustments
        if previous_emotion:
            # If we're continuing the same emotion, boost confidence slightly
            if emotion_match.emotion == previous_emotion:
                emotion_match.confidence = min(emotion_match.confidence * 1.1, 1.0)
                logger.debug(f"🎭 Boosted confidence for continuing emotion: {previous_emotion}")
            
            # Some emotions shouldn't rapidly switch (e.g., sad -> happy needs higher threshold)
            incompatible_switches = {
                "sad": ["happy", "excited"],
                "angry": ["happy", "excited"], 
                "happy": ["sad", "angry"],
            }
            
            if (previous_emotion in incompatible_switches and 
                emotion_match.emotion in incompatible_switches[previous_emotion]):
                # Require higher confidence for dramatic emotion switches
                if emotion_match.confidence < 0.6:
                    logger.debug(f"🎭 Suppressing dramatic emotion switch from {previous_emotion} "
                               f"to {emotion_match.emotion} (confidence too low)")
                    mark_latency_timestamp("emotion_context_end")
                    return None
        
        mark_latency_timestamp("emotion_context_end")
        return emotion_match

# Global emotion detector instance
_emotion_detector: Optional[LunaEmotionDetector] = None

def get_emotion_detector() -> LunaEmotionDetector:
    """Get or create the global emotion detector instance."""
    global _emotion_detector
    if _emotion_detector is None:
        _emotion_detector = LunaEmotionDetector()
    return _emotion_detector

def detect_emotion_for_vts(text: str, previous_emotion: str = None) -> Optional[str]:
    """
    Detect emotion and return the appropriate VTube Studio hotkey ID.
    
    Args:
        text (str): Text to analyze
        previous_emotion (str): Previous emotion for context
        
    Returns:
        Optional[str]: VTube Studio hotkey ID, or None if no emotion detected
    """
    mark_latency_timestamp("vts_emotion_start")
    
    detector = get_emotion_detector()
    emotion_match = detector.get_contextual_emotion(text, previous_emotion)
    
    if emotion_match:
        logger.info(f"🎭 Luna should express: {emotion_match.emotion} -> {emotion_match.hotkey_id}")
        mark_latency_timestamp("vts_emotion_end")
        return emotion_match.hotkey_id
    
    mark_latency_timestamp("vts_emotion_end")
    return None

def get_emotion_name_from_hotkey(hotkey_id: str) -> Optional[str]:
    """Get the emotion name from a hotkey ID for tracking."""
    detector = get_emotion_detector()
    for emotion, config in detector.emotion_patterns.items():
        if config["hotkey_id"] == hotkey_id:
            return emotion
    return None 