#!/usr/bin/env python3
"""
Test Exact Minecraft Query
===========================
Tests the exact failing query with full user filtering logic
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def test_exact_minecraft_query():
    print("🧱 Testing Exact Failing Minecraft Query")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    
    # The exact failing query
    query = "Luna, what's your favorite block in Minecraft?"
    user_id = 921637353364287489
    
    print(f"🔍 Query: '{query}'")
    print(f"👤 User ID: {user_id}")
    
    # Test step by step to see where it fails
    
    # Step 1: Test enhanced_get_memory_context (the one that's failing)
    print(f"\n1️⃣ Testing enhanced_get_memory_context:")
    try:
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=user_id,
            current_channel_id=12345,
            is_dm_channel=False
        )
        
        if context:
            print(f"   ✅ Success: {len(context)} chars")
            lines = context.split('\n')[:3]
            for line in lines:
                if line.strip():
                    print(f"     {line.strip()}")
        else:
            print(f"   ❌ Failed: No context returned")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Step 2: Test the memory system directly with no user filter
    print(f"\n2️⃣ Testing memory system with no user filter:")
    ms = get_memory_system()
    
    # Extract key terms like enhanced_get_memory_context does
    import re
    key_terms = []
    words = re.findall(r'\b\w+\b', query.lower())
    
    important_indicators = {'my', 'your', 'luna', 'i', 'you'}
    stop_words = {'what', 'is', 'are', 'do', 'know', 'about', 'the', 'a', 'an', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
    
    for word in words:
        if word in important_indicators or (word not in stop_words and len(word) > 2):
            key_terms.append(word)
    
    search_query = ' '.join(key_terms)
    print(f"   Search query: '{search_query}'")
    
    try:
        context = await ms.retrieve_relevant_context(
            query=search_query,
            user_id=None,  # No user filter for Luna query
            channel_id=None,
            max_facts=5
        )
        
        if context:
            print(f"   ✅ Success: {len(context)} chars")
            lines = context.split('\n')[:3]
            for line in lines:
                if line.strip():
                    print(f"     {line.strip()}")
        else:
            print(f"   ❌ Failed: No context returned")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Step 3: Test with user filter (wrong way)
    print(f"\n3️⃣ Testing memory system WITH user filter (should be wrong):")
    try:
        context = await ms.retrieve_relevant_context(
            query=search_query,
            user_id=user_id,  # Wrong - should not filter for Luna queries
            channel_id=None,
            max_facts=5
        )
        
        if context:
            print(f"   ✅ Success: {len(context)} chars")
            lines = context.split('\n')[:3]
            for line in lines:
                if line.strip():
                    print(f"     {line.strip()}")
        else:
            print(f"   ❌ Failed: No context returned (expected - facts are under different user)")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Step 4: Test user filtering logic
    print(f"\n4️⃣ Testing user filtering logic:")
    query_lower = query.lower()
    
    # Check for user-specific indicators (asking about the user, not Luna)
    # Use more precise matching to avoid false positives
    user_indicators = ['my ', 'mine ', 'i like', 'i love', 'i prefer', 'i said', 'i told', 'what did i']
    is_user_query = any(indicator in query_lower for indicator in user_indicators)
    
    # Check for Luna-specific indicators (asking about Luna's preferences)
    # These are strong indicators that the query is about Luna
    luna_indicators = ['luna', 'your ', 'you like', 'you love', 'you prefer', 'you enjoy', 'do you like']
    is_luna_query = any(indicator in query_lower for indicator in luna_indicators)
    
    # Additional check: if query starts with "what" but contains Luna indicators, it's about Luna
    if query_lower.startswith('what') and ('luna' in query_lower or 'your ' in query_lower):
        is_luna_query = True
        # Override user query detection if it's clearly about Luna
        if 'luna' in query_lower or 'your ' in query_lower:
            is_user_query = False
    
    print(f"   Query: '{query}'")
    print(f"   is_user_query: {is_user_query}")
    print(f"   is_luna_query: {is_luna_query}")
    
    if is_user_query and not is_luna_query:
        search_user_id = user_id
        classification = "USER query (filter by user)"
    elif is_luna_query:
        search_user_id = None
        classification = "LUNA query (no user filter)"
    else:
        search_user_id = user_id
        classification = "DEFAULT (filter by user)"
    
    print(f"   Classification: {classification}")
    print(f"   search_user_id: {search_user_id}")
    
    if search_user_id is None:
        print(f"   ✅ Correct: Should not filter by user for Luna queries")
    else:
        print(f"   ❌ Wrong: Should not filter by user for Luna queries")

if __name__ == "__main__":
    asyncio.run(test_exact_minecraft_query())