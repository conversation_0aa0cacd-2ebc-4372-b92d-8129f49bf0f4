#!/usr/bin/env python3
"""
Comprehensive Test Suite for Luna's Ultra-Low Latency Memory System

This test suite validates all components of the memory system:
- Multi-tier storage (hot cache, warm storage, cold storage)
- Memory type classification (working, factual, episodic, semantic, behavioral)
- Fact extraction and consolidation
- Ultra-fast retrieval (<5ms target)
- Background processing
- Discord-specific optimizations

Run with: python test_memory_system.py
"""

import asyncio
import time
import json
import os
import sys
import tempfile
import shutil
from typing import Dict, List, Any
import unittest

# Add the parent directory to path to import Luna modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import Luna's memory system components
from llm_response.memory_system import (
    LunaMemorySystem, MemoryFact, MemoryType, HotCache, MemoryGraph,
    WarmStorage, initialize_memory_system, get_memory_system
)

# Import memory types system
try:
    from llm_response.memory_types import (
        MemoryType as TypedMemoryType, Typed<PERSON><PERSON>oryF<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        memory_type_manager, initialize_memory_types
    )
    MEMORY_TYPES_AVAILABLE = True
except ImportError:
    MEMORY_TYPES_AVAILABLE = False
    print("⚠️ Memory types system not available for testing")

class MemorySystemTestSuite:
    """Comprehensive test suite for the memory system"""
    
    def __init__(self):
        self.temp_dir = None
        self.memory_system = None
        self.test_results = {
            'passed': 0,
            'failed': 0,
            'performance_metrics': {}
        }
    
    async def setup(self):
        """Set up test environment"""
        print("🔧 Setting up test environment...")
        
        # Create temporary directory for test databases
        self.temp_dir = tempfile.mkdtemp(prefix='luna_memory_test_')
        print(f"📁 Test directory: {self.temp_dir}")
        
        # Initialize memory system
        self.memory_system = LunaMemorySystem()
        await self.memory_system.start_background_processing()
        
        if MEMORY_TYPES_AVAILABLE:
            await initialize_memory_types()
            print("✅ Memory types system initialized")
        
        print("✅ Test environment ready")
    
    async def teardown(self):
        """Clean up test environment"""
        print("🧹 Cleaning up test environment...")
        
        if self.memory_system:
            await self.memory_system.stop_background_processing()
        
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        print("✅ Cleanup complete")
    
    def assert_test(self, condition: bool, test_name: str, details: str = ""):
        """Assert a test condition and track results"""
        if condition:
            print(f"✅ {test_name}")
            self.test_results['passed'] += 1
        else:
            print(f"❌ {test_name}: {details}")
            self.test_results['failed'] += 1
        return condition
    
    async def test_hot_cache(self):
        """Test hot cache performance and functionality"""
        print("\\n🔥 Testing Hot Cache...")
        
        cache = HotCache(max_size=100, ttl_seconds=300)
        
        # Test basic operations
        cache.put("test_key", "test_value")
        result = cache.get("test_key")
        self.assert_test(result == "test_value", "Hot cache basic storage/retrieval")
        
        # Test TTL expiration (simulate with short TTL)
        cache_short = HotCache(max_size=10, ttl_seconds=0.1)
        cache_short.put("expire_key", "expire_value")
        await asyncio.sleep(0.2)
        result = cache_short.get("expire_key")
        self.assert_test(result is None, "Hot cache TTL expiration")
        
        # Test performance
        start_time = time.time()
        for i in range(1000):
            cache.put(f"perf_key_{i}", f"perf_value_{i}")
        
        for i in range(1000):
            cache.get(f"perf_key_{i}")
        
        cache_time = (time.time() - start_time) * 1000
        self.test_results['performance_metrics']['hot_cache_1000_ops'] = f"{cache_time:.2f}ms"
        self.assert_test(cache_time < 50, f"Hot cache performance (1000 ops in {cache_time:.2f}ms)")
    
    async def test_memory_graph(self):
        """Test memory graph relationship mapping"""
        print("\\n🕸️ Testing Memory Graph...")
        
        graph = MemoryGraph()
        
        # Add nodes and edges
        graph.add_node("Luna", {"type": "assistant", "personality": "helpful"})
        graph.add_node("Gavin", {"type": "user", "role": "creator"})
        graph.add_edge("Luna", "Gavin", "created_by")
        graph.add_edge("Gavin", "Luna", "created")
        
        # Test relationship retrieval
        facts = graph.get_connected_facts("Luna", max_depth=2)
        self.assert_test(len(facts) > 0, "Memory graph relationship retrieval")
        
        # Test performance
        start_time = time.time()
        for i in range(100):
            graph.add_node(f"entity_{i}", {"type": "test"})
            graph.add_edge("Luna", f"entity_{i}", "knows")
        
        for i in range(100):
            graph.get_connected_facts(f"entity_{i}", max_depth=1)
        
        graph_time = (time.time() - start_time) * 1000
        self.test_results['performance_metrics']['memory_graph_100_ops'] = f"{graph_time:.2f}ms"
        self.assert_test(graph_time < 100, f"Memory graph performance (100 ops in {graph_time:.2f}ms)")
    
    async def test_memory_types_classification(self):
        """Test memory type classification system"""
        if not MEMORY_TYPES_AVAILABLE:
            print("\\n⚠️ Skipping memory types test (not available)")
            return
        
        print("\\n🧠 Testing Memory Types Classification...")
        
        manager = MemoryTypeManager()
        
        # Test factual memory
        factual_memory = await manager.process_and_store_memory(
            content="Luna's favorite thing is potatoes",
            entities=["Luna", "potatoes"],
            relationships=[("Luna", "likes", "potatoes")],
            user_id=12345,
            channel_id="test_channel"
        )
        
        self.assert_test(
            factual_memory.memory_type == TypedMemoryType.FACTUAL,
            "Factual memory classification"
        )
        
        # Test behavioral memory
        behavioral_memory = await manager.process_and_store_memory(
            content="Gavin usually joins voice chat in the evening",
            entities=["Gavin", "voice chat"],
            relationships=[("Gavin", "joins", "voice chat")],
            user_id=12345,
            channel_id="test_channel"
        )
        
        self.assert_test(
            behavioral_memory.memory_type == TypedMemoryType.BEHAVIORAL,
            "Behavioral memory classification"
        )
        
        # Test episodic memory
        episodic_memory = await manager.process_and_store_memory(
            content="Yesterday we played Minecraft together and built a castle",
            entities=["Minecraft", "castle"],
            relationships=[("users", "played", "Minecraft")],
            user_id=12345,
            channel_id="test_channel"
        )
        
        self.assert_test(
            episodic_memory.memory_type == TypedMemoryType.EPISODIC,
            "Episodic memory classification"
        )
        
        # Test retrieval by type
        results = await manager.retrieve_relevant_memories(
            query="potatoes",
            memory_types=[TypedMemoryType.FACTUAL],
            limit_per_type=5
        )
        
        factual_results = results.get(TypedMemoryType.FACTUAL, [])
        self.assert_test(len(factual_results) > 0, "Memory type-specific retrieval")
        
        # Test performance
        start_time = time.time()
        for i in range(10):
            await manager.process_and_store_memory(
                content=f"Test memory {i} for performance testing",
                entities=[f"entity_{i}"],
                relationships=[],
                user_id=12345,
                channel_id="test_channel"
            )
        
        classification_time = (time.time() - start_time) * 1000
        self.test_results['performance_metrics']['memory_classification_10_items'] = f"{classification_time:.2f}ms"
        self.assert_test(
            classification_time < 500,
            f"Memory classification performance (10 items in {classification_time:.2f}ms)"
        )
    
    async def test_ultra_fast_retrieval(self):
        """Test ultra-fast retrieval performance (<5ms target)"""
        print("\\n⚡ Testing Ultra-Fast Retrieval...")
        
        # Store test memories
        test_facts = [
            MemoryFact(
                content="Luna loves potatoes and considers them the best food",
                memory_type=MemoryType.FACTUAL,
                user_id=12345,
                channel_id="test_channel",
                confidence=0.9,
                timestamp=time.time(),
                entities=["Luna", "potatoes"],
                relationships=[("Luna", "loves", "potatoes")]
            ),
            MemoryFact(
                content="Gavin created Luna as a Discord chatbot",
                memory_type=MemoryType.FACTUAL,
                user_id=67890,
                channel_id="test_channel",
                confidence=0.95,
                timestamp=time.time(),
                entities=["Gavin", "Luna", "Discord"],
                relationships=[("Gavin", "created", "Luna")]
            ),
            MemoryFact(
                content="Users often play Minecraft together in the evening",
                memory_type=MemoryType.BEHAVIORAL,
                user_id=12345,
                channel_id="test_channel",
                confidence=0.8,
                timestamp=time.time(),
                entities=["Minecraft", "users"],
                relationships=[("users", "play", "Minecraft")]
            )
        ]
        
        # Store facts in warm storage
        for fact in test_facts:
            self.memory_system.warm_storage.store_fact(fact)
        
        # Test retrieval performance
        retrieval_times = []
        
        for i in range(20):  # Multiple tests for average
            start_time = time.time()
            
            context = await self.memory_system.retrieve_relevant_context(
                query="potatoes",
                user_id=12345,
                max_facts=5
            )
            
            retrieval_time = (time.time() - start_time) * 1000
            retrieval_times.append(retrieval_time)
        
        avg_retrieval_time = sum(retrieval_times) / len(retrieval_times)
        max_retrieval_time = max(retrieval_times)
        
        self.test_results['performance_metrics']['avg_retrieval_time'] = f"{avg_retrieval_time:.2f}ms"
        self.test_results['performance_metrics']['max_retrieval_time'] = f"{max_retrieval_time:.2f}ms"
        
        # Check if we meet the <5ms target
        self.assert_test(
            avg_retrieval_time < 5.0,
            f"Ultra-fast retrieval average (<5ms target): {avg_retrieval_time:.2f}ms"
        )
        
        # Check that we get relevant results
        context = await self.memory_system.retrieve_relevant_context(
            query="potatoes",
            user_id=12345,
            max_facts=5
        )
        
        self.assert_test(
            "potatoes" in context.lower() or "Luna" in context,
            "Retrieval returns relevant content"
        )
    
    async def test_background_processing(self):
        """Test background processing system"""
        print("\\n🔄 Testing Background Processing...")
        
        # Queue some conversation chunks for processing
        test_messages = [
            {"role": "user", "content": "I really love playing Minecraft"},
            {"role": "assistant", "content": "That's great! What do you like to build?"},
            {"role": "user", "content": "I prefer building castles and towers"}
        ]
        
        # Test queueing
        await self.memory_system.process_conversation_chunk(
            messages=test_messages,
            user_id=12345,
            channel_id="test_channel"
        )
        
        # Give background processor time to work
        await asyncio.sleep(0.5)
        
        # Check queue size reduced
        queue_size = self.memory_system._processing_queue.qsize()
        self.assert_test(
            queue_size == 0,
            f"Background processing queue cleared (remaining: {queue_size})"
        )
        
        # Test that memories were extracted and stored
        # This is harder to test directly, but we can check if extraction ran
        extraction_times = self.memory_system.metrics.get('extraction_time', [])
        self.assert_test(
            len(extraction_times) > 0,
            "Background fact extraction executed"
        )
    
    async def test_integration_performance(self):
        """Test end-to-end integration performance"""
        print("\\n🚀 Testing End-to-End Integration...")
        
        # Simulate a realistic Discord conversation scenario
        conversation_messages = [
            {"role": "user", "content": "Hey Luna, what do you think about potatoes?"},
            {"role": "assistant", "content": "I absolutely love potatoes! They're versatile and delicious."},
            {"role": "user", "content": "Cool! I'm thinking of playing some Minecraft later."},
            {"role": "assistant", "content": "That sounds fun! Are you building anything specific?"},
            {"role": "user", "content": "Yeah, I usually build castles on Friday evenings."}
        ]
        
        start_time = time.time()
        
        # Process conversation
        await self.memory_system.process_conversation_chunk(
            messages=conversation_messages,
            user_id=12345,
            channel_id="general"
        )
        
        # Wait for background processing
        await asyncio.sleep(1.0)
        
        # Test retrieval
        context1 = await self.memory_system.retrieve_relevant_context(
            query="what does Luna think about potatoes?",
            user_id=12345,
            channel_id="general"
        )
        
        context2 = await self.memory_system.retrieve_relevant_context(
            query="when does the user play Minecraft?",
            user_id=12345,
            channel_id="general"
        )
        
        total_time = (time.time() - start_time) * 1000
        
        self.test_results['performance_metrics']['integration_full_cycle'] = f"{total_time:.2f}ms"
        
        self.assert_test(
            len(context1) > 0,
            "Integration test: Retrieved context about potatoes"
        )
        
        self.assert_test(
            len(context2) > 0,
            "Integration test: Retrieved context about Minecraft"
        )
        
        self.assert_test(
            total_time < 2000,  # 2 seconds for full cycle
            f"Integration performance (full cycle in {total_time:.2f}ms)"
        )
    
    async def test_memory_consolidation(self):
        """Test memory consolidation and deduplication"""
        print("\\n🔄 Testing Memory Consolidation...")
        
        # Store similar facts that should be consolidated
        similar_facts = [
            MemoryFact(
                content="Luna likes potatoes",
                memory_type=MemoryType.FACTUAL,
                user_id=12345,
                channel_id="test",
                confidence=0.8,
                timestamp=time.time(),
                entities=["Luna", "potatoes"]
            ),
            MemoryFact(
                content="Luna loves potatoes",
                memory_type=MemoryType.FACTUAL,
                user_id=12345,
                channel_id="test",
                confidence=0.9,
                timestamp=time.time(),
                entities=["Luna", "potatoes"]
            ),
            MemoryFact(
                content="Luna enjoys eating potatoes",
                memory_type=MemoryType.FACTUAL,
                user_id=12345,
                channel_id="test",
                confidence=0.85,
                timestamp=time.time(),
                entities=["Luna", "potatoes"]
            )
        ]
        
        # Store all facts
        for fact in similar_facts:
            self.memory_system.warm_storage.store_fact(fact)
        
        # Retrieve and check if highest confidence is returned
        results = self.memory_system.warm_storage.search_facts(
            query="Luna potatoes",
            user_id=12345,
            limit=5
        )
        
        self.assert_test(
            len(results) > 0,
            "Memory consolidation: Facts stored and retrievable"
        )
        
        # Check if highest confidence fact is prioritized
        if results:
            highest_confidence = max(fact.confidence for fact in results)
            self.assert_test(
                highest_confidence >= 0.9,
                f"Memory consolidation: Highest confidence prioritized ({highest_confidence})"
            )
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Luna Memory System Test Suite\\n")
        print("=" * 60)
        
        try:
            await self.setup()
            
            # Run individual test suites
            await self.test_hot_cache()
            await self.test_memory_graph()
            await self.test_memory_types_classification()
            await self.test_ultra_fast_retrieval()
            await self.test_background_processing()
            await self.test_integration_performance()
            await self.test_memory_consolidation()
            
        finally:
            await self.teardown()
        
        # Print final results
        print("\\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        print(f"✅ Tests Passed: {self.test_results['passed']}")
        print(f"❌ Tests Failed: {self.test_results['failed']}")
        
        total_tests = self.test_results['passed'] + self.test_results['failed']
        success_rate = (self.test_results['passed'] / total_tests * 100) if total_tests > 0 else 0
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print("\\n⚡ PERFORMANCE METRICS:")
        for metric, value in self.test_results['performance_metrics'].items():
            print(f"  {metric}: {value}")
        
        # Determine overall result
        if self.test_results['failed'] == 0:
            print("\\n🎉 ALL TESTS PASSED! Memory system is ready for production.")
            return True
        else:
            print(f"\\n⚠️ {self.test_results['failed']} test(s) failed. Please review and fix issues.")
            return False

async def main():
    """Main test execution function"""
    test_suite = MemorySystemTestSuite()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\\n🚀 Luna's ultra-low latency memory system is fully operational!")
    else:
        print("\\n🔧 Some tests failed. Memory system needs attention.")
    
    return success

if __name__ == "__main__":
    # Run the test suite
    result = asyncio.run(main())
    sys.exit(0 if result else 1)