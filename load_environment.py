import os

def load_env_vars(filepath='local.env'):
    """
    Loads environment variables from the specified file.
    Each line should have the format VAR=VALUE.
    Comments (starting with '#') and empty lines are ignored.
    """
    with open(filepath, 'r') as file:
        for line in file:
            line = line.strip()
            # Skip comments and empty lines
            if not line or line.startswith('#'):
                continue
            # Split variable from value
            if '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()