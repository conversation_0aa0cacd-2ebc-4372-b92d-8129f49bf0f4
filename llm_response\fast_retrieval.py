"""
Luna Ultra-Fast Retrieval System
================================

Advanced caching and precomputation system for <1ms memory retrieval.
Implements multiple optimization strategies to achieve ultra-low latency.
"""

import asyncio
import hashlib
import json
import logging
import time
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Set, Tuple, Any
import heapq

from .memory_system import MemoryType, MemoryFact
from .vector_storage import get_vector_storage
from .config import USER_PROFILES, get_main_name_by_id

logger = logging.getLogger(__name__)

@dataclass
class RetrievalRequest:
    """A memory retrieval request with metadata"""
    query: str
    user_id: int
    channel_id: Optional[str]
    memory_types: List[MemoryType]
    max_results: int
    timestamp: float
    
    def cache_key(self) -> str:
        """Generate cache key for this request"""
        key_data = f"{self.query}:{self.user_id}:{self.channel_id}:{self.max_results}"
        return hashlib.md5(key_data.encode()).hexdigest()

@dataclass 
class RetrievalResult:
    """Result of a memory retrieval with metadata"""
    facts: List[MemoryFact]
    retrieval_time_ms: float
    cache_hit: bool
    source: str  # 'hot_cache', 'warm_cache', 'vector_search', 'database'
    query_embedding: Optional[List[float]] = None

class QueryPredictor:
    """Predicts likely queries and precomputes results"""
    
    def __init__(self, max_patterns: int = 500):
        self.query_patterns: Dict[str, int] = defaultdict(int)
        self.user_patterns: Dict[int, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.recent_queries: deque = deque(maxlen=1000)
        self.max_patterns = max_patterns
        self._lock = threading.RLock()
    
    def record_query(self, query: str, user_id: int):
        """Record a query for pattern analysis"""
        with self._lock:
            # Normalize query
            normalized = self._normalize_query(query)
            
            # Record patterns
            self.query_patterns[normalized] += 1
            self.user_patterns[user_id][normalized] += 1
            self.recent_queries.append((normalized, user_id, time.time()))
    
    def _normalize_query(self, query: str) -> str:
        """Normalize query for pattern matching"""
        import re
        
        # Convert to lowercase
        normalized = query.lower().strip()
        
        # Remove common variations
        normalized = re.sub(r'\b(what|how|when|where|why|tell me|show me)\b', '', normalized)
        normalized = re.sub(r'\b(about|their|his|her|the|a|an)\b', '', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        # Extract key concepts
        key_concepts = []
        for word in normalized.split():
            if len(word) > 3:  # Only keep meaningful words
                key_concepts.append(word)
        
        return ' '.join(key_concepts[:3])  # Keep top 3 concepts
    
    def get_likely_queries(self, user_id: int, limit: int = 10) -> List[Tuple[str, float]]:
        """Get likely queries for a user with confidence scores"""
        with self._lock:
            user_queries = self.user_patterns.get(user_id, {})
            global_queries = self.query_patterns
            
            # Combine user-specific and global patterns
            combined_scores = {}
            
            for query, count in user_queries.items():
                user_score = count / max(1, sum(user_queries.values()))
                global_score = global_queries.get(query, 0) / max(1, sum(global_queries.values()))
                combined_scores[query] = 0.7 * user_score + 0.3 * global_score
            
            # Add popular global queries not seen by user
            for query, count in global_queries.items():
                if query not in combined_scores:
                    global_score = count / max(1, sum(global_queries.values()))
                    combined_scores[query] = 0.2 * global_score
            
            # Sort by score and return top results
            sorted_queries = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            return sorted_queries[:limit]

class PrecomputeEngine:
    """Precomputes results for likely queries"""
    
    def __init__(self, max_precomputed: int = 200):
        self.precomputed_results: Dict[str, Tuple[RetrievalResult, float]] = {}
        self.precompute_queue: List[Tuple[float, str, int]] = []  # (priority, query, user_id)
        self.max_precomputed = max_precomputed
        self._lock = threading.RLock()
        self._processing = False
    
    def request_precompute(self, query: str, user_id: int, priority: float = 1.0):
        """Request precomputation of a query result"""
        with self._lock:
            cache_key = f"{query}:{user_id}"
            
            # Don't precompute if already cached
            if cache_key in self.precomputed_results:
                return
            
            # Add to priority queue
            heapq.heappush(self.precompute_queue, (-priority, cache_key, user_id, query))
    
    async def process_precompute_queue(self, retrieval_system):
        """Process the precompute queue in background"""
        if self._processing:
            return
        
        self._processing = True
        try:
            while True:
                with self._lock:
                    if not self.precompute_queue:
                        break
                    
                    _, cache_key, user_id, query = heapq.heappop(self.precompute_queue)
                
                # Precompute result
                try:
                    start_time = time.time()
                    result = await retrieval_system._retrieve_from_storage(query, user_id, max_results=5)
                    compute_time = time.time()
                    
                    with self._lock:
                        # Store with expiration
                        expiry_time = compute_time + 300  # 5 minute expiry
                        self.precomputed_results[cache_key] = (result, expiry_time)
                        
                        # Limit cache size
                        if len(self.precomputed_results) > self.max_precomputed:
                            # Remove oldest entries
                            current_time = time.time()
                            expired_keys = [k for k, (_, exp_time) in self.precomputed_results.items() 
                                          if exp_time < current_time]
                            for k in expired_keys:
                                del self.precomputed_results[k]
                    
                    logger.debug(f"Precomputed result for '{query}' in {(compute_time - start_time)*1000:.1f}ms")
                    
                except Exception as e:
                    logger.error(f"Error precomputing result for '{query}': {e}")
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.01)
        
        finally:
            self._processing = False
    
    def get_precomputed(self, query: str, user_id: int) -> Optional[RetrievalResult]:
        """Get precomputed result if available"""
        with self._lock:
            cache_key = f"{query}:{user_id}"
            
            if cache_key in self.precomputed_results:
                result, expiry_time = self.precomputed_results[cache_key]
                
                if time.time() < expiry_time:
                    return result
                else:
                    # Remove expired entry
                    del self.precomputed_results[cache_key]
            
            return None

class FastRetrievalSystem:
    """Ultra-fast memory retrieval system with aggressive optimization"""
    
    def __init__(self):
        # Multi-level caching
        self.l1_cache: Dict[str, Tuple[RetrievalResult, float]] = {}  # Ultra-fast cache
        self.l2_cache: Dict[str, Tuple[RetrievalResult, float]] = {}  # Larger cache
        
        # Cache configuration
        self.l1_max_size = 100  # Keep hot for <1ms access
        self.l2_max_size = 500  # Keep warm for <5ms access
        self.cache_ttl = 300   # 5 minutes
        
        # Optimization components
        self.query_predictor = QueryPredictor()
        self.precompute_engine = PrecomputeEngine()
        
        # Performance tracking
        self.stats = {
            'l1_hits': 0,
            'l2_hits': 0,
            'storage_queries': 0,
            'precompute_hits': 0,
            'total_queries': 0,
            'avg_latency_ms': 0.0
        }
        
        # User context tracking
        self.user_contexts: Dict[int, Dict[str, Any]] = defaultdict(dict)
        
        # Thread safety
        self._cache_lock = threading.RLock()
        
        # Background task for precomputation
        self._background_task: Optional[asyncio.Task] = None
    
    async def retrieve(self, query: str, user_id: int, channel_id: str = None, 
                      memory_types: List[MemoryType] = None, max_results: int = 5) -> RetrievalResult:
        """Ultra-fast memory retrieval with multi-level optimization"""
        start_time = time.time()
        
        # Create request
        request = RetrievalRequest(
            query=query,
            user_id=user_id,
            channel_id=channel_id,
            memory_types=memory_types or list(MemoryType),
            max_results=max_results,
            timestamp=start_time
        )
        
        # Record query for prediction
        self.query_predictor.record_query(query, user_id)
        
        # Try L1 cache first (ultra-fast)
        result = self._get_from_l1_cache(request)
        if result:
            self.stats['l1_hits'] += 1
            self._update_stats(start_time)
            return result
        
        # Try L2 cache (fast)
        result = self._get_from_l2_cache(request)
        if result:
            self.stats['l2_hits'] += 1
            self._promote_to_l1(request, result)
            self._update_stats(start_time)
            return result
        
        # Try precomputed results
        result = self.precompute_engine.get_precomputed(query, user_id)
        if result:
            self.stats['precompute_hits'] += 1
            self._store_in_l2_cache(request, result)
            self._update_stats(start_time)
            return result
        
        # Retrieve from storage (slower path)
        result = await self._retrieve_from_storage(query, user_id, channel_id, memory_types, max_results)
        self.stats['storage_queries'] += 1
        
        # Cache the result
        self._store_in_l2_cache(request, result)
        
        # Start background precomputation for related queries
        self._trigger_precomputation(user_id)
        
        self._update_stats(start_time)
        return result
    
    def _get_from_l1_cache(self, request: RetrievalRequest) -> Optional[RetrievalResult]:
        """Get result from L1 (ultra-fast) cache"""
        with self._cache_lock:
            cache_key = request.cache_key()
            
            if cache_key in self.l1_cache:
                result, expiry_time = self.l1_cache[cache_key]
                
                if time.time() < expiry_time:
                    result.cache_hit = True
                    result.source = 'l1_cache'
                    result.retrieval_time_ms = 0.1  # Sub-millisecond
                    return result
                else:
                    del self.l1_cache[cache_key]
            
            return None
    
    def _get_from_l2_cache(self, request: RetrievalRequest) -> Optional[RetrievalResult]:
        """Get result from L2 (fast) cache"""
        with self._cache_lock:
            cache_key = request.cache_key()
            
            if cache_key in self.l2_cache:
                result, expiry_time = self.l2_cache[cache_key]
                
                if time.time() < expiry_time:
                    result.cache_hit = True
                    result.source = 'l2_cache'
                    result.retrieval_time_ms = 2.0  # Fast
                    return result
                else:
                    del self.l2_cache[cache_key]
            
            return None
    
    def _promote_to_l1(self, request: RetrievalRequest, result: RetrievalResult):
        """Promote frequently accessed results to L1 cache"""
        with self._cache_lock:
            cache_key = request.cache_key()
            expiry_time = time.time() + self.cache_ttl
            
            # Evict if L1 is full
            if len(self.l1_cache) >= self.l1_max_size:
                oldest_key = min(self.l1_cache.keys(), 
                               key=lambda k: self.l1_cache[k][1])
                del self.l1_cache[oldest_key]
            
            self.l1_cache[cache_key] = (result, expiry_time)
    
    def _store_in_l2_cache(self, request: RetrievalRequest, result: RetrievalResult):
        """Store result in L2 cache"""
        with self._cache_lock:
            cache_key = request.cache_key()
            expiry_time = time.time() + self.cache_ttl
            
            # Evict if L2 is full
            if len(self.l2_cache) >= self.l2_max_size:
                # Remove oldest entries
                current_time = time.time()
                expired_keys = [k for k, (_, exp_time) in self.l2_cache.items() 
                              if exp_time < current_time]
                
                if expired_keys:
                    for k in expired_keys[:len(expired_keys)//2]:  # Remove half
                        del self.l2_cache[k]
                else:
                    # Remove oldest if no expired entries
                    oldest_key = min(self.l2_cache.keys(), 
                                   key=lambda k: self.l2_cache[k][1])
                    del self.l2_cache[oldest_key]
            
            self.l2_cache[cache_key] = (result, expiry_time)
    
    async def _retrieve_from_storage(self, query: str, user_id: int, channel_id: str = None, 
                                   memory_types: List[MemoryType] = None, max_results: int = 5) -> RetrievalResult:
        """Retrieve from storage systems (slower path)"""
        start_time = time.time()
        
        try:
            # Get storage systems
            from .memory_system import get_memory_system
            memory_system = get_memory_system()
            vector_storage = get_vector_storage()
            
            # Parallel search across storage systems
            tasks = []
            
            # Vector similarity search
            if vector_storage:
                tasks.append(vector_storage.search_similar(query, k=max_results))
            
            # Database search  
            if memory_system and memory_system.warm_storage:
                search_tasks = []
                for mem_type in (memory_types or [MemoryType.FACTUAL, MemoryType.BEHAVIORAL]):
                    search_tasks.append(memory_system.warm_storage.search_facts(
                        query=query, user_id=user_id, memory_type=mem_type, limit=max_results//2
                    ))
                tasks.extend(search_tasks)
            
            # Execute searches in parallel
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
            else:
                results = []
            
            # Combine and rank results
            all_facts = []
            
            # Process vector results
            if results and not isinstance(results[0], Exception):
                vector_results = results[0]
                for content, score, metadata in vector_results:
                    # Convert to MemoryFact (simplified)
                    fact = MemoryFact(
                        content=content,
                        memory_type=MemoryType.FACTUAL,
                        user_id=user_id,
                        channel_id=channel_id,
                        confidence=score,
                        timestamp=time.time()
                    )
                    all_facts.append(fact)
            
            # Process database results
            for i, result in enumerate(results[1:], 1):
                if not isinstance(result, Exception):
                    all_facts.extend(result)
            
            # Deduplicate and rank by confidence
            seen_content = set()
            unique_facts = []
            for fact in sorted(all_facts, key=lambda f: f.confidence, reverse=True):
                if fact.content not in seen_content:
                    seen_content.add(fact.content)
                    unique_facts.append(fact)
                    if len(unique_facts) >= max_results:
                        break
            
            retrieval_time = (time.time() - start_time) * 1000
            
            return RetrievalResult(
                facts=unique_facts,
                retrieval_time_ms=retrieval_time,
                cache_hit=False,
                source='storage'
            )
            
        except Exception as e:
            logger.error(f"Error in storage retrieval: {e}")
            return RetrievalResult(
                facts=[],
                retrieval_time_ms=(time.time() - start_time) * 1000,
                cache_hit=False,
                source='error'
            )
    
    def _trigger_precomputation(self, user_id: int):
        """Trigger background precomputation for likely queries"""
        try:
            likely_queries = self.query_predictor.get_likely_queries(user_id, limit=5)
            
            for query, confidence in likely_queries:
                if confidence > 0.1:  # Only precompute likely queries
                    self.precompute_engine.request_precompute(query, user_id, confidence)
            
            # Start background processing if not already running
            if self._background_task is None or self._background_task.done():
                self._background_task = asyncio.create_task(
                    self.precompute_engine.process_precompute_queue(self)
                )
        except Exception as e:
            logger.error(f"Error triggering precomputation: {e}")
    
    def _update_stats(self, start_time: float):
        """Update performance statistics"""
        self.stats['total_queries'] += 1
        query_time = (time.time() - start_time) * 1000
        
        # Update running average
        total = self.stats['total_queries']
        current_avg = self.stats['avg_latency_ms']
        self.stats['avg_latency_ms'] = ((current_avg * (total - 1)) + query_time) / total
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics"""
        total = max(1, self.stats['total_queries'])
        
        return {
            **self.stats,
            'l1_hit_rate': f"{(self.stats['l1_hits'] / total) * 100:.1f}%",
            'l2_hit_rate': f"{(self.stats['l2_hits'] / total) * 100:.1f}%",
            'total_cache_hit_rate': f"{((self.stats['l1_hits'] + self.stats['l2_hits'] + self.stats['precompute_hits']) / total) * 100:.1f}%",
            'l1_cache_size': len(self.l1_cache),
            'l2_cache_size': len(self.l2_cache),
            'precompute_queue_size': len(self.precompute_engine.precompute_queue)
        }
    
    def optimize_caches(self):
        """Optimize cache performance"""
        current_time = time.time()
        
        with self._cache_lock:
            # Clean expired entries
            expired_l1 = [k for k, (_, exp_time) in self.l1_cache.items() if exp_time < current_time]
            expired_l2 = [k for k, (_, exp_time) in self.l2_cache.items() if exp_time < current_time]
            
            for k in expired_l1:
                del self.l1_cache[k]
            for k in expired_l2:
                del self.l2_cache[k]
        
        logger.debug(f"Cache optimization: removed {len(expired_l1)} L1 + {len(expired_l2)} L2 expired entries")

# Global instance
_fast_retrieval_system: Optional[FastRetrievalSystem] = None

def get_fast_retrieval_system() -> FastRetrievalSystem:
    """Get the global fast retrieval system"""
    global _fast_retrieval_system
    if _fast_retrieval_system is None:
        _fast_retrieval_system = FastRetrievalSystem()
    return _fast_retrieval_system

async def initialize_fast_retrieval() -> bool:
    """Initialize the fast retrieval system"""
    try:
        retrieval_system = get_fast_retrieval_system()
        logger.info("✅ Fast retrieval system initialized")
        return True
    except Exception as e:
        logger.error(f"❌ Error initializing fast retrieval system: {e}")
        return False