# Ollama to GGUF Converter

This directory contains scripts to convert Ollama models to GGUF format for use with llama.cpp and llama-cpp-python.

## 🚀 Quick Start

### Convert Luna-Tune Model (Windows)
```batch
# Double-click or run from command line
convert_luna_model.bat
```

### Convert Any Model (Cross-platform)
```bash
# List available models
python convert_ollama_to_gguf.py --list

# Convert a specific model
python convert_ollama_to_gguf.py luna-tune:latest

# Convert with custom output name
python convert_ollama_to_gguf.py luna-tune:latest --output my-luna-model.gguf
```

## 📁 Files

- **`convert_ollama_to_gguf.py`** - Main Python script for converting Ollama models to GGUF
- **`convert_luna_model.bat`** - Windows batch script specifically for Luna-Tune model
- **`OLLAMA_TO_GGUF_GUIDE.md`** - Detailed technical guide (existing)

## ✨ Features

- **Cross-platform support** (Windows, Linux, macOS)
- **Automatic model discovery** - Finds Ollama models and manifests automatically
- **Error handling** - Clear error messages and validation
- **Progress indication** - Shows file sizes and copy progress
- **Overwrite protection** - Asks before overwriting existing files
- **Multiple output formats** - Custom naming and directory support

## 🔧 Requirements

- **Python 3.6+**
- **Ollama** installed and running
- **Model already downloaded** in Ollama (use `ollama pull model-name`)

## 📋 Usage Examples

### List Available Models
```bash
python convert_ollama_to_gguf.py --list
```

### Convert Luna-Tune Model
```bash
python convert_ollama_to_gguf.py luna-tune:latest
# Output: models/luna-tune-latest.gguf
```

### Convert with Custom Name
```bash
python convert_ollama_to_gguf.py luna-tune:latest --output luna-v2.gguf
# Output: models/luna-v2.gguf
```

### Convert Other Models
```bash
python convert_ollama_to_gguf.py llama3.1:8b
python convert_ollama_to_gguf.py mistral:7b --output mistral-chat.gguf
```

## 🔍 How It Works

1. **Model Discovery** - Locates Ollama models directory and manifests
2. **Manifest Parsing** - Extracts model blob hash from JSON manifest
3. **Blob Location** - Finds the actual model file in Ollama's blob storage
4. **File Copy** - Copies and renames the blob to `.gguf` format
5. **Verification** - Confirms successful copy and shows file size

## 📂 Output Location

All converted models are saved to the `./models/` directory with `.gguf` extension.

## 🛠️ Integration with Luna

The converted GGUF files can be used with:

- **llama-cpp-python** (current Luna setup)
- **Direct llama.cpp** binaries
- **Other GGUF-compatible inference engines**

### Using with Luna's Current Setup

The converted model can be loaded in Luna's existing llama-cpp-python setup by updating the model path in your configuration to point to the new `.gguf` file.

## 🚨 Troubleshooting

### "Model not found"
- Run `ollama list` to see available models
- Make sure the model is downloaded: `ollama pull model-name`

### "Ollama not found"
- Install Ollama from https://ollama.ai
- Make sure it's in your system PATH

### "Permission denied"
- Run as administrator (Windows) or with sudo (Linux/macOS)
- Check that the models directory is writable

### "Blob file not found"
- The model might be corrupted or partially downloaded
- Try re-downloading: `ollama pull model-name`

## 📊 Model Size Reference

| Model | Ollama Size | GGUF Size | Notes |
|-------|-------------|-----------|-------|
| luna-tune:latest | 4.0 GB | ~3.0 GB | Custom fine-tuned model |
| llama3.1:8b | ~4.7 GB | ~4.7 GB | Standard 8B parameter model |
| mistral:7b | ~4.1 GB | ~4.1 GB | Mistral 7B base model |

## 🔄 Re-running Conversions

The script can be run as often as needed. It will:
- Detect if output file already exists
- Ask for confirmation before overwriting
- Always use the latest version from Ollama

This is useful when you update your Ollama model and want to refresh the GGUF version.

## 💡 Tips

- **Backup important models** before conversion
- **Check file sizes** to ensure successful conversion
- **Test the GGUF file** with your target application before deleting originals
- **Use descriptive names** for custom output files to track versions 