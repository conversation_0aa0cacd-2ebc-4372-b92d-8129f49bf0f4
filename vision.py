import os
import base64
import sys
import ctypes
from llama_cpp import <PERSON><PERSON><PERSON>, llama_log_set
from llama_cpp.llama_chat_format import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

# --- STEP 1: Silence all C-level logging before anything else ---
# This is the most reliable way to get a completely silent initialization.
def silence_llama_logging(level=None): # Use 'level' if you want to re-enable later
    """
    Redirects the C-level stderr of the llama.cpp library to /dev/null
    or NUL on Windows, effectively silencing it.
    """
    # Define the callback function signature
    log_callback = ctypes.CFUNCTYPE(None, ctypes.c_int, ctypes.c_char_p, ctypes.c_void_p)
    
    # Define a Python function that does nothing
    def empty_callback(level, message, user_data):
        pass

    # Convert the Python function to a C-style function pointer
    c_callback = log_callback(empty_callback)
    
    # Set the llama.cpp logger to our silent callback
    # We need to store the callback in a global variable to prevent it from being garbage collected
    global silent_callback
    silent_callback = c_callback
    llama_log_set(silent_callback, ctypes.c_void_p(0))

# Call the function to silence logs immediately
silence_llama_logging()


# --- Configuration ---
MODEL_PATH = r"C:\Users\<USER>\Desktop\New folder (4)\chatbot\Luna-Discord\models\gemma3.gguf"
CLIP_MODEL_PATH = r"C:\Users\<USER>\Desktop\New folder (4)\chatbot\Luna-Discord\models\mmproj-F16.gguf"

# --- GPU and Performance Settings for MAXIMUM SPEED ---
N_GPU_LAYERS = -1          # Offload all layers to the GPU
MAIN_GPU = 0               # The primary GPU to use
TENSOR_SPLIT = [1.0]       # ### <<< CRITICAL: Force all layers to a single GPU. No splitting.

# --- Initialization ---
def initialize_model():
    """
    Initializes the Llama model with optimizations for minimum latency.
    """
    if not os.path.exists(MODEL_PATH) or not os.path.exists(CLIP_MODEL_PATH):
        print("Error: Model or CLIP path does not exist.")
        return None

    try:
        chat_handler = Llava15ChatHandler(
            clip_model_path=CLIP_MODEL_PATH,
            verbose=False  # Keep this false as a fallback
        )

        llm = Llama(
            model_path=MODEL_PATH,
            chat_handler=chat_handler,
            n_gpu_layers=N_GPU_LAYERS,
            main_gpu=MAIN_GPU,
            tensor_split=TENSOR_SPLIT, ### <<< CRITICAL: Prevents splitting model across GPUs
            flash_attn=True,          # Use Flash Attention for speed
            n_ctx=2048,               # Context size
            n_batch=1024,             # ### OPTIMIZED: Process prompt in large batches
            n_ubatch=512,             # ### OPTIMIZED: Set ubatch size
            logits_all=False,         # ### OPTIMIZED: We only need the next token, not all logits
            verbose=False,            # Disable Python-level info messages
        )
        return llm
    except Exception as e:
        # Re-enable logging temporarily to see the error if initialization fails
        llama_log_set(None, ctypes.c_void_p(0))
        print(f"An error occurred during model initialization: {e}")
        # The full llama.cpp error log will now print to the console
        return None

def image_to_base64(image_path):
    if not os.path.exists(image_path):
        print(f"Error: Image path does not exist: {image_path}")
        return None
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"Failed to encode image to base64: {e}")
        return None

def main():
    print("Initializing model...")
    llm = initialize_model()

    if llm is None:
        print("Exiting due to model initialization failure.")
        return

    conversation_history = [
        {"role": "system", "content": "You are a helpful assistant."}
    ]

    print("\n--- Image Analysis Chat ---")
    print("Model initialized. Enter 'exit' to quit.")

    while True:
        try:
            image_path = input("\nEnter the full path to your image: ").strip()
            if image_path.lower() == 'exit': break

            user_prompt = input("What would you like to know about the image?: ").strip()
            if user_prompt.lower() == 'exit': break

            base64_image = image_to_base64(image_path)
            if not base64_image: continue

            user_message = {
                "role": "user",
                "content": [
                    {"type": "text", "text": user_prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
                ],
            }
            conversation_history.append(user_message)

            print("\nAnalyzing...")
            
            response = llm.create_chat_completion(messages=conversation_history)

            assistant_response = response['choices'][0]['message']['content']
            print(f"\nAssistant:\n{assistant_response.strip()}")

            conversation_history.append({"role": "assistant", "content": assistant_response})

        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"\nAn error occurred: {e}")
            if conversation_history and conversation_history[-1]["role"] == "user":
                conversation_history.pop()

if __name__ == "__main__":
    main()