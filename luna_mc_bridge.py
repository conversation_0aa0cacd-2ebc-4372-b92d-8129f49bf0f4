import socketio
import threading
import logging
import queue

# Configure logging
logger = logging.getLogger("luna_mc_bridge")
logging.basicConfig(level=logging.INFO)

# MindServer connection settings
MINDSERVER_URL = "ws://localhost:1123"  # Use port 1123 as requested

class LunaMCBridge:
    def __init__(self, agent_name="lunapotato"):
        self.sio = socketio.Client()
        self.agent_name = agent_name
        self._event_queue = queue.Queue()
        self._connected = threading.Event()
        self._register_handlers()

    def _register_handlers(self):
        @self.sio.event
        def connect():
            logger.info("Connected to MindServer at %s", MINDSERVER_URL)
            self._connected.set()
            # Register Luna as a manager (if needed)
            self.sio.emit("register-agents", [self.agent_name])

        @self.sio.event
        def disconnect():
            logger.info("Disconnected from MindServer")
            self._connected.clear()

        @self.sio.on("agent-thought")
        def on_agent_thought(data):
            logger.info(f"Agent thought: {data}")
            self._event_queue.put(("thought", data))

        @self.sio.on("agent-action-start")
        def on_action_start(data):
            logger.info(f"Agent action started: {data}")
            self._event_queue.put(("action_start", data))

        @self.sio.on("agent-action-end")
        def on_action_end(data):
            logger.info(f"Agent action ended: {data}")
            self._event_queue.put(("action_end", data))

        @self.sio.on("agent-public-chat")
        def on_public_chat(data):
            logger.info(f"Agent public chat: {data}")
            self._event_queue.put(("public_chat", data))

    def connect(self):
        logger.info("Connecting to MindServer at %s...", MINDSERVER_URL)
        self.sio.connect(MINDSERVER_URL)
        self._connected.wait(timeout=10)
        if not self._connected.is_set():
            raise RuntimeError("Failed to connect to MindServer.")

    def disconnect(self):
        self.sio.disconnect()

    def send_command(self, command: str):
        """
        Send a command to the Minecraft agent as if Luna issued it.
        """
        logger.info(f"Sending command to agent '{self.agent_name}': {command}")
        self.sio.emit("luna_command_for_agent", {
            "targetAgentName": self.agent_name,
            "command": command
        })

    def get_event(self, block=True, timeout=None):
        """
        Retrieve the next event from the agent (thought, action, chat, etc).
        Returns a tuple: (event_type, data)
        """
        try:
            return self._event_queue.get(block=block, timeout=timeout)
        except queue.Empty:
            return None

    def start_background_listener(self):
        """
        Start a background thread to keep the socketio client alive.
        """
        thread = threading.Thread(target=self.sio.wait, daemon=True)
        thread.start()

# Example usage (for integration):
# bridge = LunaMCBridge(agent_name="lunapotato")
# bridge.connect()
# bridge.send_command('!come_here')
# while True:
#     event = bridge.get_event()
#     if event:
#         print(event) 