# Per-User Volume Adjustment for Discord Voice

This document explains the per-user volume adjustment feature that allows <PERSON> to boost or reduce the volume of specific users' audio before speech-to-text processing.

## Overview

The per-user volume adjustment feature helps compensate for users with quiet microphones or inconsistent audio levels. Instead of asking users to adjust their microphone settings, <PERSON> can automatically boost their audio volume during processing.

## How It Works

1. **User Identification**: When audio is received from Discord, the user ID is passed to the audio preprocessing function
2. **Volume Lookup**: The system looks up the user's configured volume multiplier from their profile
3. **Audio Adjustment**: The audio samples are multiplied by the volume multiplier
4. **Clipping Protection**: Audio is clipped to prevent values from exceeding the valid range [-1.0, 1.0]
5. **Normal Processing**: The adjusted audio continues through dB filtering and transcription

## Configuration

### User Profiles

Volume multipliers are stored in the existing user profile system in `llm_response/config.py`:

```python
USER_PROFILES = {
    443964602850738176: {
        "main_name": "Tachi",
        "aliases": ["tachi", "sir goonlord", "goonlord"],
        "volume_multiplier": 1.5  # 50% volume boost
    },
    464688859486224394: {
        "main_name": "<PERSON>",
        "aliases": ["toast", "mari", "marty"],
        "volume_multiplier": 0.8  # 20% volume reduction
    },
}
```

### Volume Multiplier Values

- **1.0**: No adjustment (default)
- **> 1.0**: Volume boost (e.g., 1.5 = 50% louder, 2.0 = double volume)
- **< 1.0**: Volume reduction (e.g., 0.8 = 20% quieter, 0.5 = half volume)
- **Range**: 0.1 to 5.0 (enforced by Discord commands)

## Discord Commands

### !volume - Manage Volume Settings

**Show all volume settings:**
```
!volume
```
Displays an embed with all users and their current volume multipliers.

**Show specific user's volume:**
```
!volume @username
```
Shows the current volume setting for the mentioned user.

**Set user's volume:**
```
!volume @username 1.5
```
Sets the volume multiplier for the mentioned user to 1.5x (50% boost).

### Examples

```
!volume                    # Show all volume settings
!volume @Tachi            # Show Tachi's current volume
!volume @Tachi 2.0        # Set Tachi's volume to 2x (double)
!volume @Mari 0.8         # Set Mari's volume to 0.8x (20% reduction)
!volume @Quiet_User 1.5   # Boost quiet user by 50%
```

## Technical Implementation

### Audio Processing Pipeline

1. **Discord Audio Reception** (`discord_sink.py`)
   - Raw audio data received with user ID
   - User ID passed to transcription function

2. **Audio Preprocessing** (`speech_to_text.py`)
   - Volume multiplier looked up by user ID
   - Audio samples multiplied by volume multiplier
   - Clipping applied to prevent distortion
   - dB level calculated on adjusted audio
   - Normal filtering and resampling continues

3. **Configuration Management** (`llm_response/config.py`)
   - Volume multipliers stored in user profiles
   - Helper functions for getting/setting volumes
   - Integration with existing user management system

### Key Functions

```python
# Get user's volume multiplier
get_volume_multiplier_by_id(user_id: int) -> float

# Set user's volume multiplier
set_volume_multiplier_by_id(user_id: int, multiplier: float) -> bool

# List all volume settings
list_volume_settings() -> dict
```

## Use Cases

### Quiet Microphones
**Problem**: User has a quiet microphone that often gets filtered out by dB threshold
**Solution**: Set volume multiplier to 1.5-2.0x to boost their audio above the threshold

```
!volume @QuietUser 1.8
```

### Loud Microphones
**Problem**: User's microphone is too loud and dominates conversations
**Solution**: Set volume multiplier to 0.6-0.8x to reduce their audio level

```
!volume @LoudUser 0.7
```

### Inconsistent Audio Levels
**Problem**: Some users are much quieter than others in the same voice channel
**Solution**: Boost quiet users and/or reduce loud users to normalize levels

```
!volume @QuietUser1 1.6
!volume @QuietUser2 1.4
!volume @LoudUser 0.8
```

## Safety Features

### Clipping Protection
Audio values are automatically clipped to the valid range [-1.0, 1.0] to prevent:
- Digital distortion
- Audio artifacts
- Transcription errors from invalid audio data

### Range Validation
Volume multipliers are restricted to reasonable ranges:
- **Minimum**: 0.1x (90% reduction)
- **Maximum**: 5.0x (500% boost)
- Prevents accidental extreme settings

### User Profile Integration
- Only works for users already in the user profile system
- Prevents unauthorized volume adjustments
- Maintains consistency with existing user management

## Monitoring and Logging

### Debug Logs
When volume adjustment is applied, you'll see logs like:
```
DEBUG: 🔊 Applied 1.5x volume adjustment for Tachi
```

### Volume Command Feedback
Commands provide clear feedback:
```
✅ Set Tachi volume to 1.5x 🔊 Boosted
✅ Set Mari volume to 0.8x 🔉 Reduced
```

### Audio Processing Logs
The existing dB filtering logs will show the adjusted audio levels:
```
DEBUG: 🔊 Audio passed dB filter: -32.1 dB >= -40.0 dB threshold (450ms)
```

## Best Practices

### Initial Setup
1. Start with all users at 1.0x (default)
2. Monitor transcription quality and dB filtering logs
3. Identify users whose audio is frequently filtered out
4. Apply modest boosts (1.2-1.5x) initially
5. Adjust based on results

### Tuning Guidelines
- **Conservative adjustments**: Start with small changes (±0.2x)
- **Test incrementally**: Make small adjustments and test
- **Monitor quality**: Watch for distortion or artifacts
- **User feedback**: Ask users if their audio sounds different

### Troubleshooting
- **Audio still filtered**: Increase volume multiplier or lower dB threshold
- **Distorted audio**: Reduce volume multiplier or check for clipping
- **No effect**: Verify user is in USER_PROFILES and command succeeded

## Integration with dB Filtering

The volume adjustment works seamlessly with the dB filtering system:

1. **Volume adjustment applied first**: Audio is boosted/reduced
2. **dB level calculated**: On the adjusted audio
3. **Filtering decision made**: Based on adjusted audio level

This means a quiet user with a 2.0x multiplier will have their dB level effectively doubled, making them much more likely to pass the dB threshold filter.

## Future Enhancements

Potential improvements:
- **Automatic volume adjustment**: Based on recent audio levels
- **Per-channel settings**: Different volumes for different voice channels
- **Temporary adjustments**: Volume boosts that expire after a time period
- **Audio quality metrics**: Monitoring for optimal adjustment levels
