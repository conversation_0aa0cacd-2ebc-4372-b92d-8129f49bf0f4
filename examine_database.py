#!/usr/bin/env python3
"""
Examine Database Facts
======================
Check what person-related facts exist in the database
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def examine_database_facts():
    print("🗃️ Examining Database Facts")
    print("=" * 40)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # Get all facts to see what we have
        print("📊 All facts in database:")
        cursor = conn.execute("""
            SELECT id, content, user_id, confidence
            FROM memory_facts 
            ORDER BY confidence DESC, id
            LIMIT 20
        """)
        
        all_facts = cursor.fetchall()
        for i, fact in enumerate(all_facts, 1):
            print(f"  {i:2d}. '{fact[1]}' (User: {fact[2]}, Conf: {fact[3]})")
        
        # Look for person names (common ones)
        print(f"\n👥 Looking for person-related facts:")
        person_names = ['gavin', 'mari', 'ethan', 'luna', 'user', 'people', 'friend', 'someone']
        
        for name in person_names:
            cursor = conn.execute("""
                SELECT COUNT(*) FROM memory_facts 
                WHERE LOWER(content) LIKE ?
            """, (f'%{name}%',))
            
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"  '{name}': {count} facts")
                
                # Show examples
                cursor = conn.execute("""
                    SELECT content FROM memory_facts 
                    WHERE LOWER(content) LIKE ?
                    LIMIT 3
                """, (f'%{name}%',))
                
                examples = cursor.fetchall()
                for example in examples:
                    print(f"    → {example[0]}")
        
        # Check FTS indexing
        print(f"\n🔍 FTS Index status:")
        try:
            cursor = conn.execute("SELECT COUNT(*) FROM memory_facts_fts")
            fts_count = cursor.fetchone()[0]
            print(f"  FTS indexed facts: {fts_count}")
            
            cursor = conn.execute("SELECT COUNT(*) FROM memory_facts")
            total_count = cursor.fetchone()[0]
            print(f"  Total facts: {total_count}")
            
            if fts_count != total_count:
                print(f"  ⚠️ FTS index may be out of sync!")
        except Exception as e:
            print(f"  ❌ FTS error: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    asyncio.run(examine_database_facts())