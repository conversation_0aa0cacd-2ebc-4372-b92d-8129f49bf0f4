#!/usr/bin/env python3
"""
Test Specific Logged Queries
============================
Tests the exact queries from the user's logs to debug the issues
"""
import asyncio
from llm_response.memory_system import get_memory_system, initialize_memory_system
from llm_response.processing import enhanced_get_memory_context

async def test_logged_queries():
    print("🔍 Testing Specific Queries from User's Logs")
    print("=" * 60)
    
    # Initialize memory system
    await initialize_memory_system()
    
    # User ID from logs
    user_id = 921637353364287489
    
    # The exact queries from the logs that had issues
    logged_queries = [
        "<PERSON>, what's your favorite vegetable?",  # Was working in logs
        "Luna, what's my favorite game?",         # Should find user facts
        "Luna, what's my favorite color?",        # Should find user facts
    ]
    
    print(f"👤 Testing with User ID: {user_id}\n")
    
    for query in logged_queries:
        print(f"🔍 Query: '{query}'")
        
        # Test the exact same function called in the logs
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=user_id,
            current_channel_id=12345,
            is_dm_channel=False
        )
        
        if context:
            # Analyze what was found
            if 'luna' in context.lower() and 'gavin said' not in context.lower():
                print(f"  ✅ CORRECT: Found Luna facts")
            elif 'gavin said' in context.lower():
                print(f"  ✅ CORRECT: Found Gavin facts (for 'my' queries)")
            else:
                print(f"  ❓ UNCLEAR: Found other facts")
            
            # Show first few facts
            lines = [line.strip() for line in context.split('\n') if line.strip()]
            for line in lines[:2]:
                print(f"     📝 {line}")
        else:
            print(f"  ❌ No context found")
        
        print()
    
    # Test the memory system logic directly
    print(f"🧠 Testing Memory System Logic:")
    ms = get_memory_system()
    
    # Test specific query classification
    test_cases = [
        ("Luna, what's my favorite color?", "Should be USER query (contains 'my')"),
        ("Luna, what's your favorite color?", "Should be LUNA query (contains 'your')"),
        ("What color do you prefer?", "Should be LUNA query (contains 'you prefer')"),
        ("What's my favorite game?", "Should be USER query (contains 'my')"),
        ("Luna, what's my favorite game?", "Should be USER query (contains 'my')")
    ]
    
    for query, expected in test_cases:
        query_lower = query.lower()
        
        # Replicate the logic from memory_system.py
        user_indicators = ['my ', 'mine', 'i like', 'i love', 'i prefer', 'i said', 'i told', 'what did i']
        is_user_query = any(indicator in query_lower for indicator in user_indicators)
        
        luna_indicators = ['luna', 'your ', 'you like', 'you love', 'you prefer', 'favorite', 'you enjoy', 'do you like', 'what do you']
        is_luna_query = any(indicator in query_lower for indicator in luna_indicators)
        
        if is_user_query:
            classification = "USER (filter by user_id)"
        elif is_luna_query and not is_user_query:
            classification = "LUNA (no user filter)"
        else:
            classification = "DEFAULT (filter by user_id)"
        
        print(f"  '{query}' → {classification}")
        print(f"    Expected: {expected}")
        print(f"    is_user_query: {is_user_query}, is_luna_query: {is_luna_query}")
        print()

if __name__ == "__main__":
    asyncio.run(test_logged_queries())