## Setup

#### Install dependencies
`pip install py-cord[voice] numpy faiss-cpu sentence-transformers google-generativeai pydub faster-whisper openai`

#### Set Environment variables
`$env:GOOGLE_API_KEY =`
`$env:DISCORD_BOT_TOKEN =`

#### Install and login to huggingface_hub
`pip install huggingface_hub`
`huggingface-cli login`

#### Put luna_prompt.txt in repository
Create a file named `luna_prompt.txt`
Put a prompt in it

#### Install CudaDNN

#### Install ffmpeg

Go to `https://www.gyan.dev/ffmpeg/builds/e` and download the `ffmpeg-git-full.7z` zip

Then unzip and put the three files (bin license and README) in a file at C:\ffmpeg
#### Install wsl
Type `wsl --install`

#### Install Docker

#### Run kokoro tts

`docker run --gpus all -p 8880:8880 ghcr.io/remsky/kokoro-fastapi-gpu:v0.1.4`

#### Run app
`.\start_app.bat`