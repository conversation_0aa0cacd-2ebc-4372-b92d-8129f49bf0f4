"""
Simple Memory System Test
========================

Basic tests for Luna's new memory system without external dependencies.
"""

import asyncio
import time
import tempfile
import os
import sys
import json

# Add the path to import Luna's modules
sys.path.append(os.path.dirname(__file__))

def test_hot_cache():
    """Test the hot cache component"""
    print("🔥 Testing Hot Cache...")
    
    from llm_response.memory_system import HotCache
    
    cache = HotCache(max_size=3, ttl_seconds=60)
    
    # Test basic operations
    cache.put("key1", "value1")
    assert cache.get("key1") == "value1", "Cache get failed"
    
    # Test eviction
    cache.put("key2", "value2")
    cache.put("key3", "value3")
    cache.put("key4", "value4")  # Should evict key1
    
    assert cache.get("key1") is None, "Eviction failed"
    assert cache.get("key2") == "value2", "Cache integrity failed"
    
    print("✅ Hot Cache tests passed")


def test_memory_graph():
    """Test the memory graph component"""
    print("🕸️ Testing Memory Graph...")
    
    from llm_response.memory_system import MemoryGraph
    
    graph = MemoryGraph()
    
    # Add nodes and edges
    graph.add_node("user1", {"name": "Tachi"})
    graph.add_node("user2", {"name": "Gavin"})
    graph.add_edge("user1", "user2", "is_friend_of", {"strength": 0.9})
    
    # Test connected facts
    facts = graph.get_connected_facts("user1")
    assert len(facts) >= 1, "Graph connection failed"
    assert facts[0] == ("user1", "is_friend_of", "user2"), "Relationship not found"
    
    print("✅ Memory Graph tests passed")


def test_warm_storage():
    """Test the warm storage component"""
    print("🗄️ Testing Warm Storage...")
    
    from llm_response.memory_system import WarmStorage, MemoryFact, MemoryType
    
    # Create temporary database
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        storage = WarmStorage(temp_db.name)
        
        # Create and store a fact
        fact = MemoryFact(
            content="User loves building castles in Minecraft",
            memory_type=MemoryType.FACTUAL,
            user_id=123,
            channel_id="456",
            confidence=0.9,
            timestamp=time.time(),
            entities=["minecraft", "castles"]
        )
        
        fact_id = storage.store_fact(fact)
        assert fact_id is not None, "Fact storage failed"
        
        # Retrieve facts
        facts = storage.search_facts(user_id=123, limit=10)
        assert len(facts) == 1, "Fact retrieval failed"
        assert facts[0].content == fact.content, "Fact content mismatch"
        
        print("✅ Warm Storage tests passed")
    
    finally:
        os.unlink(temp_db.name)


async def test_memory_system():
    """Test the complete memory system"""
    print("🧠 Testing Complete Memory System...")
    
    from llm_response.memory_system import LunaMemorySystem
    
    memory_system = LunaMemorySystem()
    
    # Test context retrieval (should work even without initialization)
    context = await memory_system.retrieve_relevant_context(
        query="test query",
        user_id=123
    )
    
    # Should return empty string since no facts are stored
    assert isinstance(context, str), "Context retrieval failed"
    
    print("✅ Memory System tests passed")


async def performance_benchmark():
    """Run performance benchmarks"""
    print("\n🚀 Performance Benchmark")
    print("=" * 50)
    
    from llm_response.memory_system import HotCache, LunaMemorySystem
    
    # Test 1: Cache Performance
    print("\n📊 Cache Performance Test")
    cache = HotCache(max_size=1000, ttl_seconds=300)
    
    # Write performance
    start_time = time.time()
    for i in range(1000):
        cache.put(f"key_{i}", f"value_{i}")
    write_time = (time.time() - start_time) * 1000
    
    # Read performance
    start_time = time.time()
    for i in range(1000):
        result = cache.get(f"key_{i}")
    read_time = (time.time() - start_time) * 1000
    
    print(f"Cache write (1000 ops): {write_time:.1f}ms ({write_time/1000:.3f}ms/op)")
    print(f"Cache read (1000 ops): {read_time:.1f}ms ({read_time/1000:.3f}ms/op)")
    
    # Test 2: Memory System Performance
    print("\n🧠 Memory System Performance Test")
    memory_system = LunaMemorySystem()
    
    # Pre-populate cache for realistic test
    memory_system.hot_cache.put("test_query_123", "cached result")
    
    retrieval_times = []
    for i in range(10):
        start_time = time.time()
        context = await memory_system.retrieve_relevant_context(
            query="test query",
            user_id=123
        )
        retrieval_time = (time.time() - start_time) * 1000
        retrieval_times.append(retrieval_time)
    
    avg_retrieval = sum(retrieval_times) / len(retrieval_times)
    min_retrieval = min(retrieval_times)
    max_retrieval = max(retrieval_times)
    
    print(f"Context retrieval avg: {avg_retrieval:.1f}ms")
    print(f"Context retrieval min: {min_retrieval:.1f}ms")
    print(f"Context retrieval max: {max_retrieval:.1f}ms")
    
    # Performance Summary
    print("\n🎯 Performance Summary")
    print("-" * 30)
    cache_target = read_time/1000 < 1.0  # <1ms per operation
    retrieval_target = avg_retrieval < 5.0  # <5ms average
    
    print(f"Cache performance: {'✅ PASS' if cache_target else '❌ FAIL'} (Target: <1ms/op, Actual: {read_time/1000:.3f}ms/op)")
    print(f"Retrieval performance: {'✅ PASS' if retrieval_target else '❌ FAIL'} (Target: <5ms, Actual: {avg_retrieval:.1f}ms)")
    
    overall = cache_target and retrieval_target
    print(f"\nOverall Performance: {'✅ PASSED' if overall else '❌ FAILED'}")
    
    return overall


def test_config_compatibility():
    """Test compatibility with Luna's existing config"""
    print("⚙️ Testing Config Compatibility...")
    
    try:
        from llm_response.config import USER_PROFILES, get_main_name_by_id
        
        # Test user profile access
        test_user_ids = list(USER_PROFILES.keys())[:3]  # Test first 3 users
        
        for user_id in test_user_ids:
            main_name = get_main_name_by_id(user_id)
            assert main_name is not None, f"Failed to get main name for user {user_id}"
        
        print("✅ Config compatibility tests passed")
        
    except Exception as e:
        print(f"❌ Config compatibility test failed: {e}")
        return False
    
    return True


async def integration_test():
    """Test integration with Luna's existing systems"""
    print("🔗 Testing System Integration...")
    
    try:
        # Test imports work
        from llm_response.memory_integration import get_memory_integration
        from llm_response.fact_extraction import get_fact_extractor
        
        integration = get_memory_integration()
        extractor = get_fact_extractor()
        
        assert integration is not None, "Memory integration not available"
        assert extractor is not None, "Fact extractor not available"
        
        print("✅ Integration tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🧪 Luna Memory System 2.0 - Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Component tests
    try:
        test_hot_cache()
        test_results.append(("Hot Cache", True))
    except Exception as e:
        print(f"❌ Hot Cache test failed: {e}")
        test_results.append(("Hot Cache", False))
    
    try:
        test_memory_graph()
        test_results.append(("Memory Graph", True))
    except Exception as e:
        print(f"❌ Memory Graph test failed: {e}")
        test_results.append(("Memory Graph", False))
    
    try:
        test_warm_storage()
        test_results.append(("Warm Storage", True))
    except Exception as e:
        print(f"❌ Warm Storage test failed: {e}")
        test_results.append(("Warm Storage", False))
    
    try:
        await test_memory_system()
        test_results.append(("Memory System", True))
    except Exception as e:
        print(f"❌ Memory System test failed: {e}")
        test_results.append(("Memory System", False))
    
    try:
        config_ok = test_config_compatibility()
        test_results.append(("Config Compatibility", config_ok))
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        test_results.append(("Config Compatibility", False))
    
    try:
        integration_ok = await integration_test()
        test_results.append(("Integration", integration_ok))
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        test_results.append(("Integration", False))
    
    # Performance benchmark
    try:
        performance_ok = await performance_benchmark()
        test_results.append(("Performance", performance_ok))
    except Exception as e:
        print(f"❌ Performance benchmark failed: {e}")
        test_results.append(("Performance", False))
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Luna Memory System 2.0 is ready for deployment.")
    else:
        print(f"\n⚠️ {total-passed} test(s) failed. Please review the issues above.")
    
    return passed == total


if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    exit(0 if success else 1)