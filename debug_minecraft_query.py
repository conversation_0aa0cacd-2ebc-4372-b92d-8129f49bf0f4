#!/usr/bin/env python3
"""
Debug Minecraft Query
=====================
Debug why "<PERSON>, what's your favorite block in Minecraft?" is failing
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_minecraft_query():
    print("🧱 Debugging Minecraft Block Query")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # The failing query
    query = "luna your favorite block minecraft"
    
    print(f"🔍 Query: '{query}'")
    
    # Check what facts exist about Minecraft
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        print(f"\n📊 Facts containing 'minecraft', 'block', or 'cobblestone':")
        cursor = conn.execute("""
            SELECT id, content, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%minecraft%' OR content LIKE '%block%' OR content LIKE '%cobblestone%' OR content LIKE '%obsidian%'
            ORDER BY confidence DESC
        """)
        
        minecraft_facts = cursor.fetchall()
        for i, fact in enumerate(minecraft_facts, 1):
            print(f"  {i}. '{fact[1]}' (User: {fact[2]}, Conf: {fact[3]})")
        
        # Test FTS search for each term
        print(f"\n🔍 Testing FTS search for individual terms:")
        test_terms = ['minecraft', 'block', 'cobblestone', 'obsidian', 'favorite']
        
        for term in test_terms:
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 3
            """, (f'"{term}"',))
            
            results = cursor.fetchall()
            print(f"   '{term}' → {len(results)} results:")
            for result in results:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
        
        # Test the complex query with AND logic
        print(f"\n🔍 Testing complex AND query:")
        and_query = '"luna" AND "favorite" AND "block" AND "minecraft"'
        print(f"   Query: {and_query}")
        
        try:
            cursor = conn.execute("""
                SELECT mf.id, mf.content, mf.confidence 
                FROM memory_facts mf
                WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                ORDER BY mf.confidence DESC
                LIMIT 5
            """, (and_query,))
            
            results = cursor.fetchall()
            print(f"   Results: {len(results)}")
            for result in results:
                print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test priority term selection logic
        print(f"\n🏆 Testing priority term selection:")
        query_terms = ["luna", "your", "favorite", "block", "minecraft"]
        
        # Convert "your" to "luna"
        query_terms = ['luna' if term == 'your' else term for term in query_terms]
        
        # Add semantic expansions (minecraft and block should be in expansions)
        semantic_expansions = {
            'vegetable': ['food', 'potatoes', 'loves'],
            'game': ['minecraft', 'block', 'cobblestone'],
            'gaming': ['minecraft', 'block', 'cobblestone'],
        }
        
        # Priority mapping
        term_priority = {
            'color': 10, 'purple': 10,
            'food': 10, 'potatoes': 10, 'vegetable': 10, 'vegetables': 10, 'loves': 9, 'likes': 9,
            'block': 10, 'minecraft': 10, 'cobblestone': 10, 'obsidian': 10,
            'favorite': 7,
            'luna': 3  # General term, lower priority
        }
        
        print(f"   Query terms: {query_terms}")
        
        prioritized_terms = []
        for term in query_terms:
            if term in term_priority:
                priority = term_priority[term]
                prioritized_terms.append((priority, term))
                print(f"   '{term}' → priority {priority}")
        
        prioritized_terms.sort(reverse=True)
        
        print(f"   Prioritized: {prioritized_terms}")
        
        # Test each priority term
        for priority, term in prioritized_terms:
            cursor = conn.execute("""
                SELECT COUNT(*) FROM memory_facts_fts WHERE memory_facts_fts MATCH ?
            """, (f'"{term}"',))
            
            count = cursor.fetchone()[0]
            print(f"   '{term}' (priority {priority}) → {count} results")
            
            if count > 0:
                print(f"   ✅ This should be selected as the successful term")
                
                cursor = conn.execute("""
                    SELECT mf.id, mf.content, mf.confidence 
                    FROM memory_facts mf
                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.confidence DESC
                    LIMIT 3
                """, (f'"{term}"',))
                
                results = cursor.fetchall()
                for result in results:
                    print(f"     {result[0]}: {result[1]} (conf: {result[2]})")
                break
    
    finally:
        conn.close()

if __name__ == "__main__":
    asyncio.run(debug_minecraft_query())