import sqlite3
import logging
import time # Needed for timestamp in case of direct calls, though usually passed in

# Import DB_PATH from the config module within the same package
from .config import DB_PATH

# Set up logger for this module
logger = logging.getLogger(__name__)

def log_message_to_db(user_id, role, content, timestamp, channel_type, channel_id):
    """Logs a message entry directly to the database."""
    conn = None
    try:
        # Use the imported DB_PATH
        conn = sqlite3.connect(DB_PATH, timeout=10)
        cursor = conn.cursor()
        db_query = """
            INSERT INTO interactions
            (user_id, role, content, timestamp, channel_type, channel_id)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        # Note: audio_start_time is omitted for text messages
        db_params = (str(user_id), role, content, timestamp, channel_type, str(channel_id))
        cursor.execute(db_query, db_params)
        conn.commit()
        logger.debug(f"Logged message to DB: Role {role}, User {user_id} in Channel {channel_id}")
    except sqlite3.Error as e:
        logger.error(f"SQLite error logging message to DB: {e} - Query: '{db_query}' Params: {db_params}", exc_info=True)
    except Exception as e:
        logger.error(f"Unexpected error logging message to DB: {e}", exc_info=True)
    finally:
        if conn:
            conn.close()

def get_conversation_history(limit=100000, channel_id=None):
    """Retrieves conversation history from the database.

    Args:
        limit (int): Maximum number of messages to retrieve
        channel_id (str, optional): If provided, only get messages from this channel

    Returns:
        list: List of message dictionaries with role, content, timestamp, and user_id
    """
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH, timeout=10)
        cursor = conn.cursor()

        if channel_id:
            query = """
                SELECT role, content, timestamp, user_id, channel_type, channel_id
                FROM interactions
                WHERE channel_id = ?
                ORDER BY timestamp ASC
                LIMIT ?
            """
            cursor.execute(query, (str(channel_id), limit))
        else:
            query = """
                SELECT role, content, timestamp, user_id, channel_type, channel_id
                FROM interactions
                ORDER BY timestamp ASC
                LIMIT ?
            """
            cursor.execute(query, (limit,))

        history = [
            {
                "role": row[0],
                "content": row[1],
                "timestamp": row[2],
                "user_id": row[3],
                "channel_type": row[4],
                "channel_id": row[5]
            }
            for row in cursor.fetchall()
        ]

        logger.debug(f"Retrieved {len(history)} messages from conversation history")
        return history
    except sqlite3.Error as e:
        logger.error(f"SQLite error retrieving conversation history: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"Unexpected error retrieving conversation history: {e}", exc_info=True)
        return []
    finally:
        if conn:
            conn.close()

# Example usage (optional, for testing)
if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG)
    logger.info("Testing database logging...")
    # Ensure the table exists before testing
    try:
        conn_test = sqlite3.connect(DB_PATH)
        conn_test.execute("""
            CREATE TABLE IF NOT EXISTS interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                role TEXT,
                content TEXT,
                timestamp REAL,
                channel_type TEXT,
                channel_id TEXT,
                audio_start_time REAL DEFAULT NULL
            );
        """)
        conn_test.commit()
        conn_test.close()
        logger.info("Checked/Created 'interactions' table.")
    except Exception as setup_e:
        logger.error(f"Failed to setup DB for testing: {setup_e}")

    log_message_to_db(
        user_id="test_user_123",
        role="user",
        content="This is a test message.",
        timestamp=time.time(),
        channel_type="test",
        channel_id="test_channel_456"
    )
    logger.info("Test log entry attempted.")