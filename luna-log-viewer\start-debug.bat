@echo off
title Luna Control Center - Debug
echo =====================================
echo    LUNA CONTROL CENTER - DEBUG      
echo =====================================
echo.

echo DEBUG: Starting script...
echo DEBUG: Current directory is: %cd%

cd /d "%~dp0"
echo DEBUG: Changed to script directory: %cd%

echo.
echo Checking Node.js...
node --version
if errorlevel 1 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

echo.
echo Checking npm...
npm --version
if errorlevel 1 (
    echo ERROR: npm not found
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
npm install
if errorlevel 1 (
    echo ERROR: npm install failed
    pause
    exit /b 1
)

echo.
echo Starting Electron app...
npm start
