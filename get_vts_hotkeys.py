#!/usr/bin/env python3
"""
Get current VTube Studio hotkeys and update the emotion detection file.

This script connects to VTube Studio, retrieves all available hotkeys,
and helps update the hotkey IDs in vtube_studio_emotions.py.
"""

import asyncio
import json
import logging
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set environment variable to enable VTube Studio for this script
os.environ["VTUBE_STUDIO_ENABLED"] = "true"

try:
    import vtube_studio_client
    from llm_response.config import VTUBE_STUDIO_ENABLED
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the Luna directory")
    sys.exit(1)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def get_and_display_hotkeys():
    """Get hotkeys from VTube Studio and display them organized by type."""
    
    print(f"🔧 VTube Studio enabled: {VTUBE_STUDIO_ENABLED}")
    
    if not VTUBE_STUDIO_ENABLED:
        print("❌ VTube Studio is not enabled in config")
        print("💡 Make sure VTUBE_STUDIO_ENABLED=true in your environment")
        return False
    
    print("🎭 Connecting to VTube Studio...")
    
    # Connect to VTube Studio
    success = await vtube_studio_client.connect_vts()
    if not success:
        print("❌ Failed to connect to VTube Studio")
        print("Make sure VTube Studio is running with API enabled (port 8001)")
        print("Also ensure the API toggle is ON in VTube Studio settings")
        return False
    
    print("✅ Connected to VTube Studio!")
    
    # Get available hotkeys
    hotkeys = await vtube_studio_client.get_available_hotkeys()
    if not hotkeys:
        print("❌ Failed to get hotkeys from VTube Studio")
        return False
    
    print(f"✅ Found {len(hotkeys)} hotkeys")
    print("\n" + "="*80)
    print("AVAILABLE HOTKEYS")
    print("="*80)
    
    # Organize hotkeys by type for easier reading
    hotkey_types = {}
    for hotkey in hotkeys:
        hotkey_type = hotkey.get('type', 'Unknown')
        if hotkey_type not in hotkey_types:
            hotkey_types[hotkey_type] = []
        hotkey_types[hotkey_type].append(hotkey)
    
    # Display hotkeys organized by type
    for hotkey_type, type_hotkeys in sorted(hotkey_types.items()):
        print(f"\n📂 {hotkey_type} ({len(type_hotkeys)} hotkeys):")
        print("-" * 60)
        
        for hotkey in type_hotkeys:
            name = hotkey.get('name', 'Unnamed')
            hotkey_id = hotkey.get('hotkeyID', 'No ID')
            description = hotkey.get('description', 'No description')
            
            print(f"  🎯 {name}")
            print(f"     ID: {hotkey_id}")
            print(f"     Description: {description}")
            print()
    
    # Look for potential emotion-related hotkeys
    print("\n" + "="*80)
    print("POTENTIAL EMOTION HOTKEYS")
    print("="*80)
    
    emotion_keywords = [
        'happy', 'joy', 'smile', 'laugh', 'excited',
        'sad', 'cry', 'tear', 'disappointed', 'down',
        'angry', 'mad', 'rage', 'frustrated', 'annoyed',
        'surprised', 'shock', 'wow', 'amazed', 'gasp',
        'confused', 'puzzled', 'question', 'wonder', 'think',
        'thinking', 'pondering', 'curious', 'hmm'
    ]
    
    emotion_hotkeys = []
    for hotkey in hotkeys:
        name = hotkey.get('name', '').lower()
        description = hotkey.get('description', '').lower()
        
        for keyword in emotion_keywords:
            if keyword in name or keyword in description:
                emotion_hotkeys.append((hotkey, keyword))
                break
    
    if emotion_hotkeys:
        print("Found these potentially emotion-related hotkeys:")
        print()
        for hotkey, keyword in emotion_hotkeys:
            name = hotkey.get('name', 'Unnamed')
            hotkey_id = hotkey.get('hotkeyID', 'No ID')
            description = hotkey.get('description', 'No description')
            
            print(f"  🎭 {name} (matched keyword: '{keyword}')")
            print(f"     ID: {hotkey_id}")
            print(f"     Description: {description}")
            print()
    else:
        print("No obviously emotion-related hotkeys found.")
        print("You may need to manually identify which hotkeys correspond to emotions.")
    
    # Generate suggested Python code for updating vtube_studio_emotions.py
    print("\n" + "="*80)
    print("SUGGESTED UPDATES FOR vtube_studio_emotions.py")
    print("="*80)
    
    if emotion_hotkeys:
        print("Based on the emotion-related hotkeys found, here are suggested updates:")
        print()
        print("```python")
        print("# Update these hotkey IDs in vtube_studio_emotions.py:")
        print()
        
        emotion_mapping = {
            'happy': ['happy', 'joy', 'smile', 'laugh', 'excited'],
            'sad': ['sad', 'cry', 'tear', 'disappointed', 'down'],
            'angry': ['angry', 'mad', 'rage', 'frustrated', 'annoyed'],
            'surprised': ['surprised', 'shock', 'wow', 'amazed', 'gasp'],
            'confused': ['confused', 'puzzled', 'question', 'wonder'],
            'thinking': ['thinking', 'pondering', 'curious', 'hmm', 'think']
        }
        
        for emotion, keywords in emotion_mapping.items():
            matching_hotkeys = []
            for hotkey, keyword in emotion_hotkeys:
                if keyword in keywords:
                    matching_hotkeys.append(hotkey)
            
            if matching_hotkeys:
                print(f'"{emotion}": {{')
                # Show the first matching hotkey as the suggestion
                best_match = matching_hotkeys[0]
                hotkey_id = best_match.get('hotkeyID', 'NO_ID_FOUND')
                print(f'    "hotkey_id": "{hotkey_id}",  # {best_match.get("name", "Unnamed")}')
                print('    # ... rest of emotion config')
                print('},')
                print()
    else:
        print("Since no emotion-related hotkeys were automatically detected,")
        print("you'll need to manually map hotkeys to emotions.")
        print()
        print("Look through the hotkey list above and identify which ones")
        print("correspond to different emotions, then update vtube_studio_emotions.py")
        print("with their hotkey IDs.")
    
    print("```")
    print()
    print("💡 Copy the hotkey IDs you want to use and update vtube_studio_emotions.py manually.")
    
    # Save full hotkey data to a file for reference
    try:
        with open('vts_hotkeys_dump.json', 'w') as f:
            json.dump(hotkeys, f, indent=2)
        print(f"\n📁 Full hotkey data saved to: vts_hotkeys_dump.json")
    except Exception as e:
        print(f"⚠️ Failed to save hotkey data: {e}")
    
    # Close the VTube Studio connection
    await vtube_studio_client.close_vts_client()
    
    return True

async def main():
    """Main function."""
    try:
        await get_and_display_hotkeys()
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        logger.error(f"❌ Error: {e}", exc_info=True)
    finally:
        # Ensure we close the connection
        try:
            await vtube_studio_client.close_vts_client()
        except Exception:
            pass

if __name__ == "__main__":
    asyncio.run(main()) 