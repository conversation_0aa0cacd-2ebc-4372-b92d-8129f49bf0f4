#!/usr/bin/env python3
"""
Check User Facts
===============
Checks what facts exist for the actual user ID
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def check_user_facts():
    print("👤 Checking Facts for Real User ID")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    user_id = 921637353364287489  # Gavin's real Discord ID
    
    # Connect to database
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # Check facts for the specific user
        print(f"🔍 Facts for user {user_id}:")
        cursor = conn.execute("""
            SELECT id, content, memory_type, confidence, entities
            FROM memory_facts 
            WHERE user_id = ?
            ORDER BY confidence DESC
        """, (user_id,))
        
        user_facts = cursor.fetchall()
        print(f"Found {len(user_facts)} facts for user {user_id}:")
        for fact in user_facts:
            print(f"  {fact[0]}. {fact[1]} (conf: {fact[3]})")
        
        # Check facts that might be relevant to "favorite game" or "favorite color"
        print(f"\n🎮 Facts containing 'game':")
        cursor = conn.execute("""
            SELECT id, content, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%game%'
            ORDER BY confidence DESC
        """)
        game_facts = cursor.fetchall()
        for fact in game_facts:
            print(f"  {fact[0]}. {fact[1]} (user: {fact[2]}, conf: {fact[3]})")
        
        print(f"\n🎨 Facts containing 'color' for different users:")
        cursor = conn.execute("""
            SELECT id, content, user_id, confidence
            FROM memory_facts 
            WHERE content LIKE '%color%'
            ORDER BY confidence DESC
        """)
        color_facts = cursor.fetchall()
        for fact in color_facts:
            print(f"  {fact[0]}. {fact[1]} (user: {fact[2]}, conf: {fact[3]})")
        
        # Test the actual search with user filtering
        print(f"\n🔍 Testing search_facts with user filtering:")
        
        # Test 1: Search for "favorite color" with user filter
        facts = ms.warm_storage.search_facts(
            query="favorite color",
            user_id=user_id,
            limit=5
        )
        print(f"Query 'favorite color' with user {user_id}: {len(facts)} facts")
        for fact in facts:
            print(f"  - {fact.content} (user: {fact.user_id})")
        
        # Test 2: Search for "favorite color" without user filter
        facts = ms.warm_storage.search_facts(
            query="favorite color",
            user_id=None,
            limit=5
        )
        print(f"Query 'favorite color' without user filter: {len(facts)} facts")
        for fact in facts:
            print(f"  - {fact.content} (user: {fact.user_id})")
        
        # Test 3: Search for "game" with user filter
        facts = ms.warm_storage.search_facts(
            query="game",
            user_id=user_id,
            limit=5
        )
        print(f"Query 'game' with user {user_id}: {len(facts)} facts")
        for fact in facts:
            print(f"  - {fact.content} (user: {fact.user_id})")
    
    finally:
        conn.close()
    
    print(f"\n💡 Analysis:")
    print(f"   - If no facts exist for user {user_id}, that explains the issue")
    print(f"   - The system should return NOTHING for user queries when no user facts exist")
    print(f"   - Currently it's returning Luna facts even with user filtering")

if __name__ == "__main__":
    asyncio.run(check_user_facts())