"""Lightweight Pokémon Retrieval module for <PERSON>.

This module is intentionally simple and dependency-free to minimise latency.
It lazily loads `datasets/pokemon_dataset.csv` into an in-memory dict on first use
and exposes two helpers:

1. needs_pokemon_context(question: str) -> tuple[bool, str | None]
   Very fast heuristics (keyword OR Pokémon-name match) to decide whether a
   message is Pokémon-related.  If it is, the detected name (lower-case) is
   returned so the caller can fetch context.

2. get_pokemon_context(name: str) -> str
   Returns a concise, single-paragraph description of `name` assembled from the
   CSV fields, suitable for direct injection into the LLM prompt.

CSV columns expected (header names must match exactly):
    pokemon_id, name, primary_type, secondary_type, first_appearance,
    generation, category, total_base_stats, hp, attack, defense, special_attack,
    special_defense, speed
"""

from __future__ import annotations

import csv
import os
import re
from typing import Dict, Optional, Tuple

_DATA: Dict[str, Dict[str, str]] = {}
_DATA_LOADED = False
_NAME_REGEX: Optional[re.Pattern[str]] = None

_DATASET_PATH = os.path.join(os.path.dirname(__file__), "datasets", "pokemon_dataset.csv")

# Very general Pokémon-related words
_KEYWORD_RE = re.compile(r"\b(pok[eé]mon|pokedex|dex|evolution|type chart|types?)\b", re.I)

###############################################################################
# Internal helpers
###############################################################################

def _load_dataset() -> None:
    global _DATA_LOADED, _NAME_REGEX
    if _DATA_LOADED:
        return
    if not os.path.exists(_DATASET_PATH):
        raise FileNotFoundError(f"Pokémon dataset not found at {_DATASET_PATH}")

    with open(_DATASET_PATH, newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            name_key = row["name"].strip().lower()
            _DATA[name_key] = row

    # Build regex that matches any Pokémon name as a word boundary
    escaped_names = map(re.escape, _DATA.keys())
    _NAME_REGEX = re.compile(r"\b(" + "|".join(escaped_names) + r")\b", re.I)
    _DATA_LOADED = True


def _ensure_loaded():
    if not _DATA_LOADED:
        _load_dataset()

###############################################################################
# Public API
###############################################################################

def needs_pokemon_context(question: str) -> Tuple[bool, Optional[str]]:
    """Cheap check whether *question* is about a Pokémon.

    Returns (True, name) if context should be injected, else (False, None).
    """
    if not question:
        return False, None

    _ensure_loaded()

    # Keyword present?
    if _KEYWORD_RE.search(question):
        # Try to capture specific Pokémon referenced in question.
        name_match = _NAME_REGEX.search(question)
        if name_match:
            return True, name_match.group(1).lower()
        return True, None  # generic Poké question

    # No keyword – maybe direct name mention (“What type is Bulbasaur?”)
    name_match = _NAME_REGEX.search(question)
    if name_match:
        return True, name_match.group(1).lower()

    return False, None


def get_pokemon_context(name: str, question: str = "") -> str:
    """Return a concise context string for *name*.

    If *name* not found, returns an empty string.
    If question is about type effectiveness, provides type effectiveness info.
    """
    _ensure_loaded()

    row = _DATA.get(name.lower())
    if not row:
        return ""

    primary = row["primary_type"]
    secondary = row.get("secondary_type") or ""
    
    # Check if this is a type effectiveness question
    effectiveness_patterns = [
        "good against", "effective against", "strong against", "beats", "counters",
        "weak to", "vulnerable to", "bad against"
    ]
    
    is_effectiveness_question = any(pattern in question.lower() for pattern in effectiveness_patterns)
    
    if is_effectiveness_question:
        # Import type effectiveness function
        try:
            from rag_type_chart import get_type_effectiveness_context
            
            # Get type effectiveness for primary type
            primary_context = get_type_effectiveness_context(primary, question)
            
            # Get type effectiveness for secondary type if exists
            secondary_context = ""
            if secondary:
                secondary_context = get_type_effectiveness_context(secondary, question)
            
            # Combine information
            if secondary_context and secondary_context != primary_context:
                types_info = f"{primary.capitalize()}/{secondary.capitalize()}"
                desc = f"{row['name'].capitalize()} is a {types_info} type Pokémon. {primary_context}"
                if secondary_context:
                    desc += f" As a {secondary.capitalize()} type, {secondary_context.replace(secondary.capitalize(), 'it')}"
            else:
                type_info = f"{primary.capitalize()}{('/' + secondary.capitalize()) if secondary else ''}"
                desc = f"{row['name'].capitalize()} is a {type_info} type Pokémon. {primary_context}"
                
            return desc
            
        except ImportError:
            # Fallback if type effectiveness module not available
            pass
    
    # Default behavior - general Pokemon info
    types = f"{primary}/{secondary}".rstrip("/")
    desc = (
        f"{row['name']} is a Generation {row['generation']} Pokémon. "
        f"Category: {row['category']}. Types: {types}. "
        f"Base stats (total {row['total_base_stats']}): HP {row['hp']}, ATK {row['attack']}, DEF {row['defense']}, "
        f"SpA {row['special_attack']}, SpD {row['special_defense']}, SPD {row['speed']}."
    )

    return desc
