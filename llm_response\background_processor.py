"""
Luna Background Memory Processor
===============================

Advanced background processing system that handles memory operations
asynchronously without blocking Discord responses.

Features:
- Multi-threaded processing with priority queues
- Intelligent batching and consolidation
- Adaptive rate limiting based on system load
- Error recovery and retry mechanisms
- Performance monitoring and optimization
"""

import asyncio
import logging
import threading
import time
import json
from collections import deque, defaultdict
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
import heapq
import statistics

from .memory_system import MemoryFact, MemoryType
from .fact_extraction import FactExtractor, ExtractionContext
from .vector_storage import get_vector_storage
from .config import get_main_name_by_id

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    URGENT = 0      # DM responses, important user interactions
    HIGH = 1        # Voice conversations, active channels  
    NORMAL = 2      # Regular text conversations
    LOW = 3         # Background consolidation, cleanup
    MAINTENANCE = 4 # Optimization, indexing

@dataclass
class ProcessingTask:
    """A background processing task"""
    id: str
    priority: TaskPriority
    task_type: str
    data: Dict[str, Any]
    created_at: float
    attempts: int = 0
    max_attempts: int = 3
    
    def __lt__(self, other):
        """For priority queue ordering"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_at < other.created_at

class ProcessingStats:
    """Statistics tracking for the background processor"""
    
    def __init__(self):
        self.tasks_processed = 0
        self.tasks_failed = 0
        self.processing_times = deque(maxlen=1000)
        self.queue_sizes = deque(maxlen=100)
        self.memory_extractions = 0
        self.vector_additions = 0
        self.consolidations = 0
        self.start_time = time.time()
    
    def record_task(self, processing_time: float, success: bool):
        """Record a completed task"""
        if success:
            self.tasks_processed += 1
        else:
            self.tasks_failed += 1
        self.processing_times.append(processing_time)
    
    def record_queue_size(self, size: int):
        """Record current queue size"""
        self.queue_sizes.append(size)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        uptime = time.time() - self.start_time
        total_tasks = self.tasks_processed + self.tasks_failed
        
        return {
            'uptime_hours': uptime / 3600,
            'total_tasks': total_tasks,
            'success_rate': f"{(self.tasks_processed / max(1, total_tasks)) * 100:.1f}%",
            'avg_processing_time': f"{statistics.mean(self.processing_times) if self.processing_times else 0:.3f}s",
            'avg_queue_size': f"{statistics.mean(self.queue_sizes) if self.queue_sizes else 0:.1f}",
            'tasks_per_hour': int(total_tasks / max(0.001, uptime / 3600)),
            'memory_extractions': self.memory_extractions,
            'vector_additions': self.vector_additions,
            'consolidations': self.consolidations
        }

class AdaptiveRateLimiter:
    """Adaptive rate limiting based on system performance"""
    
    def __init__(self):
        self.max_concurrent = 3
        self.current_load = 0
        self.avg_processing_time = 0.1
        self.error_rate = 0.0
        self.last_adjustment = time.time()
        self._lock = threading.Lock()
    
    def should_process(self) -> bool:
        """Check if we should process another task"""
        with self._lock:
            return self.current_load < self.max_concurrent
    
    def acquire(self):
        """Acquire processing slot"""
        with self._lock:
            self.current_load += 1
    
    def release(self, processing_time: float, success: bool):
        """Release processing slot and update metrics"""
        with self._lock:
            self.current_load = max(0, self.current_load - 1)
            
            # Update metrics
            if self.avg_processing_time == 0:
                self.avg_processing_time = processing_time
            else:
                self.avg_processing_time = 0.9 * self.avg_processing_time + 0.1 * processing_time
            
            if not success:
                self.error_rate = 0.9 * self.error_rate + 0.1
            else:
                self.error_rate = 0.9 * self.error_rate
            
            # Adaptive adjustment
            self._adapt_limits()
    
    def _adapt_limits(self):
        """Adapt processing limits based on performance"""
        now = time.time()
        if now - self.last_adjustment < 30:  # Adjust every 30 seconds
            return
        
        self.last_adjustment = now
        
        # Increase concurrency if performing well
        if self.error_rate < 0.05 and self.avg_processing_time < 0.5:
            self.max_concurrent = min(8, self.max_concurrent + 1)
        # Decrease if struggling
        elif self.error_rate > 0.2 or self.avg_processing_time > 2.0:
            self.max_concurrent = max(1, self.max_concurrent - 1)
        
        logger.debug(f"Adapted rate limiter: max_concurrent={self.max_concurrent}, error_rate={self.error_rate:.3f}")

class TaskBatcher:
    """Batches similar tasks for efficient processing"""
    
    def __init__(self):
        self.batches: Dict[str, List[ProcessingTask]] = defaultdict(list)
        self.last_flush = time.time()
        self.batch_timeout = 5.0  # Flush batches every 5 seconds
        self._lock = threading.Lock()
    
    def add_task(self, task: ProcessingTask) -> bool:
        """Add task to batch, returns True if batch should be processed"""
        with self._lock:
            batch_key = f"{task.task_type}:{task.priority.value}"
            self.batches[batch_key].append(task)
            
            # Check if batch is ready
            batch_size = len(self.batches[batch_key])
            time_since_flush = time.time() - self.last_flush
            
            # Process if batch is full or timeout reached
            return batch_size >= 10 or time_since_flush >= self.batch_timeout
    
    def get_ready_batches(self) -> List[Tuple[str, List[ProcessingTask]]]:
        """Get batches ready for processing"""
        with self._lock:
            ready_batches = []
            now = time.time()
            
            for batch_key, tasks in list(self.batches.items()):
                if tasks and (len(tasks) >= 5 or now - self.last_flush >= self.batch_timeout):
                    ready_batches.append((batch_key, tasks.copy()))
                    self.batches[batch_key] = []
            
            if ready_batches:
                self.last_flush = now
            
            return ready_batches

class BackgroundProcessor:
    """Main background processing system"""
    
    def __init__(self):
        # Processing queues
        self.task_queue: List[ProcessingTask] = []
        self.processing_active = False
        
        # Components
        self.fact_extractor = FactExtractor()
        self.stats = ProcessingStats()
        self.rate_limiter = AdaptiveRateLimiter()
        self.batcher = TaskBatcher()
        
        # Consolidation tracking
        self.user_activity: Dict[int, deque] = defaultdict(lambda: deque(maxlen=50))
        self.last_consolidation: Dict[int, float] = {}
        
        # Thread safety
        self._queue_lock = threading.Lock()
        self._shutdown_event = threading.Event()
        
        # Background tasks
        self._processing_task: Optional[asyncio.Task] = None
        self._consolidation_task: Optional[asyncio.Task] = None
        self._maintenance_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """Start the background processor"""
        if self.processing_active:
            return
        
        self.processing_active = True
        logger.info("🚀 Starting background memory processor...")
        
        # Start processing tasks
        self._processing_task = asyncio.create_task(self._process_tasks())
        self._consolidation_task = asyncio.create_task(self._consolidation_worker())
        self._maintenance_task = asyncio.create_task(self._maintenance_worker())
        
        logger.info("✅ Background memory processor started")
    
    async def stop(self):
        """Stop the background processor"""
        if not self.processing_active:
            return
        
        logger.info("🛑 Stopping background memory processor...")
        self.processing_active = False
        self._shutdown_event.set()
        
        # Cancel tasks
        for task in [self._processing_task, self._consolidation_task, self._maintenance_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("✅ Background memory processor stopped")
    
    def queue_conversation_processing(self, messages: List[Dict[str, Any]], 
                                   user_id: int, channel_id: str, 
                                   channel_type: str = "text", 
                                   priority: TaskPriority = TaskPriority.NORMAL):
        """Queue conversation for memory processing"""
        task_id = f"conv_{user_id}_{int(time.time() * 1000)}"
        
        task = ProcessingTask(
            id=task_id,
            priority=priority,
            task_type="conversation_processing",
            data={
                "messages": messages,
                "user_id": user_id,
                "channel_id": channel_id,
                "channel_type": channel_type
            },
            created_at=time.time()
        )
        
        self._add_task(task)
        logger.debug(f"📝 Queued conversation processing for user {user_id}")
    
    def queue_fact_extraction(self, content: str, user_id: int, 
                            channel_id: str, context: Dict[str, Any] = None,
                            priority: TaskPriority = TaskPriority.HIGH):
        """Queue immediate fact extraction"""
        task_id = f"fact_{user_id}_{int(time.time() * 1000)}"
        
        task = ProcessingTask(
            id=task_id,
            priority=priority,
            task_type="fact_extraction",
            data={
                "content": content,
                "user_id": user_id,
                "channel_id": channel_id,
                "context": context or {}
            },
            created_at=time.time()
        )
        
        self._add_task(task)
        logger.debug(f"🧠 Queued fact extraction for user {user_id}")
    
    def queue_consolidation(self, user_id: int, 
                          priority: TaskPriority = TaskPriority.LOW):
        """Queue memory consolidation for a user"""
        # Rate limit consolidations
        last_consolidation = self.last_consolidation.get(user_id, 0)
        if time.time() - last_consolidation < 300:  # 5 minutes
            return
        
        task_id = f"consolidate_{user_id}_{int(time.time())}"
        
        task = ProcessingTask(
            id=task_id,
            priority=priority,
            task_type="consolidation",
            data={"user_id": user_id},
            created_at=time.time()
        )
        
        self._add_task(task)
        self.last_consolidation[user_id] = time.time()
        logger.debug(f"🔄 Queued consolidation for user {user_id}")
    
    def _add_task(self, task: ProcessingTask):
        """Add task to processing queue"""
        with self._queue_lock:
            heapq.heappush(self.task_queue, task)
            self.stats.record_queue_size(len(self.task_queue))
    
    async def _process_tasks(self):
        """Main task processing loop"""
        logger.info("🔄 Background task processor started")
        
        while self.processing_active and not self._shutdown_event.is_set():
            try:
                # Check rate limiting
                if not self.rate_limiter.should_process():
                    await asyncio.sleep(0.1)
                    continue
                
                # Get next task
                task = self._get_next_task()
                if not task:
                    await asyncio.sleep(0.5)
                    continue
                
                # Acquire processing slot
                self.rate_limiter.acquire()
                
                # Process task
                start_time = time.time()
                success = await self._process_single_task(task)
                processing_time = time.time() - start_time
                
                # Update metrics
                self.rate_limiter.release(processing_time, success)
                self.stats.record_task(processing_time, success)
                
                # Brief pause to prevent overwhelming
                await asyncio.sleep(0.01)
                
            except Exception as e:
                logger.error(f"Error in task processing loop: {e}", exc_info=True)
                await asyncio.sleep(1)
    
    def _get_next_task(self) -> Optional[ProcessingTask]:
        """Get the next highest priority task"""
        with self._queue_lock:
            if self.task_queue:
                return heapq.heappop(self.task_queue)
            return None
    
    async def _process_single_task(self, task: ProcessingTask) -> bool:
        """Process a single task"""
        try:
            logger.debug(f"Processing task {task.id} ({task.task_type})")
            
            if task.task_type == "conversation_processing":
                return await self._process_conversation(task)
            elif task.task_type == "fact_extraction":
                return await self._process_fact_extraction(task)
            elif task.task_type == "consolidation":
                return await self._process_consolidation(task)
            else:
                logger.warning(f"Unknown task type: {task.task_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing task {task.id}: {e}", exc_info=True)
            
            # Retry failed tasks
            task.attempts += 1
            if task.attempts < task.max_attempts:
                # Re-queue with lower priority
                task.priority = TaskPriority.LOW
                self._add_task(task)
                logger.debug(f"Re-queued failed task {task.id} (attempt {task.attempts})")
            
            return False
    
    async def _process_conversation(self, task: ProcessingTask) -> bool:
        """Process conversation for memory extraction"""
        data = task.data
        
        try:
            # Create extraction context
            context = ExtractionContext(
                user_id=data["user_id"],
                channel_id=data["channel_id"],
                channel_type=data["channel_type"],
                conversation_history=data["messages"][:-1],  # All but last message
                current_message=data["messages"][-1] if data["messages"] else {"content": "", "user_id": data["user_id"]}
            )
            
            # Extract facts
            facts = await self.fact_extractor.extract_facts_from_conversation(context)
            
            if facts:
                # Store facts in memory system
                from .memory_system import get_memory_system
                memory_system = get_memory_system()
                
                for fact in facts:
                    # Store in warm storage
                    if memory_system and memory_system.warm_storage:
                        memory_system.warm_storage.store_fact(fact)
                    
                    # Add to vector storage for similarity search
                    vector_storage = get_vector_storage()
                    if vector_storage:
                        await vector_storage.add_entry(
                            content=fact.content,
                            metadata={
                                "memory_type": fact.memory_type.value,
                                "user_id": fact.user_id,
                                "channel_id": fact.channel_id,
                                "confidence": fact.confidence,
                                "entities": fact.entities,
                                "relationships": fact.relationships
                            }
                        )
                        self.stats.vector_additions += 1
                    
                    # Update graph relationships
                    if memory_system and memory_system.memory_graph:
                        for entity in fact.entities:
                            memory_system.memory_graph.add_node(entity, {
                                "type": "entity",
                                "last_seen": time.time(),
                                "user_id": data["user_id"]
                            })
                        
                        for rel in fact.relationships:
                            memory_system.memory_graph.add_edge(rel[0], rel[2], rel[1], {
                                "confidence": fact.confidence,
                                "source": "conversation"
                            })
                
                self.stats.memory_extractions += 1
                logger.debug(f"Extracted {len(facts)} facts from conversation")
            
            # Track user activity for consolidation
            self.user_activity[data["user_id"]].append(time.time())
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing conversation: {e}", exc_info=True)
            return False
    
    async def _process_fact_extraction(self, task: ProcessingTask) -> bool:
        """Process immediate fact extraction"""
        data = task.data
        
        try:
            # Create simple extraction context
            context = ExtractionContext(
                user_id=data["user_id"],
                channel_id=data["channel_id"],
                channel_type="text",
                conversation_history=[],
                current_message={"content": data["content"], "user_id": data["user_id"]}
            )
            
            # Extract facts
            facts = await self.fact_extractor.extract_facts_from_conversation(context)
            
            # Store facts (same as conversation processing)
            # ... (similar code to _process_conversation)
            
            self.stats.memory_extractions += 1
            return True
            
        except Exception as e:
            logger.error(f"Error in fact extraction: {e}", exc_info=True)
            return False
    
    async def _process_consolidation(self, task: ProcessingTask) -> bool:
        """Process memory consolidation"""
        user_id = task.data["user_id"]
        
        try:
            # Get user's recent facts
            from .memory_system import get_memory_system
            memory_system = get_memory_system()
            
            if not memory_system or not memory_system.warm_storage:
                return False
            
            # Find similar or contradictory facts
            facts = memory_system.warm_storage.search_facts(user_id=user_id, limit=50)
            
            if len(facts) < 5:  # Not enough facts to consolidate
                return True
            
            # Group facts by content similarity
            # (Simplified consolidation - in production, would use more sophisticated logic)
            
            self.stats.consolidations += 1
            logger.debug(f"Consolidated memories for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error in consolidation: {e}", exc_info=True)
            return False
    
    async def _consolidation_worker(self):
        """Background worker for user memory consolidation"""
        while self.processing_active and not self._shutdown_event.is_set():
            try:
                # Check for users needing consolidation
                now = time.time()
                
                for user_id, activity in self.user_activity.items():
                    if len(activity) >= 10:  # User has been active
                        last_consolidation = self.last_consolidation.get(user_id, 0)
                        
                        if now - last_consolidation > 1800:  # 30 minutes
                            self.queue_consolidation(user_id, TaskPriority.LOW)
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in consolidation worker: {e}", exc_info=True)
                await asyncio.sleep(60)
    
    async def _maintenance_worker(self):
        """Background worker for system maintenance"""
        while self.processing_active and not self._shutdown_event.is_set():
            try:
                # Optimize vector storage
                vector_storage = get_vector_storage()
                if vector_storage:
                    await vector_storage.optimize()
                
                # Clean up expired cache entries
                from .fast_retrieval import get_fast_retrieval_system
                retrieval_system = get_fast_retrieval_system()
                retrieval_system.optimize_caches()
                
                logger.debug("🔧 Performed maintenance tasks")
                
                await asyncio.sleep(3600)  # Every hour
                
            except Exception as e:
                logger.error(f"Error in maintenance worker: {e}", exc_info=True)
                await asyncio.sleep(1800)
    
    def get_status(self) -> Dict[str, Any]:
        """Get processor status"""
        with self._queue_lock:
            queue_size = len(self.task_queue)
        
        return {
            "active": self.processing_active,
            "queue_size": queue_size,
            "current_load": self.rate_limiter.current_load,
            "max_concurrent": self.rate_limiter.max_concurrent,
            "error_rate": f"{self.rate_limiter.error_rate * 100:.1f}%",
            **self.stats.get_summary()
        }

# Global instance
_background_processor: Optional[BackgroundProcessor] = None

def get_background_processor() -> BackgroundProcessor:
    """Get the global background processor"""
    global _background_processor
    if _background_processor is None:
        _background_processor = BackgroundProcessor()
    return _background_processor

async def initialize_background_processor() -> bool:
    """Initialize and start the background processor"""
    try:
        processor = get_background_processor()
        await processor.start()
        logger.info("✅ Background processor initialized and started")
        return True
    except Exception as e:
        logger.error(f"❌ Error initializing background processor: {e}")
        return False

async def shutdown_background_processor():
    """Shutdown the background processor"""
    global _background_processor
    if _background_processor:
        await _background_processor.stop()
        _background_processor = None
        logger.info("✅ Background processor shutdown complete")