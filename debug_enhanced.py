#!/usr/bin/env python3
import asyncio
import time
import logging
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType

# Set up logging
logging.basicConfig(level=logging.DEBUG)

async def debug_enhanced():
    print("🔍 Debugging enhanced memory context...")
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Store a simple fact
    test_fact = MemoryFact(
        content="<PERSON> loves potatoes",
        memory_type=MemoryType.FACTUAL,
        user_id=12345,
        channel_id="test_channel",
        confidence=0.9,
        timestamp=time.time(),
        entities=["Luna", "potatoes"],
        relationships=[("Luna", "loves", "potatoes")]
    )
    
    fact_id = ms.warm_storage.store_fact(test_fact)
    print(f"✅ Stored fact ID: {fact_id}")
    
    # Rebuild FTS
    import sqlite3
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
    finally:
        conn.close()
    
    # Test the enhanced function manually
    from llm_response.processing import ULTRA_MEMORY_AVAILABLE
    print(f"ULTRA_MEMORY_AVAILABLE: {ULTRA_MEMORY_AVAILABLE}")
    
    query_text = "Tell me about potatoes"
    current_user_id = 12345
    
    print(f"\n🧪 Testing enhanced function step by step for '{query_text}':")
    
    # Step 1: Extract key terms
    import re
    key_terms = []
    words = re.findall(r'\b\w+\b', query_text.lower())
    stop_words = {'what', 'is', 'are', 'do', 'you', 'know', 'about', 'the', 'a', 'an', 'your', 'my', 'his', 'her', 'their', 'our', 'that', 'this', 'these', 'those', 'how', 'when', 'where', 'why', 'who', 'tell', 'me'}
    
    for word in words:
        if word not in stop_words and len(word) > 2:
            key_terms.append(word)
    
    search_query = ' '.join(key_terms[:3]) if key_terms else query_text
    print(f"  1. Key terms extracted: {key_terms} -> '{search_query}'")
    
    # Step 2: Direct memory system call
    enhanced_context = await ms.retrieve_relevant_context(
        query=search_query,
        user_id=current_user_id,
        channel_id=None,
        max_facts=8
    )
    print(f"  2. Direct memory result: '{enhanced_context}' (len: {len(enhanced_context) if enhanced_context else 0})")
    
    # Step 3: Test the actual enhanced function
    from llm_response.processing import enhanced_get_memory_context
    enhanced_result = await enhanced_get_memory_context(
        query_text=query_text,
        current_user_id=current_user_id
    )
    print(f"  3. Enhanced function result: '{enhanced_result}' (len: {len(enhanced_result) if enhanced_result else 0})")

if __name__ == "__main__":
    asyncio.run(debug_enhanced())