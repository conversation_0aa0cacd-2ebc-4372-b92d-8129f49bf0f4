import os
import logging
import asyncio
import base64
import google.generativeai as genai
from google.generativeai import GenerationConfig
from screenshot_util import screenshot_manager
import time
import pyautogui
from datetime import datetime
import tempfile
import re
import httpx # Add httpx for fetching URL
import mimetypes # Add mimetypes for guessing type

logger = logging.getLogger(__name__)

# Configure API key
api_key = os.getenv("GOOGLE_API_KEY")
if api_key:
    genai.configure(api_key=api_key)
else:
    logger.error("GOOGLE_API_KEY environment variable not set")

# Initialize multimodal model
try:
    vision_model = genai.GenerativeModel('gemini-2.0-flash')
    logger.info("Gemini Vision model initialized successfully")
except Exception as e:
    vision_model = None
    logger.error(f"Failed to initialize Gemini Vision model: {e}")

async def analyze_screenshot(screenshot_data, prompt=None):
    """
    Analyze a screenshot using Gemini's multimodal capabilities
    
    Args:
        screenshot_data: Dictionary containing screenshot information
        prompt: Optional specific analysis prompt
        
    Returns:
        Analysis text from Gemini
    """
    if not vision_model:
        return "I'm sorry, my vision capabilities aren't available right now. Please check if the Google API key is set correctly."
    
    if not screenshot_data.get("success", False):
        return f"I couldn't analyze the screenshot: {screenshot_data.get('error', 'Unknown error')}"
    
    try:
        # Prepare parts for the multimodal model
        parts = []
        
        # Add text prompt
        if prompt:
            instruction = f"""This is a screenshot of the user's screen. {prompt}
            
            Provide a brief analysis focusing on the most important elements only:
            1. The main application or content in focus
            2. Key information that would be most relevant to the user
            
            BE VERY CONCISE - limit your answer to 2-3 sentences maximum.
            Use conversational, casual language. Don't be overly descriptive.
            """
        else:
            instruction = """This is a screenshot of the user's screen. Provide a brief analysis.
            
            Focus ONLY on the most important elements:
            1. The main application or website in focus
            2. What the user is likely doing on the screen
            
            BE VERY CONCISE - limit your answer to 2-3 sentences maximum.
            Use conversational, casual language. Don't be overly descriptive.
            """
            
        parts.append({"text": instruction})
        
        # Add image data
        image_data = screenshot_data["bytes"]
        mime_type = screenshot_manager.get_mime_type(screenshot_data["filename"])
        
        parts.append({
            "inline_data": {
                "mime_type": mime_type,
                "data": base64.b64encode(image_data).decode('utf-8')
            }
        })
        
        # Call Gemini API with strict token limit for conciseness
        response = await asyncio.to_thread(
            vision_model.generate_content,
            parts,
            generation_config=GenerationConfig(
                temperature=0.2,
                max_output_tokens=150,  # Keep responses much shorter
                top_p=0.95
            )
        )
        
        return response.text
        
    except Exception as e:
        logger.error(f"Error analyzing screenshot with Gemini: {e}")
        return f"I encountered an issue analyzing your screenshot: {str(e)}"

async def take_and_analyze_screenshot(query=None, specific_instruction=""):
    """Take a screenshot and analyze it with Gemini Vision, focusing on specific query if provided"""
    result = {
        "success": False,
        "screenshot": None,
        "analysis": "",
        "error": None
    }
    
    try:
        # Generate timestamp and filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_dir = tempfile.gettempdir()
        filename = os.path.join(temp_dir, f"luna_screenshot_{timestamp}.png")
        
        # Take the screenshot
        logger.info(f"Taking screenshot and saving to {filename}")
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        
        # Add screenshot data to result
        result["screenshot"] = {
            "timestamp": timestamp,
            "filename": filename
        }
        
        # Read the image file
        with open(filename, "rb") as img_file:
            image_data = img_file.read()
            
        # Analyze with Gemini Vision
        logger.info("Analyzing screenshot with Gemini Vision")
        
        # If we have a specific query, prioritize it over general description
        if query:
            # Create a targeted prompt focused on answering the specific question
            prompt = f"""IMPORTANT: You are analyzing a screenshot of a computer screen. 
            
SPECIFIC SEARCH REQUEST: {query}

Your task:
1. FOCUS ONLY on finding and describing what was requested
2. IGNORE the filename, file path, or any metadata about the image itself
3. Provide a DIRECT answer about what the user asked to find
4. Be CONCISE (max 2-3 sentences)
5. If you can't find what was requested, clearly state so

DO NOT mention that this is a screenshot or image file - just analyze what's visible on the screen.
"""
        else:
            # General description prompt
            prompt = """IMPORTANT: You are analyzing a screenshot of a computer screen.

Your task:
1. Describe ONLY the main content visible on the screen
2. Focus on the active application, website, or document
3. IGNORE the filename, file path, or any metadata about the image itself
4. Be CONCISE (max 2-3 sentences)
5. Use a casual, conversational tone

DO NOT mention that this is a screenshot or image file - just analyze what's visible on the screen.
"""
        
        # Configure Gemini
        model = genai.GenerativeModel('gemini-2.0-flash')
        
        # Convert image to base64 for Gemini
        image_parts = {
            "mime_type": "image/png",
            "data": base64.b64encode(image_data).decode('utf-8')
        }
        
        # Create the content parts with prompt and image
        content_parts = [
            {"text": prompt},
            {"inline_data": image_parts}
        ]
        
        # Analyze the image with proper parts structure
        response = await asyncio.to_thread(
            model.generate_content,
            content_parts,
            generation_config=GenerationConfig(
                temperature=0.1,
                top_p=0.95,
                top_k=32,
                max_output_tokens=150,  # Keep responses concise
            )
        )
        
        # Extract and process the analysis
        analysis = response.text.strip()
        
        # Remove any meta-commentary about screenshots or files
        analysis = re.sub(r"(?i)(?:this is|the image shows|i can see|in this screenshot|looking at this|this appears to be) (?:a screenshot|an image|a picture) of", "", analysis)
        analysis = re.sub(r"(?i)the file(?:name)? (?:path|location) (?:is|shows)", "", analysis)
        analysis = re.sub(r"(?i)I (?:can )?see (?:the|a) file(?:name)?", "", analysis)
        
        # If dealing with a specific query, ensure the response is targeted
        if query:
            query_terms = [term for term in query.lower().split() if len(term) > 3]
            # Check if the analysis references the query terms
            if query_terms and not any(term in analysis.lower() for term in query_terms):
                # If not, explicitly reference the query in the response
                analysis = f"Regarding your search for '{query}': {analysis}"
        
        # Clean up the final analysis
        analysis = re.sub(r'\s+', ' ', analysis).strip()  # Remove extra whitespace
        if analysis.startswith(':'): analysis = analysis[1:].strip()  # Remove leading colons
        
        result["analysis"] = analysis
        result["success"] = True
        
        logger.info(f"Screenshot analysis: {analysis[:100]}...")
        return result
        
    except Exception as e:
        logger.error(f"Error in screenshot analysis: {e}")
        result["error"] = str(e)
        return result

# --- New Function to Analyze Image URL ---
async def analyze_image_url(image_url: str) -> str | None:
    """
    Fetches an image from a URL and analyzes it using Gemini Vision.

    Args:
        image_url: The URL of the image to analyze.

    Returns:
        The analysis text from Gemini, or None if analysis fails.
    """
    if not vision_model:
        logger.error("Vision model not initialized, cannot analyze image URL.")
        return None # Return None on model init failure

    logger.info(f"Attempting to analyze image from URL: {image_url}")
    analysis_text = None

    try:
        # Fetch the image data asynchronously
        async with httpx.AsyncClient() as client:
            response = await client.get(image_url, follow_redirects=True, timeout=15.0)
            response.raise_for_status() # Raise exception for bad status codes
            image_bytes = await response.aread()

        # Guess MIME type
        mime_type, _ = mimetypes.guess_type(image_url)
        if not mime_type or not mime_type.startswith("image/"):
            # Fallback based on content type header if guess fails
            content_type = response.headers.get("content-type")
            if content_type and content_type.startswith("image/"):
                mime_type = content_type
            else:
                # Default if still unknown, Gemini might handle it
                mime_type = "image/png"
                logger.warning(f"Could not determine reliable MIME type for {image_url}, defaulting to {mime_type}")

        logger.debug(f"Fetched image from {image_url}, size: {len(image_bytes)} bytes, MIME type: {mime_type}")

        # Prepare parts for Gemini
        prompt = """Analyze this image from a Discord chat. Provide a brief, concise description focusing on the main subject and any notable actions or elements. Use a neutral, objective tone. Max 2-3 sentences."""

        parts = [
            {"text": prompt},
            {
                "inline_data": {
                    "mime_type": mime_type,
                    "data": base64.b64encode(image_bytes).decode('utf-8')
                }
            }
        ]

        # Call Gemini API
        gemini_response = await asyncio.to_thread(
            vision_model.generate_content,
            parts,
            generation_config=GenerationConfig(
                temperature=0.2,
                max_output_tokens=150,
                top_p=0.95
            )
        )

        analysis_text = gemini_response.text.strip()
        # Basic cleaning
        analysis_text = re.sub(r'\s+', ' ', analysis_text).strip()
        logger.info(f"Image URL analysis result: {analysis_text[:100]}...")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error fetching image URL {image_url}: {e.response.status_code} - {e}")
    except httpx.RequestError as e:
        logger.error(f"Network error fetching image URL {image_url}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error analyzing image URL {image_url}: {e}", exc_info=True)

    return analysis_text # Returns analysis or None if error occurred