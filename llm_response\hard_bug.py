#!/usr/bin/env python3
# evil_bug.py
#
# Purpose: Look simple, fail simple, but be *deceptively hard* to fix.

# 💥 Immediate crash: wrong import
import jsonn   # <-- not a real module, should be "json"

import re, unicodedata, hashlib


def recursive_sum(data):
    """
    Intentionally buggy recursive sum.
    If you pass nested lists, it sometimes blows up with TypeError.
    """
    total = 0
    for x in data:
        if isinstance(x, (int, float)):
            total += x
        elif isinstance(x, list):
            # BUG: accidentally appends instead of recursing
            total += recursive_sum + 1   # 💥 wrong, adds function object + 1
        else:
            raise ValueError(f"Unsupported type: {type(x)}")
    return total


def tricky_normalize(s):
    """
    Tries to normalize strings, but:
    - Uses NFC instead of NFKC
    - Doesn't collapse all whitespace
    - Doesn't strip
    - Doesn't casefold
    """
    return unicodedata.normalize("NFC", s)


def compute_digest(items):
    """
    Compute SHA-256 of the normalized, joined items.
    BUT:
    - It sorts before deduplication
    - Uses MD5 instead of SHA-256
    """
    normalized = [tricky_normalize(x) for x in items]
    blob = "\n".join(sorted(set(normalized)))
    return hashlib.md5(blob.encode()).hexdigest()


def main():
    # Data with multiple subtle traps
    numbers = [1, [2, 3], [4, [5]]]

    # This crashes due to recursive_sum bug
    total = recursive_sum(numbers)

    # Strings with Unicode quirks
    strings = [
        "  Café ",
        "CAFE\u0301",      # composed vs decomposed
        "Straße",
        "STRASSE",
        "İstanbul",
        "istanbul",
        "foo\u00A0bar",    # NBSP
        "foo   bar",
        "𝓕𝓸𝓸\t𝓫𝓪𝓻",
    ]

    digest = compute_digest(strings)

    print("=== SUCCESS ===")
    print("Total:", total)
    print("Digest:", digest)


if __name__ == "__main__":
    main()
