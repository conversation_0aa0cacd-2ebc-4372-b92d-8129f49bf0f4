#!/usr/bin/env python3
import asyncio
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType
from llm_response.processing import enhanced_get_memory_context, process_conversation_memory

async def test_conversation_memory():
    print("🗣️ Testing conversation memory integration...")
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Add some conversation facts like <PERSON>'s interactions
    test_facts = [
        MemoryFact(
            content="<PERSON> loves potatoes and considers them the best food",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,  # <PERSON>'s user ID
            channel_id="test_channel",
            confidence=0.9,
            timestamp=time.time(),
            entities=["Luna", "potatoes"],
            relationships=[("Luna", "loves", "potatoes")]
        ),
        MemoryFact(
            content="<PERSON> is testing the memory system upgrades",
            memory_type=MemoryType.EPISODIC,
            user_id=12345,
            channel_id="test_channel",
            confidence=0.8,
            timestamp=time.time(),
            entities=["<PERSON>", "memory system"],
            relationships=[("<PERSON>", "is testing", "memory system")]
        ),
        MemoryFact(
            content="User's favorite color is blue",
            memory_type=MemoryType.FACTUAL,
            user_id=12345,
            channel_id="test_channel",
            confidence=0.7,
            timestamp=time.time(),
            entities=["user", "blue", "color"],
            relationships=[("user", "prefers", "blue")]
        )
    ]
    
    # Store facts
    for fact in test_facts:
        ms.warm_storage.store_fact(fact)
    print(f"✅ Stored {len(test_facts)} conversation facts")
    
    # Rebuild FTS
    import sqlite3
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        conn.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
        conn.commit()
    finally:
        conn.close()
    
    # Test conversation scenarios
    test_cases = [
        "Hi Luna",
        "I'm testing your memory system upgrades", 
        "What's your favorite color?",
        "Do you remember what you like to eat?",
        "Tell me about potatoes"
    ]
    
    print("\n🧪 Testing memory retrieval for different queries:")
    for query in test_cases:
        print(f"\n  Query: '{query}'")
        
        # Simulate what happens in processing.py
        memory_start = time.time()
        context = await enhanced_get_memory_context(
            query_text=query,
            current_user_id=12345,
            current_channel_id=None,
            is_dm_channel=False
        )
        memory_time = (time.time() - memory_start) * 1000
        
        if context and context.strip():
            print(f"    ✅ Memory context ({memory_time:.2f}ms): {context[:100]}...")
        else:
            print(f"    ❌ No memory context found ({memory_time:.2f}ms)")
    
    # Test background memory processing
    print(f"\n🔄 Testing background memory processing...")
    conversation_messages = [
        {"role": "user", "content": "Gavin: Hi Luna", "user_id": 12345},
        {"role": "assistant", "content": "Hey. What's up?", "user_id": 67890},
        {"role": "user", "content": "Gavin: I'm testing your memory system upgrades", "user_id": 12345},
        {"role": "assistant", "content": "Oh, *that*. Let's see… you just said you were testing my memory. Is that it?", "user_id": 67890}
    ]
    
    await process_conversation_memory(
        messages=conversation_messages,
        user_id=12345,
        channel_id="test_channel",
        channel_type="text"
    )
    print("✅ Conversation queued for background processing")
    
    # Give background processor time to work
    await asyncio.sleep(1.0)
    
    print("\n📊 Final memory system status:")
    print(f"  Retrieval times: {list(ms.metrics.get('retrieval_time', []))[-5:]}ms")
    print(f"  Extraction times: {len(ms.metrics.get('extraction_time', []))} extractions")

if __name__ == "__main__":
    asyncio.run(test_conversation_memory())