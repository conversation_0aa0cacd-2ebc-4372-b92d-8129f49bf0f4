#!/usr/bin/env python3
"""
Ollama to GGUF Converter Script

This script converts Ollama models to GGUF format for use with llama.cpp.
It automatically handles model discovery, blob extraction, and file management.

Usage:
    python convert_ollama_to_gguf.py luna-tune:latest
    python convert_ollama_to_gguf.py llama3.1:8b --output custom-name.gguf
    python convert_ollama_to_gguf.py --list  # Show available models
"""

import os
import sys
import json
import shutil
import argparse
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any


class OllamaToGGUFConverter:
    def __init__(self):
        self.models_dir = Path("./models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Determine Ollama storage location
        if os.name == 'nt':  # Windows
            self.ollama_base = Path(os.environ.get('OLLAMA_MODELS', 
                                  os.path.expanduser('~/.ollama/models')))
        else:  # Linux/macOS
            self.ollama_base = Path(os.environ.get('OLLAMA_MODELS', 
                                  os.path.expanduser('~/.ollama/models')))
    
    def run_ollama_command(self, args: list) -> str:
        """Run an ollama command and return the output."""
        try:
            result = subprocess.run(['ollama'] + args, 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"❌ Error running ollama command: {e}")
            print(f"Error output: {e.stderr}")
            sys.exit(1)
        except FileNotFoundError:
            print("❌ Error: Ollama not found. Please install Ollama first.")
            sys.exit(1)
    
    def list_models(self) -> None:
        """List all available Ollama models."""
        print("📋 Available Ollama models:")
        output = self.run_ollama_command(['list'])
        print(output)
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get detailed information about a model."""
        print(f"🔍 Getting information for model: {model_name}")
        
        # First check if model exists
        try:
            self.run_ollama_command(['show', model_name])
        except:
            print(f"❌ Model '{model_name}' not found.")
            print("Available models:")
            self.list_models()
            sys.exit(1)
        
        # Get verbose model info
        verbose_output = self.run_ollama_command(['show', model_name, '--verbose'])
        
        # Parse the model info to find the model file
        lines = verbose_output.split('\n')
        for line in lines:
            if 'Model file' in line or 'modelfile' in line.lower():
                continue
            if line.strip().startswith('FROM '):
                print(f"📄 Model base: {line.strip()}")
        
        return {"name": model_name, "info": verbose_output}
    
    def find_model_manifest(self, model_name: str) -> Path:
        """Find the model's manifest file."""
        # Parse model name and tag
        if ':' in model_name:
            name, tag = model_name.split(':', 1)
        else:
            name, tag = model_name, 'latest'
        
        # Look for manifest file
        manifest_paths = [
            self.ollama_base / "manifests" / "registry.ollama.ai" / "library" / name / tag,
            self.ollama_base / "manifests" / "registry.ollama.ai" / "library" / model_name / "latest",
            self.ollama_base / "manifests" / "registry.ollama.ai" / name / tag,
        ]
        
        for manifest_path in manifest_paths:
            if manifest_path.exists():
                print(f"📄 Found manifest: {manifest_path}")
                return manifest_path
        
        # If not found, try to find any manifest with the model name
        manifests_dir = self.ollama_base / "manifests"
        if manifests_dir.exists():
            for root, dirs, files in os.walk(manifests_dir):
                if name in root and (tag in files or 'latest' in files):
                    target_file = tag if tag in files else 'latest'
                    found_path = Path(root) / target_file
                    print(f"📄 Found manifest: {found_path}")
                    return found_path
        
        raise FileNotFoundError(f"Could not find manifest for model '{model_name}'")
    
    def extract_blob_hash(self, manifest_path: Path) -> str:
        """Extract the model blob hash from manifest."""
        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            # Look for the model layer (largest blob)
            model_layer = None
            max_size = 0
            
            for layer in manifest.get('layers', []):
                # Look for model layer
                if layer.get('mediaType') == 'application/vnd.ollama.image.model':
                    model_layer = layer
                    break
                # Fallback: find largest layer
                elif layer.get('size', 0) > max_size:
                    max_size = layer.get('size', 0)
                    model_layer = layer
            
            if not model_layer:
                raise ValueError("Could not find model layer in manifest")
            
            blob_hash = model_layer['digest']
            print(f"🔍 Found model blob: {blob_hash}")
            print(f"📊 Model size: {model_layer.get('size', 0) / (1024**3):.2f} GB")
            
            return blob_hash
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in manifest: {e}")
        except KeyError as e:
            raise ValueError(f"Missing key in manifest: {e}")
    
    def copy_model_blob(self, blob_hash: str, output_path: Path) -> None:
        """Copy the model blob to the output location."""
        # Convert digest format for filesystem
        blob_filename = blob_hash.replace('sha256:', 'sha256-')
        blob_path = self.ollama_base / "blobs" / blob_filename
        
        if not blob_path.exists():
            raise FileNotFoundError(f"Blob file not found: {blob_path}")
        
        print(f"📦 Copying model from: {blob_path}")
        print(f"📁 To: {output_path}")
        
        # Copy with progress indication for large files
        shutil.copy2(blob_path, output_path)
        
        # Verify the copy
        if output_path.exists():
            size_gb = output_path.stat().st_size / (1024**3)
            print(f"✅ Successfully copied model ({size_gb:.2f} GB)")
        else:
            raise RuntimeError("Failed to copy model file")
    
    def convert_model(self, model_name: str, output_name: Optional[str] = None) -> None:
        """Convert an Ollama model to GGUF format."""
        print(f"🚀 Converting Ollama model '{model_name}' to GGUF format")
        
        # Generate output filename if not provided
        if output_name is None:
            clean_name = model_name.replace(':', '-').replace('/', '-')
            output_name = f"{clean_name}.gguf"
        
        output_path = self.models_dir / output_name
        
        # Check if output file already exists
        if output_path.exists():
            response = input(f"⚠️  File '{output_path}' already exists. Overwrite? (y/N): ")
            if response.lower() != 'y':
                print("❌ Conversion cancelled.")
                return
        
        try:
            # Step 1: Get model info
            model_info = self.get_model_info(model_name)
            
            # Step 2: Find manifest
            manifest_path = self.find_model_manifest(model_name)
            
            # Step 3: Extract blob hash
            blob_hash = self.extract_blob_hash(manifest_path)
            
            # Step 4: Copy model blob
            self.copy_model_blob(blob_hash, output_path)
            
            print(f"🎉 Model conversion completed successfully!")
            print(f"📁 Output file: {output_path}")
            print(f"💡 You can now use this model with llama.cpp or llama-cpp-python")
            
        except Exception as e:
            print(f"❌ Error during conversion: {e}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Convert Ollama models to GGUF format for llama.cpp",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python convert_ollama_to_gguf.py luna-tune:latest
  python convert_ollama_to_gguf.py llama3.1:8b --output my-model.gguf
  python convert_ollama_to_gguf.py --list
        """
    )
    
    parser.add_argument('model_name', nargs='?', 
                       help='Name of the Ollama model to convert (e.g., luna-tune:latest)')
    parser.add_argument('--output', '-o', 
                       help='Output filename for the GGUF file')
    parser.add_argument('--list', '-l', action='store_true',
                       help='List available Ollama models')
    
    args = parser.parse_args()
    
    converter = OllamaToGGUFConverter()
    
    if args.list:
        converter.list_models()
        return
    
    if not args.model_name:
        print("❌ Error: Please provide a model name or use --list to see available models")
        parser.print_help()
        sys.exit(1)
    
    converter.convert_model(args.model_name, args.output)


if __name__ == "__main__":
    main() 