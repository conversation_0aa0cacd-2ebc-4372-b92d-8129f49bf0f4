import asyncio
import io
import logging
import time
import numpy as np
from pydub import AudioSegment
from faster_whisper import WhisperModel
import torch  # <--- IMPORT TORCH

# Import latency tracking
try:
    from llm_response.latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracker not available
    def mark_latency_timestamp(event, timestamp=None):
        pass

# Initialize Whisper model with optimized parameters
# Changed from "medium.en" to "small.en" for better speed/accuracy balance
# Use int8 quantization for faster inference with minimal quality loss
WHISPER_MODEL = WhisperModel("large-v3-turbo", device="cuda", compute_type="int8")
logger = logging.getLogger(__name__)

# Audio filtering configuration
# Minimum dB level for audio to be processed (anything below this is considered background noise)
MIN_DB_THRESHOLD = -50.0  # Configurable threshold in dB
# Reference level for dB calculation (maximum possible amplitude for 16-bit audio)
DB_REFERENCE_LEVEL = 32768.0

def _calculate_audio_db_level(audio_samples):
    """
    Calculate the dB level of audio samples.

    Args:
        audio_samples: NumPy array of normalized float32 audio samples [-1.0, 1.0]

    Returns:
        float: dB level of the audio, or -inf if audio is silent
    """
    if audio_samples.size == 0:
        return float('-inf')

    # Calculate RMS (Root Mean Square) of the audio samples
    rms = np.sqrt(np.mean(audio_samples ** 2))

    # Avoid log(0) by setting a minimum RMS value
    if rms <= 0:
        return float('-inf')

    # Convert RMS to dB relative to full scale (1.0 = 0 dB)
    # 20 * log10(rms / reference) where reference = 1.0 for normalized audio
    db_level = 20 * np.log10(rms)

    return db_level

def set_db_threshold(threshold_db):
    """
    Set the minimum dB threshold for audio processing.

    Args:
        threshold_db (float): Minimum dB level for audio to be processed
    """
    global MIN_DB_THRESHOLD
    MIN_DB_THRESHOLD = threshold_db
    logger.info(f"Audio dB threshold set to {threshold_db} dB")

def get_db_threshold():
    """
    Get the current minimum dB threshold for audio processing.

    Returns:
        float: Current minimum dB threshold
    """
    return MIN_DB_THRESHOLD

def _run_transcription_sync(samples):
    """Synchronous wrapper with more granular timing and list conversion."""
    sync_total_start_time = time.monotonic()
    logger.debug("SYNC WRAPPER: Starting...")
    segments_iterator = None # Renamed to clarify it's an iterator first
    info = None
    segments_list = [] # Initialize as empty list
    transcribe_call_duration = -1.0
    list_conv_duration = -1.0
    sync_call_duration = -1.0

    try:
        # --- Actual Transcription Call ---
        transcribe_call_start = time.monotonic()
        segments_iterator, info = WHISPER_MODEL.transcribe( # Get the iterator
            samples,
            # Change beam_size to 1 to reduce repetitions
            beam_size=2,
            # Increase temperature slightly for more diversity
            temperature=0.1,
            word_timestamps=False,
            # Apply repetition suppression
            suppress_blank=True,
            suppress_tokens=[-1],
            # Keep these settings
            vad_filter=True,
            # vad_parameters={"speech_prob_threshold": 0.6}, # REMOVED - Rely on defaults for now to fix TypeError
            # logprob_threshold=-0.8, # REMOVED - Invalid argument for faster-whisper
            language="en",
            task="transcribe",
            condition_on_previous_text=False,
        )
        transcribe_call_end = time.monotonic()
        transcribe_call_duration = transcribe_call_end - transcribe_call_start
        logger.debug(f"SYNC WRAPPER: WHISPER_MODEL.transcribe call took: {transcribe_call_duration:.4f}s")
        # -------------------------------

        # --- Convert iterator to list ---
        list_conv_start = time.monotonic()
        segments_list = list(segments_iterator) # Consume the iterator into a list
        list_conv_end = time.monotonic()
        list_conv_duration = list_conv_end - list_conv_start
        logger.debug(f"SYNC WRAPPER: list(segments_iterator) took: {list_conv_duration:.4f}s")
        # -----------------------------

        # --- GPU Synchronization Call ---
        sync_call_start = time.monotonic()
        torch.cuda.synchronize() # Sync after transcription and list conversion
        sync_call_end = time.monotonic()
        sync_call_duration = sync_call_end - sync_call_start
        logger.debug(f"SYNC WRAPPER: torch.cuda.synchronize() call took: {sync_call_duration:.4f}s")
        # -----------------------------

        # Measure total time from start of transcribe() to end of synchronize()
        sync_total_end_time = time.monotonic()
        # Now includes transcribe + list conversion + sync
        total_sync_op_duration = sync_total_end_time - transcribe_call_start

        logger.debug(f"SYNC WRAPPER: Finished. Total Op Duration (transcribe+list+sync): {total_sync_op_duration:.4f}s")
        # Return the LIST, not the iterator
        return segments_list, info, total_sync_op_duration

    except Exception as e:
        # Try to sync even on error if possible
        try:
            torch.cuda.synchronize()
        except Exception as sync_e:
            logger.error(f"SYNC WRAPPER: Error during GPU sync after exception: {sync_e}")
        sync_total_end_time = time.monotonic()
        total_sync_op_duration = sync_total_end_time - sync_total_start_time # Fallback timing
        logger.error(f"SYNC WRAPPER: Error after {total_sync_op_duration:.4f}s: {e}", exc_info=True)
        # Attempt to return whatever might have been collected, or empty list
        return segments_list, info, total_sync_op_duration # Return list even on error

def _preprocess_audio_numpy(audio_data, user_id=None):
    """Preprocesses raw audio bytes directly using NumPy for lower latency."""
    # Assuming input is 48kHz, 16-bit, stereo PCM (like Discord)
    if not audio_data:
        return np.array([], dtype=np.float32)

    try:
        # 1. Interpret raw bytes as 16-bit integers
        raw_samples = np.frombuffer(audio_data, dtype=np.int16)

        # 2. Reshape for stereo and convert to float32, normalize
        num_channels = 2
        if raw_samples.size % num_channels != 0:
            # Handle potential partial frames
            cutoff = (raw_samples.size // num_channels) * num_channels
            raw_samples = raw_samples[:cutoff]
            logger.warning(f"Audio data size not multiple of channels, trimmed to {cutoff} samples")

        if raw_samples.size == 0:
            return np.array([], dtype=np.float32)

        float_samples = raw_samples.astype(np.float32) / 32768.0  # Normalize to [-1.0, 1.0]
        reshaped_samples = float_samples.reshape(-1, num_channels)

        # 3. Convert to mono (average channels)
        mono_samples = reshaped_samples.mean(axis=1)

        # 4. Apply per-user volume adjustment if user_id is provided
        if user_id is not None:
            try:
                from llm_response.config import get_volume_multiplier_by_id, get_main_name_by_id
                volume_multiplier = get_volume_multiplier_by_id(user_id)

                if volume_multiplier != 1.0:
                    # Apply volume adjustment with clipping to prevent distortion
                    mono_samples = mono_samples * volume_multiplier
                    # Clip to prevent values outside [-1.0, 1.0] range
                    mono_samples = np.clip(mono_samples, -1.0, 1.0)

                    user_name = get_main_name_by_id(user_id) or f"User {user_id}"
                    logger.debug(f"🔊 Applied {volume_multiplier:.1f}x volume adjustment for {user_name}")
            except ImportError:
                logger.warning("Could not import volume configuration functions")
            except Exception as e:
                logger.error(f"Error applying volume adjustment for user {user_id}: {e}")

        # 5. Check audio level before resampling to save processing time
        audio_db_level = _calculate_audio_db_level(mono_samples)
        audio_duration_ms = len(mono_samples) / 48000 * 1000  # Calculate duration in milliseconds

        # Filter out low-level background noise
        if audio_db_level < MIN_DB_THRESHOLD:
            logger.info(f"🔇 Audio filtered out: {audio_db_level:.1f} dB < {MIN_DB_THRESHOLD} dB threshold ({audio_duration_ms:.0f}ms)")
            return np.array([], dtype=np.float32)

        logger.debug(f"🔊 Audio passed dB filter: {audio_db_level:.1f} dB >= {MIN_DB_THRESHOLD} dB threshold ({audio_duration_ms:.0f}ms)")

        # 5. Resample from 48kHz to 16kHz using numpy interpolation
        current_len = len(mono_samples)
        target_len = int(current_len * 16000 / 48000)
        resampled_samples = np.interp(
            np.linspace(0.0, current_len - 1, target_len),  # Target indices
            np.arange(current_len),                        # Source indices
            mono_samples                                   # Source values
        ).astype(np.float32)

        return resampled_samples

    except Exception as e:
        logger.error(f"NumPy preprocessing failed: {e}", exc_info=True)
        return np.array([], dtype=np.float32)

async def timed_await(coro, operation_name="operation"):
    """Helper function to time async operations accurately."""
    start = time.monotonic()
    logger.debug(f"TIMED AWAIT: Starting {operation_name}")
    result = await coro
    end = time.monotonic()
    duration = end - start
    logger.debug(f"TIMED AWAIT: {operation_name} completed in {duration:.4f}s")
    return result, duration

async def _transcribe_single(audio_data, user_id=None):
    """Handles transcription using a sync wrapper for accurate timing."""
    total_start_time = time.monotonic()
    preproc_result = None
    transcribe_list_result = None
    result_text = ""
    actual_transcribe_duration = -1.0

    try:
        # --- Preprocessing ---
        preproc_start_time = time.monotonic()
        samples, preproc_await_time = await timed_await(
            asyncio.to_thread(_preprocess_audio_numpy, audio_data, user_id),
            "preprocessing await"
        )
        preproc_end_time = time.monotonic()
        preproc_duration = preproc_end_time - preproc_start_time
        preproc_result = samples

        if samples.size == 0:
            logger.warning("Preprocessing resulted in empty audio samples.")
            return ""

        # --- Transcription (using sync wrapper) ---
        precise_await_call_start = time.monotonic()
        logger.debug(f"PRECISE AWAIT START: {precise_await_call_start:.6f}")

        (segments_list, info, actual_transcribe_duration), transcribe_await_time = await timed_await(
            asyncio.to_thread(_run_transcription_sync, preproc_result),
            "transcription thread await"
        )

        precise_await_call_end = time.monotonic()
        logger.debug(f"PRECISE AWAIT END: {precise_await_call_end:.6f}")
        precise_await_measured_duration = precise_await_call_end - precise_await_call_start
        # Removed MEASURED AWAIT DURATION log

        transcribe_list_result = (segments_list, info)

        # Removed SYNC DURATION log
        # Removed HELPER AWAIT DURATION log

        # --- Postprocessing ---
        postproc_total_start_time = time.monotonic() # Timer for the whole postproc block
        wait_for_postproc_duration = postproc_total_start_time - precise_await_call_end
        # Removed WAIT before Postprocessing log

        logger.debug(f"Starting postprocessing at {postproc_total_start_time:.6f}")
        segments_list_res, info_res = transcribe_list_result
        logger.debug(f"Postprocessing {len(segments_list_res)} segments...")

        # --- Step 1: Attribute Access and Filtering ---
        attr_access_start_time = time.monotonic()
        filtered_texts = [seg.text for seg in segments_list_res if seg.no_speech_prob < 0.8]
        attr_access_end_time = time.monotonic()
        attr_access_duration = attr_access_end_time - attr_access_start_time
        # Removed Postproc Step 1 log
        # --- End Step 1 ---

        # --- Step 2: String Join and Repetition Cleanup ---
        join_start_time = time.monotonic()
        result_text = " ".join(filtered_texts).strip()

        # Clean up excessive repetitions
        if result_text:
            # Check for unusual repetition patterns
            words = result_text.split()
            if len(words) > 5:
                # Count word frequencies
                word_counts = {}
                for word in words:
                    word_lower = word.lower()
                    word_counts[word_lower] = word_counts.get(word_lower, 0) + 1

                # Calculate repetition rate
                unique_words = len(word_counts)
                repetition_rate = unique_words / len(words)

                # If high repetition detected (less than 40% unique words)
                if repetition_rate < 0.4:
                    logger.warning(f"Detected high repetition rate ({repetition_rate:.2f}): {result_text[:50]}...")

                    # Build cleaned text with limited repetitions
                    cleaned_words = []
                    prev_word = None
                    repeat_count = 0
                    max_repeats = 2  # Allow at most 2 repetitions of the same word

                    for word in words:
                        word_lower = word.lower()
                        if word_lower == prev_word:
                            repeat_count += 1
                            if repeat_count <= max_repeats:
                                cleaned_words.append(word)
                        else:
                            repeat_count = 0
                            cleaned_words.append(word)
                            prev_word = word_lower

                    # Update result text
                    original_len = len(words)
                    cleaned_len = len(cleaned_words)
                    if cleaned_len < original_len:
                        result_text = " ".join(cleaned_words)
                        logger.info(f"Cleaned up repetitions: {original_len} words → {cleaned_len} words")

        join_end_time = time.monotonic()
        join_duration = join_end_time - join_start_time
        # Removed Postproc Step 2 log
        # --- End Step 2 ---

        postproc_total_end_time = time.monotonic() # End timer for the whole block
        logger.debug(f"Finished postprocessing at {postproc_total_end_time:.6f}")
        # Keep the original actual_postproc_duration for the breakdown log consistency
        actual_postproc_duration = postproc_total_end_time - postproc_total_start_time
        # Removed ACTUAL Postprocessing Duration log

        # --- Final Logging ---
        total_end_time = time.monotonic()
        total_duration = total_end_time - total_start_time
        audio_kb = len(audio_data) / 1024.0

        transcribe_duration_to_log = actual_transcribe_duration if actual_transcribe_duration >= 0 else 0

        measured_preproc_duration = preproc_end_time - preproc_start_time
        measured_transcribe_await_duration = precise_await_measured_duration
        measured_postproc_duration = actual_postproc_duration # Use the total postproc time here

        preproc_perc = (measured_preproc_duration / total_duration * 100) if total_duration > 0 else 0
        transcribe_perc = (measured_transcribe_await_duration / total_duration * 100) if total_duration > 0 else 0
        postproc_perc = (measured_postproc_duration / total_duration * 100) if total_duration > 0 else 0
        wait_perc = (wait_for_postproc_duration / total_duration * 100) if total_duration > 0 else 0

        # Removed detailed TIMING BREAKDOWN log
        # logger.info(
        #     f"⏱️ TIMING BREAKDOWN ({audio_kb:.1f}KB): Total={total_duration:.4f}s | "
        #     f"Preproc={measured_preproc_duration:.4f}s ({preproc_perc:.1f}%) | "
        #     f"TranscribeAwait={measured_transcribe_await_duration:.4f}s ({transcribe_perc:.1f}%) | "
        #     f"Wait={wait_for_postproc_duration:.4f}s ({wait_perc:.1f}%) | "
        #     f"Postproc={measured_postproc_duration:.4f}s ({postproc_perc:.1f}%)"
        # )

        # --- Filter specific common hallucinations ---
        from llm_response.config import HALLUCINATION_FILTER_PHRASES
        cleaned_check_text = result_text.strip().lower()
        if cleaned_check_text in HALLUCINATION_FILTER_PHRASES:
            logger.info(f"Filtered common hallucination: '{result_text}'")
            return "" # Return empty string if it matches
        # --- End Filter ---

        if result_text:
            word_count = len(result_text.split())
            chars_per_second = len(result_text) / total_duration if total_duration > 0 else 0
            words_per_second = word_count / total_duration if total_duration > 0 else 0
            logger.info(
                f"🔤 Transcribed {word_count} words ({len(result_text)} chars) "
                f"at {chars_per_second:.1f} chars/sec, {words_per_second:.1f} words/sec"
            )

        # Mark transcription end for latency tracking
        mark_latency_timestamp("transcription_end")
        
        return result_text

    except Exception as e:
        logger.error(f"Single transcription failed: {str(e)}", exc_info=True)
        total_end_time = time.monotonic()
        total_duration = total_end_time - total_start_time
        logger.error(f"⏱️ FAILED TIMING ({len(audio_data) / 1024.0:.1f}KB): Total={total_duration:.4f}s")
        return ""

async def transcribe_audio(audio_data, temperature=0.2, user_id=None):
    """Process audio immediately for lowest possible latency."""
    return await _transcribe_single(audio_data, user_id)