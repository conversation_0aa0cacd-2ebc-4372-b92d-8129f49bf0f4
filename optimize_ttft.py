#!/usr/bin/env python3
"""
TTFT (Time-to-First-Token) Optimization Script for Luna
======================================================

This script helps optimize <PERSON>'s response speed by:
1. Testing different context sizes
2. Benchmarking model performance
3. Providing optimization recommendations

Usage: python optimize_ttft.py
"""

import time
import asyncio
import logging
import os
import sys

# Add the current directory to the path so we can import llm_response
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llm_response.initialization import initialize_llama_cpp, get_llama_cpp_client
from llm_response.config import OLLAMA_MODEL_NAME
from llm_response.llama_cpp_adapter import format_messages_for_llama_cpp, extract_response_from_llama_cpp_result

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TTFTOptimizer:
    def __init__(self):
        self.llama_cpp_client = None
        self.model_name = OLLAMA_MODEL_NAME
        
    async def initialize(self):
        """Initialize the llama.cpp client"""
        self.llama_cpp_client = initialize_llama_cpp()
        if self.llama_cpp_client is None:
            raise RuntimeError("Failed to initialize llama.cpp client")
        
    async def test_context_size_impact(self):
        """Test how context size affects TTFT"""
        print("\n🧪 Testing Context Size Impact on TTFT")
        print("=" * 50)
        
        base_message = "Hello Luna, how are you today?"
        context_sizes = [
            ("Minimal", 100),
            ("Small", 500), 
            ("Medium", 1500),
            ("Large", 3000),
            ("Very Large", 5000),
            ("Extreme", 8000)
        ]
        
        results = []
        
        for size_name, char_count in context_sizes:
            # Create context of specified size
            padding = "This is additional context. " * (char_count // 30)
            test_message = padding[:char_count] + "\n\nUser: " + base_message
            
            try:
                start_time = time.monotonic()
                
                # Format the message for llama.cpp
                messages = [{"content": test_message}]
                prompt = format_messages_for_llama_cpp(messages, include_system_prompt=True)
                
                # Run inference in thread pool
                loop = asyncio.get_event_loop()
                
                def run_inference():
                    return self.llama_cpp_client(
                        prompt,
                        max_tokens=50,
                        stream=True
                    )
                
                response = await loop.run_in_executor(None, run_inference)
                
                # Get first token
                first_token_time = None
                for chunk in response:
                    token = extract_response_from_llama_cpp_result(chunk)
                    if token:
                        first_token_time = time.monotonic()
                        break
                
                ttft = (first_token_time - start_time) * 1000 if first_token_time else None
                results.append((size_name, char_count, ttft))
                
                print(f"{size_name:12} ({char_count:4d} chars): {ttft:.0f}ms TTFT")
                
            except Exception as e:
                print(f"{size_name:12} ({char_count:4d} chars): ERROR - {e}")
                results.append((size_name, char_count, None))
            
            # Wait between tests
            await asyncio.sleep(1)
        
        # Analysis
        print(f"\n📊 Analysis:")
        valid_results = [(name, size, ttft) for name, size, ttft in results if ttft is not None]
        if len(valid_results) >= 2:
            fastest = min(valid_results, key=lambda x: x[2])
            slowest = max(valid_results, key=lambda x: x[2])
            print(f"Fastest: {fastest[0]} ({fastest[1]} chars) - {fastest[2]:.0f}ms")
            print(f"Slowest: {slowest[0]} ({slowest[1]} chars) - {slowest[2]:.0f}ms")
            print(f"Performance Impact: {slowest[2]/fastest[2]:.1f}x slower with larger context")
        
        return results
    
    async def test_session_length_impact(self):
        """Test how conversation history length affects TTFT"""
        print("\n🗣️ Testing Session Length Impact on TTFT")
        print("=" * 50)
        
        base_messages = [
            {"content": "User: Hi Luna"},
            {"content": "Assistant: Hey there!"},
            {"content": "User: How's your day?"},
            {"content": "Assistant: Going well, thanks!"},
        ]
        
        session_lengths = [1, 3, 5, 8, 12, 16, 20]
        
        for length in session_lengths:
            # Build message history
            messages = []
            for i in range(length):
                messages.extend(base_messages)
            
            # Add current message
            messages.append({"content": "User: What should we talk about?"})
            
            try:
                start_time = time.monotonic()
                
                # Format messages for llama.cpp
                prompt = format_messages_for_llama_cpp(messages[-40:], include_system_prompt=True)  # Limit to prevent excessive context
                
                # Run inference in thread pool
                loop = asyncio.get_event_loop()
                
                def run_inference():
                    return self.llama_cpp_client(
                        prompt,
                        max_tokens=50,
                        stream=True
                    )
                
                response = await loop.run_in_executor(None, run_inference)
                
                # Get first token
                first_token_time = None
                for chunk in response:
                    token = extract_response_from_llama_cpp_result(chunk)
                    if token:
                        first_token_time = time.monotonic()
                        break
                
                ttft = (first_token_time - start_time) * 1000 if first_token_time else None
                total_chars = sum(len(msg['content']) for msg in messages)
                
                print(f"{length:2d} exchanges ({total_chars:4d} chars): {ttft:.0f}ms TTFT")
                
            except Exception as e:
                print(f"{length:2d} exchanges: ERROR - {e}")
            
            await asyncio.sleep(0.5)
    
    def print_optimization_recommendations(self):
        """Print optimization recommendations"""
        print("\n🚀 TTFT Optimization Recommendations")
        print("=" * 50)
        
        recommendations = [
            "1. **Reduce Context Size**",
            "   - Limit session messages to 6-8 (currently using 8)",
            "   - Reduce RAG context to 3-5 entries (currently using 5)",
            "   - Set MAX_CONTEXT_CHARS to 3000-4000 (currently 4000)",
            "",
            "2. **Optimize Model Configuration**",
            "   - Use quantized models (Q4_K_M, Q5_K_M) for speed",
            "   - Consider using a smaller fine-tuned model",
            "   - Set temperature lower (1.0-1.2) for faster generation",
            "",
            "3. **Hardware Optimizations**",
            "   - Ensure model fits entirely in GPU VRAM",
            "   - Use GPU acceleration for embeddings",
            "   - Monitor GPU utilization during inference",
            "",
            "4. **Context Management**",
            "   - Disable RAG for simple conversations",
            "   - Use lighter system prompts",
            "   - Implement smart context pruning",
            "",
            "5. **llama.cpp Specific Optimizations**",
            "   - Adjust n_threads based on your CPU cores",
            "   - Use GPU layers (n_gpu_layers) for GPU acceleration",
            "   - Enable memory mapping (use_mmap) for faster loading",
            "   - Use memory locking (use_mlock) to prevent swapping",
            "",
            "6. **Monitoring**",
            "   - Track context size per request",
            "   - Log TTFT metrics consistently",
            "   - Set alerts for TTFT > 200ms (much faster with direct llama.cpp)"
        ]
        
        for rec in recommendations:
            print(rec)
    
    async def run_full_benchmark(self):
        """Run comprehensive TTFT benchmark"""
        print("🔥 Luna TTFT Optimization Benchmark (llama.cpp Direct)")
        print("=" * 60)
        
        await self.initialize()
        await self.test_context_size_impact()
        await self.test_session_length_impact()
        self.print_optimization_recommendations()
        
        print(f"\n✅ Benchmark completed! Check your terminal output above.")
        print(f"Target TTFT for direct llama.cpp: < 200ms")
        print(f"Expected improvement: 5-10x faster than Ollama API calls")

async def main():
    optimizer = TTFTOptimizer()
    await optimizer.run_full_benchmark()

if __name__ == "__main__":
    asyncio.run(main()) 