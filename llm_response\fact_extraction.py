"""
Luna Memory Fact Extraction Pipeline
===================================

Intelligent memory fact extraction using <PERSON>'s dual-GPU setup:
- Qwen3 (AMD RX 6650XT) for fast fact extraction and entity recognition
- Gemma3 (NVIDIA RTX 4070) for memory consolidation and relationship mapping

This pipeline processes conversation chunks in background to avoid blocking responses.
"""

import asyncio
import json
import logging
import re
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Set, Tuple, Any
from collections import defaultdict

from .memory_system import MemoryFact, MemoryType
from .config import get_main_name_by_id, get_profile_by_id, USER_PROFILES
from .initialization import get_qwen3_client, get_gemma3_client
from shared_model import call_qwen3_safe, call_gemma3_safe

logger = logging.getLogger(__name__)

@dataclass
class ExtractionContext:
    """Context for fact extraction"""
    user_id: int
    channel_id: str
    channel_type: str  # 'voice', 'text', 'dm'
    conversation_history: List[Dict[str, Any]]
    current_message: Dict[str, Any]
    user_profile: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.user_profile is None:
            self.user_profile = get_profile_by_id(self.user_id)

class FactExtractor:
    """Advanced fact extraction using dual-GPU setup"""
    
    def __init__(self):
        self.entity_patterns = {
            'games': ['minecraft', 'pokemon', 'gaming', 'game', 'build', 'server'],
            'preferences': ['like', 'love', 'hate', 'prefer', 'favorite', 'favourite'],
            'social': ['friend', 'buddy', 'pal', 'knows', 'met', 'dating'],
            'food': ['potato', 'potatoes', 'food', 'eat', 'cooking', 'hungry'],
            'personality': ['funny', 'smart', 'kind', 'annoying', 'cool', 'weird'],
            'activities': ['playing', 'building', 'working', 'studying', 'sleeping']
        }
        
        self.relationship_patterns = [
            r"(\w+) (?:is|are) friends? with (\w+)",
            r"(\w+) (?:likes|loves) (\w+)",
            r"(\w+) (?:knows|met) (\w+)",
            r"(\w+) and (\w+) (?:are|were) (?:playing|talking|hanging out)"
        ]
    
    async def extract_facts_from_conversation(self, context: ExtractionContext) -> List[MemoryFact]:
        """Main extraction pipeline using dual-GPU approach"""
        start_time = time.time()
        
        try:
            # Phase 1: Fast extraction using Qwen3 (AMD GPU)
            raw_facts = await self._extract_raw_facts_qwen3(context)
            
            # Phase 2: Consolidation and enhancement using Gemma3 (NVIDIA GPU)  
            consolidated_facts = await self._consolidate_facts_gemma3(raw_facts, context)
            
            # Phase 3: Entity and relationship extraction
            enhanced_facts = await self._extract_entities_and_relationships(consolidated_facts, context)
            
            extraction_time = (time.time() - start_time) * 1000
            logger.debug(f"🧠 Extracted {len(enhanced_facts)} facts in {extraction_time:.1f}ms")
            
            return enhanced_facts
            
        except Exception as e:
            logger.error(f"Error in fact extraction pipeline: {e}", exc_info=True)
            return []
    
    async def _extract_raw_facts_qwen3(self, context: ExtractionContext) -> List[str]:
        """Phase 1: Fast fact extraction using Qwen3 on AMD GPU"""
        try:
            # Prepare conversation context
            conversation_text = self._format_conversation_for_extraction(context)
            
            # Discord-specific extraction prompt optimized for speed
            prompt = f"""Extract key facts from this Discord conversation that Luna should remember. Focus on:

1. User preferences and personality traits
2. Gaming context (especially Minecraft)
3. Relationships between users
4. Behavioral patterns
5. Voice vs text context differences

Conversation:
{conversation_text}

User Profile: {context.user_profile.get('main_name', 'Unknown')} (ID: {context.user_id})
Channel: {context.channel_type} in {context.channel_id}

Extract ONLY clear, important facts in this format:
FACT: [TYPE] content
Where TYPE is: PREFERENCE, BEHAVIOR, RELATIONSHIP, GAMING, PERSONALITY, VOICE_CONTEXT

Examples:
FACT: [PREFERENCE] User loves building castles in Minecraft
FACT: [BEHAVIOR] User is more talkative in voice chat than text
FACT: [RELATIONSHIP] User is good friends with Tachi
FACT: [GAMING] User plays on the Luna Minecraft server regularly

Be concise and specific. Only extract facts that would be useful for future conversations."""

            qwen3_client = get_qwen3_client()
            if not qwen3_client:
                logger.warning("Qwen3 client not available for fact extraction")
                return []
            
            # Fast extraction call
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: call_qwen3_safe(
                    qwen3_client,
                    prompt,
                    max_tokens=300,
                    temperature=0.2,  # Low temperature for consistent extraction
                    stop=["\\n\\n", "User:", "Assistant:"]
                )
            )
            
            if isinstance(response, dict):
                response_text = response.get('choices', [{}])[0].get('text', '')
            else:
                response_text = str(response)
            
            # Parse extracted facts
            facts = []
            for line in response_text.split('\\n'):
                line = line.strip()
                if line.startswith('FACT:'):
                    fact_content = line[5:].strip()
                    if fact_content:
                        facts.append(fact_content)
            
            logger.debug(f"🔍 Qwen3 extracted {len(facts)} raw facts")
            return facts
            
        except Exception as e:
            logger.error(f"Error in Qwen3 fact extraction: {e}", exc_info=True)
            return []
    
    async def _consolidate_facts_gemma3(self, raw_facts: List[str], 
                                       context: ExtractionContext) -> List[MemoryFact]:
        """Phase 2: Consolidate and validate facts using Gemma3 on NVIDIA GPU"""
        if not raw_facts:
            return []
        
        try:
            # Prepare consolidation prompt
            facts_text = "\\n".join([f"- {fact}" for fact in raw_facts])
            
            prompt = f"""Review and consolidate these extracted facts about user {context.user_profile.get('main_name', 'Unknown')}:

{facts_text}

Your task:
1. Remove duplicate or contradictory facts
2. Improve fact clarity and specificity  
3. Assign confidence scores (0.0-1.0)
4. Classify each fact properly

Output format:
CONSOLIDATED_FACT: [TYPE] content | confidence: X.X

Types: FACTUAL, BEHAVIORAL, RELATIONSHIP, GAMING, VOICE_CONTEXT, EPISODIC, SEMANTIC

Examples:
CONSOLIDATED_FACT: [FACTUAL] User prefers building medieval castles in Minecraft | confidence: 0.9
CONSOLIDATED_FACT: [BEHAVIORAL] User is more active during evening hours | confidence: 0.7
CONSOLIDATED_FACT: [RELATIONSHIP] User collaborates frequently with Tachi on builds | confidence: 0.8

Be selective - only keep high-quality, useful facts."""

            gemma3_client = get_gemma3_client()
            if not gemma3_client:
                logger.warning("Gemma3 client not available for fact consolidation")
                # Fallback: convert raw facts to MemoryFacts with default values
                return self._fallback_fact_conversion(raw_facts, context)
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: call_gemma3_safe(
                    gemma3_client,
                    prompt,
                    max_tokens=400,
                    temperature=0.1,  # Very low temperature for consistency
                )
            )
            
            if isinstance(response, dict):
                response_text = response.get('choices', [{}])[0].get('text', '')
            else:
                response_text = str(response)
            
            # Parse consolidated facts
            consolidated_facts = []
            for line in response_text.split('\\n'):
                line = line.strip()
                if line.startswith('CONSOLIDATED_FACT:'):
                    fact = self._parse_consolidated_fact(line, context)
                    if fact:
                        consolidated_facts.append(fact)
            
            logger.debug(f"🔧 Gemma3 consolidated to {len(consolidated_facts)} facts")
            return consolidated_facts
            
        except Exception as e:
            logger.error(f"Error in Gemma3 fact consolidation: {e}", exc_info=True)
            return self._fallback_fact_conversion(raw_facts, context)
    
    def _parse_consolidated_fact(self, line: str, context: ExtractionContext) -> Optional[MemoryFact]:
        """Parse a consolidated fact line"""
        try:
            # Format: CONSOLIDATED_FACT: [TYPE] content | confidence: X.X
            content_part = line.split('CONSOLIDATED_FACT:', 1)[1].strip()
            
            if '|' not in content_part:
                return None
            
            fact_part, confidence_part = content_part.split('|', 1)
            
            # Extract type and content
            if not fact_part.strip().startswith('['):
                return None
            
            type_str = fact_part.split('[')[1].split(']')[0].upper()
            content = fact_part.split(']', 1)[1].strip()
            
            # Extract confidence
            confidence = 0.8  # Default
            if 'confidence:' in confidence_part:
                try:
                    conf_str = confidence_part.split('confidence:')[1].strip()
                    confidence = float(conf_str)
                except (ValueError, IndexError):
                    pass
            
            # Map type to MemoryType
            memory_type = MemoryType.FACTUAL  # Default
            type_mapping = {
                'FACTUAL': MemoryType.FACTUAL,
                'BEHAVIORAL': MemoryType.BEHAVIORAL, 
                'RELATIONSHIP': MemoryType.RELATIONSHIP,
                'GAMING': MemoryType.GAMING,
                'VOICE_CONTEXT': MemoryType.VOICE_CONTEXT,
                'EPISODIC': MemoryType.EPISODIC,
                'SEMANTIC': MemoryType.SEMANTIC
            }
            
            memory_type = type_mapping.get(type_str, MemoryType.FACTUAL)
            
            return MemoryFact(
                content=content,
                memory_type=memory_type,
                user_id=context.user_id,
                channel_id=context.channel_id,
                confidence=confidence,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.debug(f"Failed to parse consolidated fact: {line} - {e}")
            return None
    
    def _fallback_fact_conversion(self, raw_facts: List[str], 
                                 context: ExtractionContext) -> List[MemoryFact]:
        """Fallback conversion when Gemma3 is not available"""
        facts = []
        current_time = time.time()
        
        for raw_fact in raw_facts:
            try:
                # Basic parsing of [TYPE] content format
                if '[' not in raw_fact or ']' not in raw_fact:
                    continue
                
                type_str = raw_fact.split('[')[1].split(']')[0].upper()
                content = raw_fact.split(']', 1)[1].strip()
                
                # Map to memory type
                memory_type = MemoryType.FACTUAL
                if 'BEHAVIOR' in type_str:
                    memory_type = MemoryType.BEHAVIORAL
                elif 'RELATIONSHIP' in type_str:
                    memory_type = MemoryType.RELATIONSHIP
                elif 'GAMING' in type_str:
                    memory_type = MemoryType.GAMING
                elif 'VOICE' in type_str:
                    memory_type = MemoryType.VOICE_CONTEXT
                
                fact = MemoryFact(
                    content=content,
                    memory_type=memory_type,
                    user_id=context.user_id,
                    channel_id=context.channel_id,
                    confidence=0.7,  # Default confidence for fallback
                    timestamp=current_time
                )
                facts.append(fact)
                
            except Exception as e:
                logger.debug(f"Failed to convert raw fact: {raw_fact} - {e}")
                continue
        
        return facts
    
    async def _extract_entities_and_relationships(self, facts: List[MemoryFact], 
                                                 context: ExtractionContext) -> List[MemoryFact]:
        """Phase 3: Extract entities and relationships from facts"""
        for fact in facts:
            # Extract entities using pattern matching
            fact.entities = self._extract_entities_from_text(fact.content, context)
            
            # Extract relationships
            fact.relationships = self._extract_relationships_from_text(fact.content, context)
        
        return facts
    
    def _extract_entities_from_text(self, text: str, context: ExtractionContext) -> List[str]:
        """Extract entities from text using pattern matching"""
        entities = set()
        text_lower = text.lower()
        
        # Extract known entities from patterns
        for category, patterns in self.entity_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    entities.add(pattern)
        
        # Extract user names from profiles
        for user_id, profile in USER_PROFILES.items():
            main_name = profile.get('main_name', '').lower()
            aliases = profile.get('aliases', [])
            
            if main_name and main_name in text_lower:
                entities.add(profile.get('main_name'))
            
            for alias in aliases:
                if alias.lower() in text_lower:
                    entities.add(profile.get('main_name'))
        
        # Extract Discord-specific entities
        discord_entities = ['discord', 'voice chat', 'text chat', 'dm', 'server', 'channel']
        for entity in discord_entities:
            if entity in text_lower:
                entities.add(entity)
        
        return list(entities)
    
    def _extract_relationships_from_text(self, text: str, context: ExtractionContext) -> List[Tuple[str, str, str]]:
        """Extract relationships from text using regex patterns"""
        relationships = []
        
        for pattern in self.relationship_patterns:
            matches = re.finditer(pattern, text.lower())
            for match in matches:
                if len(match.groups()) >= 2:
                    entity1 = match.group(1)
                    entity2 = match.group(2)
                    
                    # Determine relationship type from pattern
                    relation = "knows"  # Default
                    if "friend" in pattern:
                        relation = "is_friend_of"
                    elif "likes" in pattern or "loves" in pattern:
                        relation = "likes"
                    elif "playing" in pattern:
                        relation = "plays_with"
                    
                    relationships.append((entity1, relation, entity2))
        
        return relationships
    
    def _format_conversation_for_extraction(self, context: ExtractionContext) -> str:
        """Format conversation history for extraction"""
        lines = []
        
        # Add recent context (last 5 messages)
        recent_messages = context.conversation_history[-5:] if context.conversation_history else []
        
        for msg in recent_messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            user_id = msg.get('user_id')
            
            if user_id and role == 'user':
                user_name = get_main_name_by_id(user_id) or f"User{user_id}"
                lines.append(f"{user_name}: {content}")
            elif role == 'assistant':
                lines.append(f"Luna: {content}")
        
        # Add current message
        current_content = context.current_message.get('content', '')
        if current_content:
            user_name = context.user_profile.get('main_name', f"User{context.user_id}")
            lines.append(f"{user_name}: {current_content}")
        
        return "\\n".join(lines)

# Factory function for easy access
def create_fact_extractor() -> FactExtractor:
    """Create a new fact extractor instance"""
    return FactExtractor()

# Global instance for reuse
_fact_extractor = None

def get_fact_extractor() -> FactExtractor:
    """Get the global fact extractor instance"""
    global _fact_extractor
    if _fact_extractor is None:
        _fact_extractor = FactExtractor()
    return _fact_extractor