#!/usr/bin/env python3
"""
Test Query Strategies
====================
Tests different approaches to find Luna's facts more effectively
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def test_query_strategies():
    print("🧪 Testing Query Strategies")
    print("=" * 50)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    # Connect directly to database
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        print("🔍 Testing different query approaches for 'luna favorite food':")
        
        # Strategy 1: Current AND logic
        print("\n1. Current AND logic: 'luna AND favorite AND food'")
        cursor = conn.execute("""
            SELECT mf.rowid, mf.content, mf.confidence 
            FROM memory_facts mf
            WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
            ORDER BY mf.confidence DESC
            LIMIT 5
        """, ("\"luna\" AND \"favorite\" AND \"food\"",))
        results = cursor.fetchall()
        print(f"   Results: {len(results)}")
        for result in results:
            print(f"     {result[1][:80]}...")
        
        # Strategy 2: Luna AND food (without "favorite")  
        print("\n2. Luna AND food: 'luna AND food'")
        cursor = conn.execute("""
            SELECT mf.rowid, mf.content, mf.confidence 
            FROM memory_facts mf
            WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
            ORDER BY mf.confidence DESC
            LIMIT 5
        """, ("\"luna\" AND \"food\"",))
        results = cursor.fetchall()
        print(f"   Results: {len(results)}")
        for result in results:
            print(f"     {result[1][:80]}...")
        
        # Strategy 3: Luna AND potatoes
        print("\n3. Luna AND potatoes: 'luna AND potatoes'")
        cursor = conn.execute("""
            SELECT mf.rowid, mf.content, mf.confidence 
            FROM memory_facts mf
            WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
            ORDER BY mf.confidence DESC
            LIMIT 5
        """, ("\"luna\" AND \"potatoes\"",))
        results = cursor.fetchall()
        print(f"   Results: {len(results)}")
        for result in results:
            print(f"     {result[1][:80]}...")
        
        # Strategy 4: Fallback with OR if AND returns no results
        print("\n4. Fallback OR logic: 'luna OR favorite OR food'")
        cursor = conn.execute("""
            SELECT mf.rowid, mf.content, mf.confidence 
            FROM memory_facts mf
            WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
            ORDER BY mf.confidence DESC
            LIMIT 5
        """, ("\"luna\" OR \"favorite\" OR \"food\"",))
        results = cursor.fetchall()
        print(f"   Results: {len(results)}")
        for result in results[:3]:
            print(f"     {result[1][:80]}...")
        
        # Strategy 5: Just "potatoes" 
        print("\n5. Single term 'potatoes':")
        cursor = conn.execute("""
            SELECT mf.rowid, mf.content, mf.confidence 
            FROM memory_facts mf
            WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
            ORDER BY mf.confidence DESC
            LIMIT 5
        """, ("\"potatoes\"",))
        results = cursor.fetchall()
        print(f"   Results: {len(results)}")
        for result in results[:3]:
            print(f"     {result[1][:80]}...")
        
        print(f"\n🔍 Testing 'your favorite' queries:")
        
        # Test "your" keyword
        print("\n6. Query 'your AND favorite':")
        cursor = conn.execute("""
            SELECT mf.rowid, mf.content, mf.confidence 
            FROM memory_facts mf
            WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
            ORDER BY mf.confidence DESC
            LIMIT 5
        """, ("\"your\" AND \"favorite\"",))
        results = cursor.fetchall()
        print(f"   Results: {len(results)}")
        for result in results:
            print(f"     {result[1][:80]}...")
            
        # Check what facts contain "your"
        print("\n7. All facts containing 'your':")
        cursor = conn.execute("SELECT content FROM memory_facts WHERE content LIKE '%your%' LIMIT 3")
        results = cursor.fetchall()
        for result in results:
            print(f"     {result[0]}")
    
    finally:
        conn.close()
    
    print(f"\n💡 Recommendations:")
    print(f"   - Use AND for precise matching when available")
    print(f"   - Fallback to individual terms if AND returns no results")
    print(f"   - Map 'food' queries to also search for 'loves', 'likes', 'eats'")
    print(f"   - Handle 'your' by converting to 'luna' for fact matching")

if __name__ == "__main__":
    asyncio.run(test_query_strategies())