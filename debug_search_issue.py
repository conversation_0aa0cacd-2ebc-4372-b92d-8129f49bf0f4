#!/usr/bin/env python3
"""
Debug Search Issue
==================
Tests why manually stored facts aren't being retrieved
"""
import asyncio
import sqlite3
import time
from llm_response.memory_system import get_memory_system, initialize_memory_system, MemoryFact, MemoryType

async def debug_search_issue():
    print("🔍 Debugging Search/Retrieval Issue")
    print("=" * 45)
    
    # Initialize memory system
    await initialize_memory_system()
    ms = get_memory_system()
    
    user_id = 921637353364287489
    channel_id = "1378098310036066335"
    
    # Store a test fact
    print("1️⃣ Storing a test fact:")
    test_fact = MemoryFact(
        content="User said their favorite color is blue",
        memory_type=MemoryType.FACTUAL,
        user_id=user_id,
        channel_id=channel_id,
        confidence=0.9,
        timestamp=time.time(),
        entities=["blue", "color", "favorite"],
        relationships=[("user", "prefers", "blue")]
    )
    
    fact_id = ms.warm_storage.store_fact(test_fact)
    print(f"   ✅ Stored fact with ID: {fact_id}")
    print(f"   Content: '{test_fact.content}'")
    print(f"   User ID: {test_fact.user_id}")
    
    # Check if it's in the database
    print(f"\n2️⃣ Checking database directly:")
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        # Check if fact exists
        cursor = conn.execute("SELECT id, content, user_id FROM memory_facts WHERE id = ?", (fact_id,))
        result = cursor.fetchone()
        if result:
            print(f"   ✅ Fact found in database: {result}")
        else:
            print(f"   ❌ Fact not found in database")
            return
        
        # Check FTS index
        print(f"\n3️⃣ Checking FTS index:")
        
        # First, rebuild FTS index to make sure it's current
        try:
            cursor.execute("INSERT INTO memory_facts_fts(memory_facts_fts) VALUES('rebuild')")
            conn.commit()
            print(f"   ✅ FTS index rebuilt")
        except Exception as e:
            print(f"   ⚠️ FTS rebuild warning: {e}")
        
        # Test FTS search for different terms
        test_terms = ['blue', 'color', 'favorite', 'user']
        for term in test_terms:
            try:
                cursor = conn.execute("""
                    SELECT mf.id, mf.content, mf.user_id
                    FROM memory_facts mf
                    WHERE mf.id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                    ORDER BY mf.id DESC
                    LIMIT 3
                """, (f'"{term}"',))
                
                results = cursor.fetchall()
                print(f"   FTS '{term}': {len(results)} results")
                for res in results:
                    if res[0] == fact_id:
                        print(f"     ✅ Found our fact: {res[1]}")
                        break
                else:
                    if results:
                        print(f"     ❌ Our fact not in results (found other facts)")
                    else:
                        print(f"     ❌ No results at all")
            except Exception as e:
                print(f"   ❌ FTS error for '{term}': {e}")
    
    finally:
        conn.close()
    
    # Test the memory system search
    print(f"\n4️⃣ Testing memory system search:")
    
    test_queries = [
        ("blue", "Should find blue color fact"),
        ("color", "Should find color preference fact"),
        ("blue color", "Should find blue color fact"),
        ("favorite", "Should find favorite preference"),
        ("user favorite color", "Should find user color preference")
    ]
    
    for query, expected in test_queries:
        print(f"\n   Query: '{query}' ({expected})")
        
        # Test with user filter
        facts_with_user = ms.warm_storage.search_facts(
            query=query,
            user_id=user_id,
            limit=5
        )
        print(f"     With user filter: {len(facts_with_user)} facts")
        for fact in facts_with_user:
            if fact.content == test_fact.content:
                print(f"       ✅ Found our test fact!")
                break
        else:
            if facts_with_user:
                print(f"       ❌ Found other facts but not our test fact")
                for fact in facts_with_user[:2]:
                    print(f"         - {fact.content}")
            else:
                print(f"       ❌ No facts found with user filter")
        
        # Test without user filter
        facts_no_user = ms.warm_storage.search_facts(
            query=query,
            user_id=None,
            limit=5
        )
        print(f"     Without user filter: {len(facts_no_user)} facts")
        for fact in facts_no_user:
            if fact.content == test_fact.content:
                print(f"       ✅ Found our test fact!")
                break
        else:
            if facts_no_user:
                print(f"       ❌ Found other facts but not our test fact")
            else:
                print(f"       ❌ No facts found without user filter")
    
    # Test high-level memory retrieval
    print(f"\n5️⃣ Testing high-level memory retrieval:")
    
    context = await ms.retrieve_relevant_context(
        query="blue color favorite",
        user_id=user_id,
        channel_id=None,
        max_facts=5
    )
    
    if context:
        print(f"   ✅ Context retrieved: {len(context)} chars")
        if test_fact.content in context:
            print(f"     ✅ Our test fact found in context!")
        else:
            print(f"     ❌ Our test fact not in context")
        print(f"   Context preview: {context[:100]}...")
    else:
        print(f"   ❌ No context retrieved")

if __name__ == "__main__":
    asyncio.run(debug_search_issue())