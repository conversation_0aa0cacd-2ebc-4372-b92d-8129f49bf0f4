# Audio dB Level Filtering for Speech-to-Text

This document explains the new dB level filtering feature added to the speech-to-text system to filter out low-level background noise.

## Overview

The dB filtering feature automatically detects and filters out audio that falls below a configurable decibel (dB) threshold. This helps prevent the speech-to-text system from processing background noise, keyboard clicks, fan noise, and other low-level audio that shouldn't be transcribed.

## How It Works

1. **Audio Level Calculation**: The system calculates the RMS (Root Mean Square) level of incoming audio and converts it to decibels (dB)
2. **Threshold Comparison**: The calculated dB level is compared against a configurable threshold
3. **Filtering Decision**: Audio below the threshold is filtered out and not sent to the transcription engine
4. **Processing Savings**: By filtering early in the preprocessing stage, we save computational resources

## Configuration

### Default Settings

- **Default Threshold**: -40.0 dB
- **Reference Level**: Full scale (1.0 = 0 dB for normalized audio)

### Adjusting the Threshold

You can adjust the dB threshold using the provided functions:

```python
from speech_to_text import set_db_threshold, get_db_threshold

# Set a new threshold (more sensitive - filters less)
set_db_threshold(-35.0)

# Set a higher threshold (less sensitive - filters more)
set_db_threshold(-45.0)

# Check current threshold
current_threshold = get_db_threshold()
print(f"Current threshold: {current_threshold} dB")
```

## dB Level Reference

Here's a rough guide to typical audio levels:

| dB Level | Description | Filtering Behavior |
|----------|-------------|-------------------|
| 0 dB | Maximum possible level | Always processed |
| -10 dB | Very loud speech/shouting | Always processed |
| -20 dB | Normal loud speech | Always processed |
| -30 dB | Normal conversation | Processed (above default threshold) |
| -40 dB | Quiet speech | At default threshold boundary |
| -50 dB | Very quiet speech/whisper | Filtered out (below default) |
| -60 dB | Background noise | Filtered out |
| -70 dB+ | Very low background noise | Filtered out |

## Logging

The system provides detailed logging to help you understand the filtering behavior:

### Debug Logs
- `🔊 Audio passed dB filter: -32.1 dB >= -40.0 dB threshold (450ms)` - Audio passed filtering
- Detailed timing information for preprocessing steps

### Info Logs
- `🔇 Audio filtered out: -45.2 dB < -40.0 dB threshold (320ms)` - Audio was filtered out
- Threshold adjustment notifications

## Testing

A test script is provided to help you understand and verify the filtering behavior:

```bash
python test_db_filtering.py
```

This script will:
1. Test dB calculation with known amplitude levels
2. Test filtering with various audio levels
3. Demonstrate threshold adjustment effects

## Tuning Recommendations

### For Noisy Environments
- **Lower threshold** (e.g., -35 dB): Filters out more background noise but might miss very quiet speech
- Good for environments with fans, air conditioning, or keyboard noise

### For Quiet Environments
- **Higher threshold** (e.g., -45 dB): More sensitive, captures quieter speech
- Good for quiet rooms or when users speak softly

### For Mixed Environments
- **Default threshold** (-40 dB): Balanced approach that works well in most situations
- Start here and adjust based on your specific needs

## Performance Impact

The dB filtering adds minimal computational overhead:
- **Calculation**: Simple RMS calculation on audio samples
- **Early Filtering**: Prevents expensive transcription processing for noise
- **Net Benefit**: Usually improves overall performance by reducing unnecessary transcription calls

## Integration

The dB filtering is automatically integrated into the existing speech-to-text pipeline:

1. Audio data arrives from Discord
2. **NEW**: dB level is calculated and checked against threshold
3. If audio passes, it continues to resampling and transcription
4. If audio fails, it's filtered out early (empty array returned)

No changes are needed to existing code - the filtering happens transparently in the preprocessing stage.

## Troubleshooting

### Audio Being Filtered Too Aggressively
- **Solution**: Lower the threshold (e.g., from -40 to -45 dB)
- **Check**: Look for `🔇 Audio filtered out` messages in logs

### Too Much Background Noise Getting Through
- **Solution**: Raise the threshold (e.g., from -40 to -35 dB)
- **Check**: Monitor transcription quality and false positives

### Inconsistent Filtering
- **Cause**: Audio levels vary significantly between users/microphones
- **Solution**: Consider per-user threshold adjustment or adaptive thresholds

## Future Enhancements

Potential improvements that could be added:
- Per-user threshold settings
- Adaptive threshold based on recent audio history
- Frequency-based filtering (not just amplitude)
- Integration with VAD (Voice Activity Detection) for smarter filtering
